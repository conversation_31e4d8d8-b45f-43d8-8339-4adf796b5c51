{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useApp } from '../contexts/AppContext';\nimport DebugPanel from './DebugPanel';\nimport { HomeIcon, MapPinIcon, ClipboardDocumentListIcon, CalendarDaysIcon, QueueListIcon, UserIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon, Bars3Icon, XMarkIcon, BellIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  _s();\n  var _user$name;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\n  const {\n    user,\n    logout,\n    isAdmin\n  } = useAuth();\n  const {\n    queuePosition\n  } = useApp();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const navigation = [{\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: HomeIcon\n  }, {\n    name: 'Locations',\n    href: '/locations',\n    icon: MapPinIcon\n  }, {\n    name: 'Services',\n    href: '/services',\n    icon: ClipboardDocumentListIcon\n  }, {\n    name: 'Appointments',\n    href: '/appointments',\n    icon: CalendarDaysIcon\n  }, {\n    name: 'Queue',\n    href: '/queue',\n    icon: QueueListIcon\n  }, {\n    name: 'Profile',\n    href: '/profile',\n    icon: UserIcon\n  }];\n  if (isAdmin) {\n    navigation.push({\n      name: 'Admin',\n      href: '/admin',\n      icon: Cog6ToothIcon\n    });\n  }\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: sidebarOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          className: \"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\",\n          onClick: () => setSidebarOpen(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            x: -300\n          },\n          animate: {\n            x: 0\n          },\n          exit: {\n            x: -300\n          },\n          transition: {\n            type: \"spring\",\n            damping: 25,\n            stiffness: 200\n          },\n          className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-16 px-4 border-b border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-primary-600\",\n              children: \"Umurongo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSidebarOpen(false),\n              className: \"p-2 rounded-md text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"mt-5 px-2\",\n            children: navigation.map(item => {\n              const isActive = location.pathname === item.href;\n              return /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                onClick: () => setSidebarOpen(false),\n                className: `group flex items-center px-2 py-2 text-base font-medium rounded-md mb-1 ${isActive ? 'bg-primary-100 text-primary-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: `mr-4 h-6 w-6 ${isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 23\n                }, this), item.name]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center flex-shrink-0 px-4\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-primary-600\",\n            children: \"Umurongo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"mt-8 flex-1 px-2 space-y-1\",\n          children: navigation.map(item => {\n            const isActive = location.pathname === item.href;\n            return /*#__PURE__*/_jsxDEV(Link, {\n              to: item.href,\n              className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md ${isActive ? 'bg-primary-100 text-primary-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n              children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                className: `mr-3 h-6 w-6 ${isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), item.name, item.name === 'Queue' && queuePosition && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-auto bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full\",\n                children: [\"#\", queuePosition.position]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 21\n              }, this)]\n            }, item.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:pl-64 flex flex-col flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between h-16 px-4 sm:px-6 lg:px-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSidebarOpen(true),\n              className: \"p-2 rounded-md text-gray-400 hover:text-gray-600 lg:hidden\",\n              children: /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 text-gray-400 hover:text-gray-600 relative\",\n              children: [/*#__PURE__*/_jsxDEV(BellIcon, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), queuePosition && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\",\n                children: \"!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setProfileDropdownOpen(!profileDropdownOpen),\n                className: \"flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hidden md:block text-sm font-medium text-gray-700\",\n                  children: user === null || user === void 0 ? void 0 : user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(ChevronDownIcon, {\n                  className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                children: profileDropdownOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    scale: 0.95\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  exit: {\n                    opacity: 0,\n                    scale: 0.95\n                  },\n                  className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"py-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/profile\",\n                      className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                      onClick: () => setProfileDropdownOpen(false),\n                      children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n                        className: \"w-4 h-4 mr-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 199,\n                        columnNumber: 27\n                      }, this), \"Profile\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleLogout,\n                      className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                      children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                        className: \"w-4 h-4 mr-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 27\n                      }, this), \"Sign out\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(DebugPanel, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 50\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"U6aQzLlivTmg5JllsiFmd04kUn4=\", false, function () {\n  return [useAuth, useApp, useLocation, useNavigate];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useNavigate", "motion", "AnimatePresence", "useAuth", "useApp", "DebugPanel", "HomeIcon", "MapPinIcon", "ClipboardDocumentListIcon", "CalendarDaysIcon", "QueueListIcon", "UserIcon", "Cog6ToothIcon", "ArrowRightOnRectangleIcon", "Bars3Icon", "XMarkIcon", "BellIcon", "ChevronDownIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Layout", "children", "_s", "_user$name", "sidebarOpen", "setSidebarOpen", "profileDropdownOpen", "setProfileDropdownOpen", "user", "logout", "isAdmin", "queuePosition", "location", "navigate", "navigation", "name", "href", "icon", "push", "handleLogout", "className", "div", "initial", "opacity", "animate", "exit", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "transition", "type", "damping", "stiffness", "map", "item", "isActive", "pathname", "to", "position", "char<PERSON>t", "toUpperCase", "scale", "process", "env", "NODE_ENV", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useApp } from '../contexts/AppContext';\nimport DebugPanel from './DebugPanel';\nimport {\n  HomeIcon,\n  MapPinIcon,\n  ClipboardDocumentListIcon,\n  CalendarDaysIcon,\n  QueueListIcon,\n  UserIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\n\nconst Layout = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\n  const { user, logout, isAdmin } = useAuth();\n  const { queuePosition } = useApp();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n    { name: 'Locations', href: '/locations', icon: MapPinIcon },\n    { name: 'Services', href: '/services', icon: ClipboardDocumentListIcon },\n    { name: 'Appointments', href: '/appointments', icon: CalendarDaysIcon },\n    { name: 'Queue', href: '/queue', icon: QueueListIcon },\n    { name: 'Profile', href: '/profile', icon: UserIcon },\n  ];\n\n  if (isAdmin) {\n    navigation.push({ name: 'Admin', href: '/admin', icon: Cog6ToothIcon });\n  }\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <>\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\"\n              onClick={() => setSidebarOpen(false)}\n            />\n            <motion.div\n              initial={{ x: -300 }}\n              animate={{ x: 0 }}\n              exit={{ x: -300 }}\n              transition={{ type: \"spring\", damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden\"\n            >\n              <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n                <h1 className=\"text-xl font-bold text-primary-600\">Umurongo</h1>\n                <button\n                  onClick={() => setSidebarOpen(false)}\n                  className=\"p-2 rounded-md text-gray-400 hover:text-gray-600\"\n                >\n                  <XMarkIcon className=\"w-6 h-6\" />\n                </button>\n              </div>\n              <nav className=\"mt-5 px-2\">\n                {navigation.map((item) => {\n                  const isActive = location.pathname === item.href;\n                  return (\n                    <Link\n                      key={item.name}\n                      to={item.href}\n                      onClick={() => setSidebarOpen(false)}\n                      className={`group flex items-center px-2 py-2 text-base font-medium rounded-md mb-1 ${\n                        isActive\n                          ? 'bg-primary-100 text-primary-900'\n                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                      }`}\n                    >\n                      <item.icon\n                        className={`mr-4 h-6 w-6 ${\n                          isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'\n                        }`}\n                      />\n                      {item.name}\n                    </Link>\n                  );\n                })}\n              </nav>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <h1 className=\"text-2xl font-bold text-primary-600\">Umurongo</h1>\n          </div>\n          <nav className=\"mt-8 flex-1 px-2 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = location.pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                    isActive\n                      ? 'bg-primary-100 text-primary-900'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <item.icon\n                    className={`mr-3 h-6 w-6 ${\n                      isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'\n                    }`}\n                  />\n                  {item.name}\n                  {item.name === 'Queue' && queuePosition && (\n                    <span className=\"ml-auto bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full\">\n                      #{queuePosition.position}\n                    </span>\n                  )}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex flex-col flex-1\">\n        {/* Top navigation */}\n        <div className=\"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex justify-between h-16 px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 lg:hidden\"\n              >\n                <Bars3Icon className=\"w-6 h-6\" />\n              </button>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {/* Notifications */}\n              <button className=\"p-2 text-gray-400 hover:text-gray-600 relative\">\n                <BellIcon className=\"w-6 h-6\" />\n                {queuePosition && (\n                  <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                    !\n                  </span>\n                )}\n              </button>\n\n              {/* Profile dropdown */}\n              <div className=\"relative\">\n                <button\n                  onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\n                  className=\"flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50\"\n                >\n                  <div className=\"w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-sm font-medium\">\n                      {user?.name?.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                  <span className=\"hidden md:block text-sm font-medium text-gray-700\">\n                    {user?.name}\n                  </span>\n                  <ChevronDownIcon className=\"w-4 h-4 text-gray-400\" />\n                </button>\n\n                <AnimatePresence>\n                  {profileDropdownOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, scale: 0.95 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      exit={{ opacity: 0, scale: 0.95 }}\n                      className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5\"\n                    >\n                      <div className=\"py-1\">\n                        <Link\n                          to=\"/profile\"\n                          className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                          onClick={() => setProfileDropdownOpen(false)}\n                        >\n                          <UserIcon className=\"w-4 h-4 mr-3\" />\n                          Profile\n                        </Link>\n                        <button\n                          onClick={handleLogout}\n                          className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                        >\n                          <ArrowRightOnRectangleIcon className=\"w-4 h-4 mr-3\" />\n                          Sign out\n                        </button>\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n\n      {/* Debug Panel (only in development) */}\n      {process.env.NODE_ENV === 'development' && <DebugPanel />}\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,SACEC,QAAQ,EACRC,UAAU,EACVC,yBAAyB,EACzBC,gBAAgB,EAChBC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,yBAAyB,EACzBC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,eAAe,QACV,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAEiC,IAAI;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAE8B;EAAc,CAAC,GAAG7B,MAAM,CAAC,CAAC;EAClC,MAAM8B,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAE9B,MAAMoC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAEjC;EAAS,CAAC,EACzD;IAAE+B,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAEhC;EAAW,CAAC,EAC3D;IAAE8B,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE/B;EAA0B,CAAC,EACxE;IAAE6B,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,eAAe;IAAEC,IAAI,EAAE9B;EAAiB,CAAC,EACvE;IAAE4B,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE7B;EAAc,CAAC,EACtD;IAAE2B,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE5B;EAAS,CAAC,CACtD;EAED,IAAIqB,OAAO,EAAE;IACXI,UAAU,CAACI,IAAI,CAAC;MAAEH,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE3B;IAAc,CAAC,CAAC;EACzE;EAEA,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzBV,MAAM,CAAC,CAAC;IACRI,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACEhB,OAAA;IAAKuB,SAAS,EAAC,yBAAyB;IAAAnB,QAAA,gBAEtCJ,OAAA,CAACjB,eAAe;MAAAqB,QAAA,EACbG,WAAW,iBACVP,OAAA,CAAAE,SAAA;QAAAE,QAAA,gBACEJ,OAAA,CAAClB,MAAM,CAAC0C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBE,IAAI,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACrBH,SAAS,EAAC,wDAAwD;UAClEM,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACFjC,OAAA,CAAClB,MAAM,CAAC0C,GAAG;UACTC,OAAO,EAAE;YAAES,CAAC,EAAE,CAAC;UAAI,CAAE;UACrBP,OAAO,EAAE;YAAEO,CAAC,EAAE;UAAE,CAAE;UAClBN,IAAI,EAAE;YAAEM,CAAC,EAAE,CAAC;UAAI,CAAE;UAClBC,UAAU,EAAE;YAAEC,IAAI,EAAE,QAAQ;YAAEC,OAAO,EAAE,EAAE;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC5Df,SAAS,EAAC,+DAA+D;UAAAnB,QAAA,gBAEzEJ,OAAA;YAAKuB,SAAS,EAAC,sEAAsE;YAAAnB,QAAA,gBACnFJ,OAAA;cAAIuB,SAAS,EAAC,oCAAoC;cAAAnB,QAAA,EAAC;YAAQ;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEjC,OAAA;cACE6B,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK,CAAE;cACrCe,SAAS,EAAC,kDAAkD;cAAAnB,QAAA,eAE5DJ,OAAA,CAACJ,SAAS;gBAAC2B,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjC,OAAA;YAAKuB,SAAS,EAAC,WAAW;YAAAnB,QAAA,EACvBa,UAAU,CAACsB,GAAG,CAAEC,IAAI,IAAK;cACxB,MAAMC,QAAQ,GAAG1B,QAAQ,CAAC2B,QAAQ,KAAKF,IAAI,CAACrB,IAAI;cAChD,oBACEnB,OAAA,CAACrB,IAAI;gBAEHgE,EAAE,EAAEH,IAAI,CAACrB,IAAK;gBACdU,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,KAAK,CAAE;gBACrCe,SAAS,EAAE,2EACTkB,QAAQ,GACJ,iCAAiC,GACjC,oDAAoD,EACvD;gBAAArC,QAAA,gBAEHJ,OAAA,CAACwC,IAAI,CAACpB,IAAI;kBACRG,SAAS,EAAE,gBACTkB,QAAQ,GAAG,kBAAkB,GAAG,yCAAyC;gBACxE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACDO,IAAI,CAACtB,IAAI;cAAA,GAdLsB,IAAI,CAACtB,IAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CAAC;YAEX,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA,eACb;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlBjC,OAAA;MAAKuB,SAAS,EAAC,0DAA0D;MAAAnB,QAAA,eACvEJ,OAAA;QAAKuB,SAAS,EAAC,qFAAqF;QAAAnB,QAAA,gBAClGJ,OAAA;UAAKuB,SAAS,EAAC,sCAAsC;UAAAnB,QAAA,eACnDJ,OAAA;YAAIuB,SAAS,EAAC,qCAAqC;YAAAnB,QAAA,EAAC;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNjC,OAAA;UAAKuB,SAAS,EAAC,4BAA4B;UAAAnB,QAAA,EACxCa,UAAU,CAACsB,GAAG,CAAEC,IAAI,IAAK;YACxB,MAAMC,QAAQ,GAAG1B,QAAQ,CAAC2B,QAAQ,KAAKF,IAAI,CAACrB,IAAI;YAChD,oBACEnB,OAAA,CAACrB,IAAI;cAEHgE,EAAE,EAAEH,IAAI,CAACrB,IAAK;cACdI,SAAS,EAAE,oEACTkB,QAAQ,GACJ,iCAAiC,GACjC,oDAAoD,EACvD;cAAArC,QAAA,gBAEHJ,OAAA,CAACwC,IAAI,CAACpB,IAAI;gBACRG,SAAS,EAAE,gBACTkB,QAAQ,GAAG,kBAAkB,GAAG,yCAAyC;cACxE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACDO,IAAI,CAACtB,IAAI,EACTsB,IAAI,CAACtB,IAAI,KAAK,OAAO,IAAIJ,aAAa,iBACrCd,OAAA;gBAAMuB,SAAS,EAAC,wFAAwF;gBAAAnB,QAAA,GAAC,GACtG,EAACU,aAAa,CAAC8B,QAAQ;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACP;YAAA,GAlBIO,IAAI,CAACtB,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBV,CAAC;UAEX,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAKuB,SAAS,EAAC,+BAA+B;MAAAnB,QAAA,gBAE5CJ,OAAA;QAAKuB,SAAS,EAAC,+DAA+D;QAAAnB,QAAA,eAC5EJ,OAAA;UAAKuB,SAAS,EAAC,gDAAgD;UAAAnB,QAAA,gBAC7DJ,OAAA;YAAKuB,SAAS,EAAC,mBAAmB;YAAAnB,QAAA,eAChCJ,OAAA;cACE6B,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAAC,IAAI,CAAE;cACpCe,SAAS,EAAC,4DAA4D;cAAAnB,QAAA,eAEtEJ,OAAA,CAACL,SAAS;gBAAC4B,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjC,OAAA;YAAKuB,SAAS,EAAC,6BAA6B;YAAAnB,QAAA,gBAE1CJ,OAAA;cAAQuB,SAAS,EAAC,gDAAgD;cAAAnB,QAAA,gBAChEJ,OAAA,CAACH,QAAQ;gBAAC0B,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC/BnB,aAAa,iBACZd,OAAA;gBAAMuB,SAAS,EAAC,8GAA8G;gBAAAnB,QAAA,EAAC;cAE/H;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAGTjC,OAAA;cAAKuB,SAAS,EAAC,UAAU;cAAAnB,QAAA,gBACvBJ,OAAA;gBACE6B,OAAO,EAAEA,CAAA,KAAMnB,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;gBAC5Dc,SAAS,EAAC,6DAA6D;gBAAAnB,QAAA,gBAEvEJ,OAAA;kBAAKuB,SAAS,EAAC,sEAAsE;kBAAAnB,QAAA,eACnFJ,OAAA;oBAAMuB,SAAS,EAAC,gCAAgC;oBAAAnB,QAAA,EAC7CO,IAAI,aAAJA,IAAI,wBAAAL,UAAA,GAAJK,IAAI,CAAEO,IAAI,cAAAZ,UAAA,uBAAVA,UAAA,CAAYuC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNjC,OAAA;kBAAMuB,SAAS,EAAC,mDAAmD;kBAAAnB,QAAA,EAChEO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO;gBAAI;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACPjC,OAAA,CAACF,eAAe;kBAACyB,SAAS,EAAC;gBAAuB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eAETjC,OAAA,CAACjB,eAAe;gBAAAqB,QAAA,EACbK,mBAAmB,iBAClBT,OAAA,CAAClB,MAAM,CAAC0C,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAK,CAAE;kBACrCpB,OAAO,EAAE;oBAAED,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAE,CAAE;kBAClCnB,IAAI,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEqB,KAAK,EAAE;kBAAK,CAAE;kBAClCxB,SAAS,EAAC,2FAA2F;kBAAAnB,QAAA,eAErGJ,OAAA;oBAAKuB,SAAS,EAAC,MAAM;oBAAAnB,QAAA,gBACnBJ,OAAA,CAACrB,IAAI;sBACHgE,EAAE,EAAC,UAAU;sBACbpB,SAAS,EAAC,qEAAqE;sBAC/EM,OAAO,EAAEA,CAAA,KAAMnB,sBAAsB,CAAC,KAAK,CAAE;sBAAAN,QAAA,gBAE7CJ,OAAA,CAACR,QAAQ;wBAAC+B,SAAS,EAAC;sBAAc;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPjC,OAAA;sBACE6B,OAAO,EAAEP,YAAa;sBACtBC,SAAS,EAAC,4EAA4E;sBAAAnB,QAAA,gBAEtFJ,OAAA,CAACN,yBAAyB;wBAAC6B,SAAS,EAAC;sBAAc;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAExD;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACc,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAMuB,SAAS,EAAC,QAAQ;QAAAnB,QAAA,eACtBJ,OAAA;UAAKuB,SAAS,EAAC,MAAM;UAAAnB,QAAA,eACnBJ,OAAA;YAAKuB,SAAS,EAAC,wCAAwC;YAAAnB,QAAA,EACpDA;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBAAIlD,OAAA,CAACd,UAAU;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtD,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAlNIF,MAAM;EAAA,QAGwBnB,OAAO,EACfC,MAAM,EACfL,WAAW,EACXC,WAAW;AAAA;AAAAsE,EAAA,GANxBhD,MAAM;AAoNZ,eAAeA,MAAM;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}