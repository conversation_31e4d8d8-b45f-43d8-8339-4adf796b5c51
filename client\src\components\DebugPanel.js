import React, { useState } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';

const DebugPanel = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [testing, setTesting] = useState(false);
  const { user, token } = useAuth();

  const runTests = async () => {
    setTesting(true);
    const results = {};

    // Test 1: Backend Health Check
    try {
      const response = await axios.get('http://localhost:5002/health');
      results.health = { success: true, data: response.data };
    } catch (error) {
      results.health = { success: false, error: error.message };
    }

    // Test 2: API Base URL
    try {
      const response = await axios.get('/api/health');
      results.apiHealth = { success: true, data: response.data };
    } catch (error) {
      results.apiHealth = { success: false, error: error.message };
    }

    // Test 3: Authentication Status
    results.auth = {
      hasToken: !!token,
      hasUser: !!user,
      tokenLength: token ? token.length : 0,
      userName: user?.name || 'Not logged in'
    };

    // Test 4: Locations API
    try {
      const response = await axios.get('/api/locations');
      results.locations = { success: true, count: response.data?.data?.locations?.length || 0 };
    } catch (error) {
      results.locations = { success: false, error: error.message };
    }

    // Test 5: Appointments API (if authenticated)
    if (token) {
      try {
        const response = await axios.get('/api/appointments');
        results.appointments = { success: true, count: response.data?.data?.appointments?.length || 0 };
      } catch (error) {
        results.appointments = { success: false, error: error.message };
      }
    } else {
      results.appointments = { success: false, error: 'Not authenticated' };
    }

    setTestResults(results);
    setTesting(false);
  };

  const copyToClipboard = () => {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      testResults,
      authStatus: {
        hasToken: !!token,
        hasUser: !!user,
        userName: user?.name
      }
    };

    navigator.clipboard.writeText(JSON.stringify(debugInfo, null, 2));
    toast.success('Debug info copied to clipboard');
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-red-500 hover:bg-red-600 text-white p-3 rounded-full shadow-lg z-50"
        title="Debug Panel"
      >
        🐛
      </button>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="fixed bottom-4 right-4 bg-white rounded-lg shadow-xl border p-6 w-96 max-h-96 overflow-y-auto z-50"
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-gray-900">Debug Panel</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ✕
        </button>
      </div>

      <div className="space-y-4">
        <div className="flex gap-2">
          <button
            onClick={runTests}
            disabled={testing}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
          >
            {testing ? 'Testing...' : 'Run Tests'}
          </button>
          <button
            onClick={copyToClipboard}
            className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
          >
            Copy Info
          </button>
        </div>

        {Object.keys(testResults).length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold text-gray-800">Test Results:</h4>

            {Object.entries(testResults).map(([test, result]) => (
              <div key={test} className="text-sm">
                <div className="flex items-center gap-2">
                  <span className={`w-3 h-3 rounded-full ${result.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                  <span className="font-medium">{test}</span>
                </div>
                {result.success ? (
                  <div className="text-green-600 ml-5">
                    {result.data ? JSON.stringify(result.data).substring(0, 50) + '...' :
                     result.count !== undefined ? `Count: ${result.count}` : 'Success'}
                  </div>
                ) : (
                  <div className="text-red-600 ml-5 text-xs">{result.error}</div>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <div><strong>Backend URL:</strong> http://localhost:5002</div>
          <div><strong>Frontend URL:</strong> {window.location.origin}</div>
          <div><strong>Auth Token:</strong> {token ? 'Present' : 'Missing'}</div>
          <div><strong>User:</strong> {user?.name || 'Not logged in'}</div>
        </div>
      </div>
    </motion.div>
  );
};

export default DebugPanel;
