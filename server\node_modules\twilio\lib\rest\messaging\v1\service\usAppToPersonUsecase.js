"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsAppToPersonUsecaseInstance = exports.UsAppToPersonUsecaseListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
function UsAppToPersonUsecaseListInstance(version, messagingServiceSid) {
    if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
        throw new Error("Parameter 'messagingServiceSid' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { messagingServiceSid };
    instance._uri = `/Services/${messagingServiceSid}/Compliance/Usa2p/Usecases`;
    instance.fetch = function fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["brandRegistrationSid"] !== undefined)
            data["BrandRegistrationSid"] = params["brandRegistrationSid"];
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UsAppToPersonUsecaseInstance(operationVersion, payload, instance._solution.messagingServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UsAppToPersonUsecaseListInstance = UsAppToPersonUsecaseListInstance;
class UsAppToPersonUsecaseInstance {
    constructor(_version, payload, messagingServiceSid) {
        this._version = _version;
        this.usAppToPersonUsecases = payload.us_app_to_person_usecases;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            usAppToPersonUsecases: this.usAppToPersonUsecases,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UsAppToPersonUsecaseInstance = UsAppToPersonUsecaseInstance;
