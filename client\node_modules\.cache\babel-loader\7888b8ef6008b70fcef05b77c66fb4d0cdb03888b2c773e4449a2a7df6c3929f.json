{"ast": null, "code": "import startOfHour from \"../startOfHour/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameHour\n * @category Hour Helpers\n * @summary Are the given dates in the same hour (and same day)?\n *\n * @description\n * Are the given dates in the same hour (and same day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same hour (and same day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 4 September 06:30:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 6, 30))\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 5 September 06:00:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 5, 6, 0))\n * //=> false\n */\nexport default function isSameHour(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfHour = startOfHour(dirtyDateLeft);\n  var dateRightStartOfHour = startOfHour(dirtyDateRight);\n  return dateLeftStartOfHour.getTime() === dateRightStartOfHour.getTime();\n}", "map": {"version": 3, "names": ["startOfHour", "requiredArgs", "isSameHour", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfHour", "dateRightStartOfHour", "getTime"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/isSameHour/index.js"], "sourcesContent": ["import startOfHour from \"../startOfHour/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameHour\n * @category Hour Helpers\n * @summary Are the given dates in the same hour (and same day)?\n *\n * @description\n * Are the given dates in the same hour (and same day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same hour (and same day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 4 September 06:30:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 6, 30))\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:00 and 5 September 06:00:00 in the same hour?\n * const result = isSameHour(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 5, 6, 0))\n * //=> false\n */\nexport default function isSameHour(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfHour = startOfHour(dirtyDateLeft);\n  var dateRightStartOfHour = startOfHour(dirtyDateRight);\n  return dateLeftStartOfHour.getTime() === dateRightStartOfHour.getTime();\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAChEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,mBAAmB,GAAGN,WAAW,CAACG,aAAa,CAAC;EACpD,IAAII,oBAAoB,GAAGP,WAAW,CAACI,cAAc,CAAC;EACtD,OAAOE,mBAAmB,CAACE,OAAO,CAAC,CAAC,KAAKD,oBAAoB,CAACC,OAAO,CAAC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}