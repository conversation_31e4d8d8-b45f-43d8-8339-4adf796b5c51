"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2020_date = void 0;
const base_config_1 = require("./base-config");
const es2020_intl_1 = require("./es2020.intl");
exports.es2020_date = Object.assign(Object.assign({}, es2020_intl_1.es2020_intl), { Date: base_config_1.TYPE });
//# sourceMappingURL=es2020.date.js.map