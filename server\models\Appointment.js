const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Appointment = sequelize.define('Appointment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  appointment_number: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  service_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  location_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'locations',
      key: 'id'
    }
  },
  appointment_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  appointment_time: {
    type: DataTypes.TIME,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM(
      'scheduled',
      'confirmed',
      'in_progress',
      'completed',
      'cancelled',
      'no_show',
      'rescheduled'
    ),
    defaultValue: 'scheduled'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    validate: {
      min: 1,
      max: 5
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  patient_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  admin_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  estimated_duration: {
    type: DataTypes.INTEGER, // in minutes
    allowNull: true
  },
  actual_start_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_end_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  check_in_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  payment_status: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded', 'not_required'),
    defaultValue: 'not_required'
  },
  payment_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  payment_method: {
    type: DataTypes.ENUM('cash', 'mobile_money', 'card', 'insurance'),
    allowNull: true
  },
  payment_reference: {
    type: DataTypes.STRING,
    allowNull: true
  },
  reminder_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  reminder_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  feedback_rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  feedback_comment: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  cancelled_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  cancelled_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  cancellation_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  rescheduled_from: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'appointments',
      key: 'id'
    }
  }
}, {
  tableName: 'appointments',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['service_id']
    },
    {
      fields: ['location_id']
    },
    {
      fields: ['appointment_date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['appointment_number']
    },
    {
      unique: true,
      fields: ['appointment_number']
    }
  ],
  hooks: {
    beforeCreate: async (appointment) => {
      if (!appointment.appointment_number) {
        // Generate unique appointment number
        const date = new Date();
        const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
        const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        appointment.appointment_number = `APT${dateStr}${randomNum}`;
      }
    }
  }
});

// Instance methods
Appointment.prototype.canBeCancelled = function() {
  const now = new Date();
  const appointmentDateTime = new Date(`${this.appointment_date} ${this.appointment_time}`);
  const timeDiff = appointmentDateTime.getTime() - now.getTime();
  const hoursDiff = timeDiff / (1000 * 60 * 60);
  
  return hoursDiff > 2 && ['scheduled', 'confirmed'].includes(this.status);
};

Appointment.prototype.canBeRescheduled = function() {
  return this.canBeCancelled();
};

Appointment.prototype.isOverdue = function() {
  const now = new Date();
  const appointmentDateTime = new Date(`${this.appointment_date} ${this.appointment_time}`);
  
  return now > appointmentDateTime && this.status === 'scheduled';
};

Appointment.prototype.getDuration = function() {
  if (this.actual_start_time && this.actual_end_time) {
    return Math.round((this.actual_end_time - this.actual_start_time) / (1000 * 60));
  }
  return this.estimated_duration;
};

module.exports = Appointment;
