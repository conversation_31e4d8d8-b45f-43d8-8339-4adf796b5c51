// Initial data seeder for Umurongo
require('dotenv').config();
const { User, Location, Service } = require('../models');

async function seedInitialData() {
  try {
    console.log('🌱 Seeding initial data...');

    // Create system admin user
    const systemAdmin = await User.findOrCreate({
      where: { phone: '+************' },
      defaults: {
        name: 'System Administrator',
        phone: '+************',
        email: '<EMAIL>',
        password: 'admin123456',
        role: 'system_admin',
        is_verified: true,
        language: 'en'
      }
    });

    console.log('✅ System admin created');

    // Create sample business admin
    const businessAdmin = await User.findOrCreate({
      where: { phone: '+************' },
      defaults: {
        name: 'Dr. <PERSON>',
        phone: '+************',
        email: '<EMAIL>',
        password: 'admin123456',
        role: 'business_admin',
        is_verified: true,
        language: 'en'
      }
    });

    console.log('✅ Business admin created');

    // Create sample customer
    const customer = await User.findOrCreate({
      where: { phone: '+************' },
      defaults: {
        name: 'Alice Uwimana',
        phone: '+************',
        email: '<EMAIL>',
        password: 'customer123',
        role: 'customer',
        is_verified: true,
        language: 'rw'
      }
    });

    console.log('✅ Sample customer created');

    // Create sample locations
    const remeraHealthCenter = await Location.findOrCreate({
      where: { name: 'Remera Health Center' },
      defaults: {
        name: 'Remera Health Center',
        description: 'Primary healthcare facility serving Remera community',
        address: 'KG 11 Ave, Remera, Gasabo',
        district: 'Gasabo',
        sector: 'Remera',
        cell: 'Urumuri',
        contact_phone: '+250788123456',
        contact_email: '<EMAIL>',
        type: 'health_center',
        latitude: -1.9441,
        longitude: 30.0619,
        max_daily_appointments: 150,
        appointment_duration: 30,
        requires_payment: false,
        admin_user_id: businessAdmin[0].id
      }
    });

    const kigaliSalon = await Location.findOrCreate({
      where: { name: 'Kigali Beauty Salon' },
      defaults: {
        name: 'Kigali Beauty Salon',
        description: 'Professional beauty and hair care services',
        address: 'KN 3 Rd, Nyarugenge',
        district: 'Nyarugenge',
        sector: 'Nyarugenge',
        contact_phone: '+250788654321',
        type: 'salon',
        latitude: -1.9536,
        longitude: 30.0606,
        max_daily_appointments: 50,
        appointment_duration: 60,
        requires_payment: true,
        payment_amount: 5000,
        admin_user_id: businessAdmin[0].id
      }
    });

    console.log('✅ Sample locations created');

    // Create sample services for health center
    const healthServices = [
      {
        name: 'General Consultation',
        description: 'General medical consultation and checkup',
        category: 'consultation',
        duration: 30,
        location_id: remeraHealthCenter[0].id,
        max_daily_slots: 40,
        advance_booking_days: 7
      },
      {
        name: 'Vaccination',
        description: 'Routine and travel vaccinations',
        category: 'vaccination',
        duration: 15,
        location_id: remeraHealthCenter[0].id,
        max_daily_slots: 30,
        advance_booking_days: 14
      },
      {
        name: 'Maternity Care',
        description: 'Prenatal and postnatal care',
        category: 'maternity',
        duration: 45,
        location_id: remeraHealthCenter[0].id,
        max_daily_slots: 20,
        advance_booking_days: 30
      },
      {
        name: 'Laboratory Tests',
        description: 'Blood tests and medical laboratory services',
        category: 'laboratory',
        duration: 20,
        location_id: remeraHealthCenter[0].id,
        max_daily_slots: 50,
        advance_booking_days: 3
      }
    ];

    for (const serviceData of healthServices) {
      await Service.findOrCreate({
        where: { 
          name: serviceData.name,
          location_id: serviceData.location_id 
        },
        defaults: serviceData
      });
    }

    // Create sample services for salon
    const salonServices = [
      {
        name: 'Hair Cut & Styling',
        description: 'Professional hair cutting and styling',
        category: 'haircut',
        duration: 60,
        price: 3000,
        requires_payment: true,
        location_id: kigaliSalon[0].id,
        max_daily_slots: 15,
        advance_booking_days: 7
      },
      {
        name: 'Manicure & Pedicure',
        description: 'Nail care and beauty treatment',
        category: 'manicure',
        duration: 90,
        price: 5000,
        requires_payment: true,
        location_id: kigaliSalon[0].id,
        max_daily_slots: 10,
        advance_booking_days: 5
      },
      {
        name: 'Hair Treatment',
        description: 'Deep conditioning and hair treatment',
        category: 'styling',
        duration: 120,
        price: 8000,
        requires_payment: true,
        location_id: kigaliSalon[0].id,
        max_daily_slots: 8,
        advance_booking_days: 10
      }
    ];

    for (const serviceData of salonServices) {
      await Service.findOrCreate({
        where: { 
          name: serviceData.name,
          location_id: serviceData.location_id 
        },
        defaults: serviceData
      });
    }

    console.log('✅ Sample services created');

    console.log('\n🎉 Initial data seeding completed!');
    console.log('\nSample accounts created:');
    console.log('System Admin: +************ / admin123456');
    console.log('Business Admin: +************ / admin123456');
    console.log('Customer: +************ / customer123');
    console.log('\nSample locations:');
    console.log('- Remera Health Center (4 services)');
    console.log('- Kigali Beauty Salon (3 services)');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  }
}

// Run seeder if called directly
if (require.main === module) {
  const { sequelize } = require('../models');
  
  sequelize.sync({ force: false })
    .then(() => seedInitialData())
    .then(() => {
      console.log('\n✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = seedInitialData;
