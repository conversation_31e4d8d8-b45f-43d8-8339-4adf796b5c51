// Simple test script to verify the backend setup
require('dotenv').config();

console.log('🧪 Testing Umurongo Backend Setup...\n');

// Test 1: Environment Variables
console.log('1. Environment Variables:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
console.log(`   PORT: ${process.env.PORT || 'not set'}`);
console.log(`   DB_HOST: ${process.env.DB_HOST || 'not set'}`);
console.log(`   DB_NAME: ${process.env.DB_NAME || 'not set'}`);
console.log(`   JWT_SECRET: ${process.env.JWT_SECRET ? 'set' : 'not set'}`);
console.log(`   TWILIO_ACCOUNT_SID: ${process.env.TWILIO_ACCOUNT_SID ? 'set' : 'not set'}`);

// Test 2: Required modules
console.log('\n2. Testing Module Imports:');
try {
  const express = require('express');
  console.log('   ✅ Express.js imported successfully');
} catch (error) {
  console.log('   ❌ Express.js import failed:', error.message);
}

try {
  const sequelize = require('./config/database');
  console.log('   ✅ Database config imported successfully');
} catch (error) {
  console.log('   ❌ Database config import failed:', error.message);
}

try {
  const { User } = require('./models');
  console.log('   ✅ User model imported successfully');
} catch (error) {
  console.log('   ❌ User model import failed:', error.message);
}

try {
  const authRoutes = require('./routes/auth');
  console.log('   ✅ Auth routes imported successfully');
} catch (error) {
  console.log('   ❌ Auth routes import failed:', error.message);
}

try {
  const smsService = require('./services/smsService');
  console.log('   ✅ SMS service imported successfully');
} catch (error) {
  console.log('   ❌ SMS service import failed:', error.message);
}

// Test 3: Database connection
console.log('\n3. Testing Database Connection:');
async function testDatabase() {
  try {
    const sequelize = require('./config/database');
    await sequelize.authenticate();
    console.log('   ✅ Database connection successful');
    
    // Test model sync (without altering tables)
    await sequelize.sync({ force: false });
    console.log('   ✅ Database models synchronized');
    
    await sequelize.close();
  } catch (error) {
    console.log('   ❌ Database connection failed:', error.message);
  }
}

// Test 4: JWT functionality
console.log('\n4. Testing JWT:');
try {
  const jwt = require('jsonwebtoken');
  const testPayload = { userId: 'test-123' };
  const token = jwt.sign(testPayload, process.env.JWT_SECRET || 'test-secret', { expiresIn: '1h' });
  const decoded = jwt.verify(token, process.env.JWT_SECRET || 'test-secret');
  
  if (decoded.userId === testPayload.userId) {
    console.log('   ✅ JWT signing and verification working');
  } else {
    console.log('   ❌ JWT verification failed');
  }
} catch (error) {
  console.log('   ❌ JWT test failed:', error.message);
}

// Test 5: Password hashing
console.log('\n5. Testing Password Hashing:');
try {
  const bcrypt = require('bcryptjs');
  const testPassword = 'testpassword123';
  const hashedPassword = bcrypt.hashSync(testPassword, 12);
  const isValid = bcrypt.compareSync(testPassword, hashedPassword);
  
  if (isValid) {
    console.log('   ✅ Password hashing and comparison working');
  } else {
    console.log('   ❌ Password comparison failed');
  }
} catch (error) {
  console.log('   ❌ Password hashing test failed:', error.message);
}

// Test 6: SMS Service
console.log('\n6. Testing SMS Service:');
try {
  const smsService = require('./services/smsService');
  const formattedPhone = smsService.formatPhoneNumber('0781234567');
  
  if (formattedPhone === '+250781234567') {
    console.log('   ✅ Phone number formatting working');
  } else {
    console.log('   ❌ Phone number formatting failed');
  }
} catch (error) {
  console.log('   ❌ SMS service test failed:', error.message);
}

// Run async tests
async function runAsyncTests() {
  await testDatabase();
  
  console.log('\n🎉 Setup test completed!');
  console.log('\nNext steps:');
  console.log('1. Make sure MySQL is running and database exists');
  console.log('2. Update .env file with your actual credentials');
  console.log('3. Run: npm run dev');
  console.log('4. Test API endpoints with Postman or curl');
  
  process.exit(0);
}

runAsyncTests().catch(error => {
  console.error('\n❌ Async test failed:', error);
  process.exit(1);
});
