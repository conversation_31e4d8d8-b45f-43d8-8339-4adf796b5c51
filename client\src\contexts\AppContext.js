import { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

const AppContext = createContext();

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export const AppProvider = ({ children }) => {
  const [locations, setLocations] = useState([]);
  const [services, setServices] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [queuePosition, setQueuePosition] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedService, setSelectedService] = useState(null);

  // Fetch locations
  const fetchLocations = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/locations');

      // Handle different response structures
      if (response.data && response.data.data && response.data.data.locations) {
        setLocations(response.data.data.locations);
      } else if (response.data && Array.isArray(response.data)) {
        setLocations(response.data);
      } else {
        console.log('No locations found or unexpected response structure');
        setLocations([]);
      }
    } catch (error) {
      console.error('Failed to fetch locations:', error);

      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        toast.error('Cannot connect to server. Please check if the backend is running.');
      } else {
        toast.error('Failed to load locations');
      }
      setLocations([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch services
  const fetchServices = async (locationId = null) => {
    try {
      setLoading(true);
      const url = locationId ? `/api/services?location_id=${locationId}` : '/api/services';
      const response = await axios.get(url);

      // Handle different response structures
      let servicesData = [];
      if (response.data && response.data.data && response.data.data.services) {
        servicesData = response.data.data.services;
      } else if (response.data && Array.isArray(response.data)) {
        servicesData = response.data;
      } else if (response.data && response.data.services) {
        servicesData = response.data.services;
      } else {
        console.log('No services found or unexpected response structure');
        servicesData = [];
      }

      setServices(servicesData);

      // Return services for location-specific calls
      if (locationId) {
        return servicesData;
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);

      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        toast.error('Cannot connect to server. Please check if the backend is running.');
      } else {
        toast.error('Failed to load services');
      }
      setServices([]);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Fetch user appointments
  const fetchAppointments = async () => {
    try {
      setLoading(true);

      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('No token found, skipping appointments fetch');
        setAppointments([]);
        return;
      }

      const response = await axios.get('/api/appointments');

      // Handle different response structures
      if (response.data && response.data.data && response.data.data.appointments) {
        setAppointments(response.data.data.appointments);
      } else if (response.data && Array.isArray(response.data)) {
        setAppointments(response.data);
      } else {
        console.log('No appointments found or unexpected response structure');
        setAppointments([]);
      }
    } catch (error) {
      console.error('Failed to fetch appointments:', error);

      // Handle specific error cases
      if (error.response?.status === 401) {
        console.log('Authentication failed - user may need to log in again');
        setAppointments([]);
      } else if (error.response?.status === 404) {
        console.log('Appointments endpoint not found');
        setAppointments([]);
      } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        toast.error('Cannot connect to server. Please check if the backend is running.');
      } else {
        toast.error('Failed to load appointments');
      }
    } finally {
      setLoading(false);
    }
  };

  // Book appointment
  const bookAppointment = async (appointmentData) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/appointments', appointmentData);

      // Add new appointment to state
      setAppointments(prev => [response.data.data.appointment, ...prev]);

      toast.success('Appointment booked successfully!');
      return { success: true, appointment: response.data.data.appointment };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to book appointment';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Cancel appointment
  const cancelAppointment = async (appointmentId) => {
    try {
      setLoading(true);
      await axios.put(`/api/appointments/${appointmentId}/cancel`);

      // Update appointment status in state
      setAppointments(prev =>
        prev.map(apt =>
          apt.id === appointmentId
            ? { ...apt, status: 'cancelled' }
            : apt
        )
      );

      toast.success('Appointment cancelled successfully');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to cancel appointment';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Get queue position
  const getQueuePosition = async () => {
    try {
      const response = await axios.get('/api/queues/my-position');
      setQueuePosition(response.data.data);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get queue position:', error);
      return null;
    }
  };

  // Join queue
  const joinQueue = async (serviceId) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/queues/join', {
        service_id: serviceId
      });

      setQueuePosition(response.data.data);
      toast.success('Joined queue successfully!');
      return { success: true, position: response.data.data };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to join queue';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Leave queue
  const leaveQueue = async () => {
    try {
      setLoading(true);
      await axios.post('/api/queues/leave');

      setQueuePosition(null);
      toast.success('Left queue successfully');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to leave queue';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Check service availability
  const checkAvailability = async (serviceId, date) => {
    try {
      const response = await axios.get(`/api/services/${serviceId}/availability`, {
        params: { date }
      });
      return response.data.data;
    } catch (error) {
      console.error('Failed to check availability:', error);
      return null;
    }
  };

  // Get dashboard stats
  const getDashboardStats = async () => {
    try {
      const response = await axios.get('/api/reports/dashboard');
      return response.data.data;
    } catch (error) {
      console.error('Failed to get dashboard stats:', error);
      return null;
    }
  };

  // ==================== CRUD OPERATIONS ====================

  // Locations CRUD
  const createLocation = async (locationData) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/locations', locationData);
      await fetchLocations(); // Refresh list
      toast.success('Location created successfully');
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Failed to create location:', error);
      toast.error(error.response?.data?.message || 'Failed to create location');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  const updateLocation = async (id, locationData) => {
    try {
      setLoading(true);
      const response = await axios.put(`/api/locations/${id}`, locationData);
      await fetchLocations(); // Refresh list
      toast.success('Location updated successfully');
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Failed to update location:', error);
      toast.error(error.response?.data?.message || 'Failed to update location');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  const deleteLocation = async (id) => {
    try {
      setLoading(true);
      await axios.delete(`/api/locations/${id}`);
      await fetchLocations(); // Refresh list
      toast.success('Location deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Failed to delete location:', error);
      toast.error(error.response?.data?.message || 'Failed to delete location');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  // Services CRUD
  const createService = async (serviceData) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/services', serviceData);
      await fetchServices(); // Refresh list
      toast.success('Service created successfully');
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Failed to create service:', error);
      toast.error(error.response?.data?.message || 'Failed to create service');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  const updateService = async (id, serviceData) => {
    try {
      setLoading(true);
      const response = await axios.put(`/api/services/${id}`, serviceData);
      await fetchServices(); // Refresh list
      toast.success('Service updated successfully');
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Failed to update service:', error);
      toast.error(error.response?.data?.message || 'Failed to update service');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  const deleteService = async (id) => {
    try {
      setLoading(true);
      await axios.delete(`/api/services/${id}`);
      await fetchServices(); // Refresh list
      toast.success('Service deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Failed to delete service:', error);
      toast.error(error.response?.data?.message || 'Failed to delete service');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  // Appointments CRUD (update and delete)
  const updateAppointment = async (id, appointmentData) => {
    try {
      setLoading(true);
      const response = await axios.put(`/api/appointments/${id}`, appointmentData);
      await fetchAppointments(); // Refresh list
      toast.success('Appointment updated successfully');
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error('Failed to update appointment:', error);
      toast.error(error.response?.data?.message || 'Failed to update appointment');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  const deleteAppointment = async (id) => {
    try {
      setLoading(true);
      await axios.delete(`/api/appointments/${id}`);
      await fetchAppointments(); // Refresh list
      toast.success('Appointment deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('Failed to delete appointment:', error);
      toast.error(error.response?.data?.message || 'Failed to delete appointment');
      return { success: false, error: error.response?.data?.message };
    } finally {
      setLoading(false);
    }
  };

  // Initialize data on mount
  useEffect(() => {
    fetchLocations();
  }, []);

  const value = {
    // State
    locations,
    services,
    appointments,
    queuePosition,
    loading,
    selectedLocation,
    selectedService,

    // Setters
    setSelectedLocation,
    setSelectedService,

    // Actions
    fetchLocations,
    fetchServices,
    fetchAppointments,
    bookAppointment,
    cancelAppointment,
    getQueuePosition,
    joinQueue,
    leaveQueue,
    checkAvailability,
    getDashboardStats,

    // CRUD operations
    createLocation,
    updateLocation,
    deleteLocation,
    createService,
    updateService,
    deleteService,
    updateAppointment,
    deleteAppointment,

    // Computed values
    hasActiveAppointments: appointments.some(apt =>
      ['confirmed', 'in_progress'].includes(apt.status)
    ),
    upcomingAppointments: appointments.filter(apt =>
      apt.status === 'confirmed' && new Date(apt.appointment_date) > new Date()
    ),
    pastAppointments: appointments.filter(apt =>
      ['completed', 'cancelled'].includes(apt.status)
    )
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};
