import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

const AppContext = createContext();

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export const AppProvider = ({ children }) => {
  const [locations, setLocations] = useState([]);
  const [services, setServices] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [queuePosition, setQueuePosition] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedService, setSelectedService] = useState(null);

  // Fetch locations
  const fetchLocations = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/locations');
      setLocations(response.data.data.locations);
    } catch (error) {
      console.error('Failed to fetch locations:', error);
      toast.error('Failed to load locations');
    } finally {
      setLoading(false);
    }
  };

  // Fetch services
  const fetchServices = async (locationId = null) => {
    try {
      setLoading(true);
      const url = locationId ? `/api/services?location_id=${locationId}` : '/api/services';
      const response = await axios.get(url);
      setServices(response.data.data.services);
    } catch (error) {
      console.error('Failed to fetch services:', error);
      toast.error('Failed to load services');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user appointments
  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/appointments');
      setAppointments(response.data.data.appointments);
    } catch (error) {
      console.error('Failed to fetch appointments:', error);
      toast.error('Failed to load appointments');
    } finally {
      setLoading(false);
    }
  };

  // Book appointment
  const bookAppointment = async (appointmentData) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/appointments', appointmentData);
      
      // Add new appointment to state
      setAppointments(prev => [response.data.data.appointment, ...prev]);
      
      toast.success('Appointment booked successfully!');
      return { success: true, appointment: response.data.data.appointment };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to book appointment';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Cancel appointment
  const cancelAppointment = async (appointmentId) => {
    try {
      setLoading(true);
      await axios.put(`/api/appointments/${appointmentId}/cancel`);
      
      // Update appointment status in state
      setAppointments(prev => 
        prev.map(apt => 
          apt.id === appointmentId 
            ? { ...apt, status: 'cancelled' }
            : apt
        )
      );
      
      toast.success('Appointment cancelled successfully');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to cancel appointment';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Get queue position
  const getQueuePosition = async () => {
    try {
      const response = await axios.get('/api/queues/my-position');
      setQueuePosition(response.data.data);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get queue position:', error);
      return null;
    }
  };

  // Join queue
  const joinQueue = async (serviceId) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/queues/join', {
        service_id: serviceId
      });
      
      setQueuePosition(response.data.data);
      toast.success('Joined queue successfully!');
      return { success: true, position: response.data.data };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to join queue';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Leave queue
  const leaveQueue = async () => {
    try {
      setLoading(true);
      await axios.post('/api/queues/leave');
      
      setQueuePosition(null);
      toast.success('Left queue successfully');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to leave queue';
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setLoading(false);
    }
  };

  // Check service availability
  const checkAvailability = async (serviceId, date) => {
    try {
      const response = await axios.get(`/api/services/${serviceId}/availability`, {
        params: { date }
      });
      return response.data.data;
    } catch (error) {
      console.error('Failed to check availability:', error);
      return null;
    }
  };

  // Get dashboard stats
  const getDashboardStats = async () => {
    try {
      const response = await axios.get('/api/reports/dashboard');
      return response.data.data;
    } catch (error) {
      console.error('Failed to get dashboard stats:', error);
      return null;
    }
  };

  // Initialize data on mount
  useEffect(() => {
    fetchLocations();
  }, []);

  const value = {
    // State
    locations,
    services,
    appointments,
    queuePosition,
    loading,
    selectedLocation,
    selectedService,
    
    // Setters
    setSelectedLocation,
    setSelectedService,
    
    // Actions
    fetchLocations,
    fetchServices,
    fetchAppointments,
    bookAppointment,
    cancelAppointment,
    getQueuePosition,
    joinQueue,
    leaveQueue,
    checkAvailability,
    getDashboardStats,
    
    // Computed values
    hasActiveAppointments: appointments.some(apt => 
      ['confirmed', 'in_progress'].includes(apt.status)
    ),
    upcomingAppointments: appointments.filter(apt => 
      apt.status === 'confirmed' && new Date(apt.appointment_date) > new Date()
    ),
    pastAppointments: appointments.filter(apt => 
      ['completed', 'cancelled'].includes(apt.status)
    )
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};
