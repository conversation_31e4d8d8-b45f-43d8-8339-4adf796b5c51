{"ast": null, "code": "import { getDefaultOptions, setDefaultOptions as setInternalDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDefaultOptions\n * @category Common Helpers\n * @summary Set default options including locale.\n * @pure false\n *\n * @description\n * Sets the defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * @param {Object} newOptions - an object with options.\n * @param {Locale} [newOptions.locale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [newOptions.weekStartsOn] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [newOptions.firstWeekContainsDate] - the day of January, which is always in the first week of the year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Set global locale:\n * import { es } from 'date-fns/locale'\n * setDefaultOptions({ locale: es })\n * const result = format(new Date(2014, 8, 2), 'PPPP')\n * //=> 'martes, 2 de septiembre de 2014'\n *\n * @example\n * // Start of the week for 2 September 2014:\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Start of the week for 2 September 2014,\n * // when we set that week starts on Monday by default:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Mon Sep 01 2014 00:00:00\n *\n * @example\n * // Manually set options take priority over default options:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2), { weekStartsOn: 0 })\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Remove the option by setting it to `undefined`:\n * setDefaultOptions({ weekStartsOn: 1 })\n * setDefaultOptions({ weekStartsOn: undefined })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n */\nexport default function setDefaultOptions(newOptions) {\n  requiredArgs(1, arguments);\n  var result = {};\n  var defaultOptions = getDefaultOptions();\n  for (var property in defaultOptions) {\n    if (Object.prototype.hasOwnProperty.call(defaultOptions, property)) {\n      ;\n      result[property] = defaultOptions[property];\n    }\n  }\n  for (var _property in newOptions) {\n    if (Object.prototype.hasOwnProperty.call(newOptions, _property)) {\n      if (newOptions[_property] === undefined) {\n        delete result[_property];\n      } else {\n        ;\n        result[_property] = newOptions[_property];\n      }\n    }\n  }\n  setInternalDefaultOptions(result);\n}", "map": {"version": 3, "names": ["getDefaultOptions", "setDefaultOptions", "setInternalDefaultOptions", "requiredArgs", "newOptions", "arguments", "result", "defaultOptions", "property", "Object", "prototype", "hasOwnProperty", "call", "_property", "undefined"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/setDefaultOptions/index.js"], "sourcesContent": ["import { getDefaultOptions, setDefaultOptions as setInternalDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDefaultOptions\n * @category Common Helpers\n * @summary Set default options including locale.\n * @pure false\n *\n * @description\n * Sets the defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * @param {Object} newOptions - an object with options.\n * @param {Locale} [newOptions.locale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [newOptions.weekStartsOn] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [newOptions.firstWeekContainsDate] - the day of January, which is always in the first week of the year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Set global locale:\n * import { es } from 'date-fns/locale'\n * setDefaultOptions({ locale: es })\n * const result = format(new Date(2014, 8, 2), 'PPPP')\n * //=> 'martes, 2 de septiembre de 2014'\n *\n * @example\n * // Start of the week for 2 September 2014:\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Start of the week for 2 September 2014,\n * // when we set that week starts on Monday by default:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Mon Sep 01 2014 00:00:00\n *\n * @example\n * // Manually set options take priority over default options:\n * setDefaultOptions({ weekStartsOn: 1 })\n * const result = startOfWeek(new Date(2014, 8, 2), { weekStartsOn: 0 })\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Remove the option by setting it to `undefined`:\n * setDefaultOptions({ weekStartsOn: 1 })\n * setDefaultOptions({ weekStartsOn: undefined })\n * const result = startOfWeek(new Date(2014, 8, 2))\n * //=> Sun Aug 31 2014 00:00:00\n */\nexport default function setDefaultOptions(newOptions) {\n  requiredArgs(1, arguments);\n  var result = {};\n  var defaultOptions = getDefaultOptions();\n  for (var property in defaultOptions) {\n    if (Object.prototype.hasOwnProperty.call(defaultOptions, property)) {\n      ;\n      result[property] = defaultOptions[property];\n    }\n  }\n  for (var _property in newOptions) {\n    if (Object.prototype.hasOwnProperty.call(newOptions, _property)) {\n      if (newOptions[_property] === undefined) {\n        delete result[_property];\n      } else {\n        ;\n        result[_property] = newOptions[_property];\n      }\n    }\n  }\n  setInternalDefaultOptions(result);\n}"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,iBAAiB,IAAIC,yBAAyB,QAAQ,iCAAiC;AACnH,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASF,iBAAiBA,CAACG,UAAU,EAAE;EACpDD,YAAY,CAAC,CAAC,EAAEE,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,cAAc,GAAGP,iBAAiB,CAAC,CAAC;EACxC,KAAK,IAAIQ,QAAQ,IAAID,cAAc,EAAE;IACnC,IAAIE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,cAAc,EAAEC,QAAQ,CAAC,EAAE;MAClE;MACAF,MAAM,CAACE,QAAQ,CAAC,GAAGD,cAAc,CAACC,QAAQ,CAAC;IAC7C;EACF;EACA,KAAK,IAAIK,SAAS,IAAIT,UAAU,EAAE;IAChC,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,UAAU,EAAES,SAAS,CAAC,EAAE;MAC/D,IAAIT,UAAU,CAACS,SAAS,CAAC,KAAKC,SAAS,EAAE;QACvC,OAAOR,MAAM,CAACO,SAAS,CAAC;MAC1B,CAAC,MAAM;QACL;QACAP,MAAM,CAACO,SAAS,CAAC,GAAGT,UAAU,CAACS,SAAS,CAAC;MAC3C;IACF;EACF;EACAX,yBAAyB,CAACI,MAAM,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}