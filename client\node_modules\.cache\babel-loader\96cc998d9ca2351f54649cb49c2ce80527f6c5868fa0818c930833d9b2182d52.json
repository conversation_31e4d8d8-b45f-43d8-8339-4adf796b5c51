{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\pages\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MagnifyingGlassIcon, BuildingOfficeIcon, WrenchScrewdriverIcon, CalendarDaysIcon, UsersIcon, ClockIcon } from '@heroicons/react/24/outline';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _data$activeTab;\n  const [activeTab, setActiveTab] = useState('locations');\n  const [data, setData] = useState({\n    locations: [],\n    services: [],\n    appointments: [],\n    users: [],\n    queues: []\n  });\n  const [loading, setLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const tabs = [{\n    id: 'locations',\n    name: 'Locations',\n    icon: BuildingOfficeIcon,\n    color: 'blue'\n  }, {\n    id: 'services',\n    name: 'Services',\n    icon: WrenchScrewdriverIcon,\n    color: 'green'\n  }, {\n    id: 'appointments',\n    name: 'Appointments',\n    icon: CalendarDaysIcon,\n    color: 'purple'\n  }, {\n    id: 'users',\n    name: 'Users',\n    icon: UsersIcon,\n    color: 'indigo'\n  }, {\n    id: 'queues',\n    name: 'Queues',\n    icon: ClockIcon,\n    color: 'orange'\n  }];\n  useEffect(() => {\n    fetchData(activeTab);\n  }, [activeTab]);\n  const fetchData = async type => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`/api/${type}`);\n      setData(prev => ({\n        ...prev,\n        [type]: response.data.data[type] || response.data.data\n      }));\n    } catch (error) {\n      console.error(`Failed to fetch ${type}:`, error);\n      toast.error(`Failed to load ${type}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreate = () => {\n    setModalMode('create');\n    setSelectedItem(null);\n    setShowModal(true);\n  };\n  const handleEdit = item => {\n    setModalMode('edit');\n    setSelectedItem(item);\n    setShowModal(true);\n  };\n  const handleView = item => {\n    setModalMode('view');\n    setSelectedItem(item);\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!window.confirm('Are you sure you want to delete this item?')) return;\n    try {\n      await axios.delete(`/api/${activeTab}/${id}`);\n      toast.success('Item deleted successfully');\n      fetchData(activeTab);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to delete item');\n    }\n  };\n  const handleSave = async formData => {\n    try {\n      if (modalMode === 'create') {\n        await axios.post(`/api/${activeTab}`, formData);\n        toast.success('Item created successfully');\n      } else if (modalMode === 'edit') {\n        await axios.put(`/api/${activeTab}/${selectedItem.id}`, formData);\n        toast.success('Item updated successfully');\n      }\n      setShowModal(false);\n      fetchData(activeTab);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to save item');\n    }\n  };\n  const filteredData = ((_data$activeTab = data[activeTab]) === null || _data$activeTab === void 0 ? void 0 : _data$activeTab.filter(item => {\n    const searchFields = getSearchFields(activeTab, item);\n    return searchFields.some(field => field === null || field === void 0 ? void 0 : field.toLowerCase().includes(searchTerm.toLowerCase()));\n  })) || [];\n  const getSearchFields = (type, item) => {\n    switch (type) {\n      case 'locations':\n        return [item.name, item.address, item.phone, item.email];\n      case 'services':\n        return [item.name, item.description, item.category];\n      case 'appointments':\n        return [item.service_name, item.location_name, item.notes];\n      case 'users':\n        return [item.first_name, item.last_name, item.email, item.phone];\n      case 'queues':\n        return [item.service_name, item.location_name];\n      default:\n        return [];\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2\",\n          children: \"Manage all system components with full CRUD operations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreate,\n        className: \"mt-4 sm:mt-0 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), \"Add New \", activeTab.slice(0, -1)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.1\n      },\n      className: \"border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"-mb-px flex space-x-8 overflow-x-auto\",\n        children: tabs.map(tab => {\n          const Icon = tab.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `py-2 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${activeTab === tab.id ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), tab.name]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.2\n      },\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n        children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n          className: \"h-5 w-5 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: `Search ${activeTab}...`,\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        delay: 0.3\n      },\n      className: \"bg-white shadow-soft rounded-xl p-6\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 capitalize\",\n          children: [activeTab, \" Management (\", filteredData.length, \" items)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), filteredData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500\",\n            children: [\"No \", activeTab, \" found\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCreate,\n            className: \"mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\",\n            children: [\"Create First \", activeTab.slice(0, -1)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid gap-4\",\n          children: filteredData.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: item.name || `${item.first_name} ${item.last_name}` || item.service_name || `${activeTab.slice(0, -1)} #${item.id}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mt-1\",\n                  children: [activeTab === 'locations' && `${item.address} • ${item.phone}`, activeTab === 'services' && `${item.category} • ${item.duration}min • ${item.price} RWF`, activeTab === 'appointments' && `${item.appointment_date} ${item.appointment_time} • ${item.status}`, activeTab === 'users' && `${item.phone} • ${item.role}`, activeTab === 'queues' && `Position #${item.position} • ${item.status}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2 ml-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleView(item),\n                  className: \"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\",\n                  title: \"View\",\n                  children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(item),\n                  className: \"p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors\",\n                  title: \"Edit\",\n                  children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(item.id),\n                  className: \"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                  title: \"Delete\",\n                  children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 21\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: [modalMode === 'create' ? 'Create' : modalMode === 'edit' ? 'Edit' : 'View', \" \", activeTab.slice(0, -1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [selectedItem && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"whitespace-pre-wrap bg-gray-50 p-3 rounded-lg\",\n              children: JSON.stringify(selectedItem, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this), modalMode !== 'view' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: [\"Full form interface will be implemented here for \", modalMode, \" operations.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-400 mt-2\",\n              children: \"For now, you can test the API endpoints directly.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3 mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowModal(false),\n            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), modalMode !== 'view' && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              // For demo, just close modal\n              setShowModal(false);\n              toast.success(`${modalMode} operation would be performed here`);\n            },\n            className: \"flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n            children: modalMode === 'create' ? 'Create' : 'Update'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"O5LIfucexpO1ikBHEqqxcrrlRdY=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "motion", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "BuildingOfficeIcon", "WrenchScrewdriverIcon", "CalendarDaysIcon", "UsersIcon", "ClockIcon", "axios", "toast", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_data$activeTab", "activeTab", "setActiveTab", "data", "setData", "locations", "services", "appointments", "users", "queues", "loading", "setLoading", "searchTerm", "setSearchTerm", "showModal", "setShowModal", "modalMode", "setModalMode", "selectedItem", "setSelectedItem", "tabs", "id", "name", "icon", "color", "fetchData", "type", "response", "get", "prev", "error", "console", "handleCreate", "handleEdit", "item", "handleView", "handleDelete", "window", "confirm", "delete", "success", "_error$response", "_error$response$data", "message", "handleSave", "formData", "post", "put", "_error$response2", "_error$response2$data", "filteredData", "filter", "searchFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "some", "field", "toLowerCase", "includes", "address", "phone", "email", "description", "category", "service_name", "location_name", "notes", "first_name", "last_name", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "slice", "transition", "delay", "map", "tab", "Icon", "placeholder", "value", "onChange", "e", "target", "length", "duration", "price", "appointment_date", "appointment_time", "status", "role", "position", "title", "scale", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/pages/AdminDashboard.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  BuildingOfficeIcon,\n  WrenchScrewdriverIcon,\n  CalendarDaysIcon,\n  UsersIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\n\nconst AdminDashboard = () => {\n  const [activeTab, setActiveTab] = useState('locations');\n  const [data, setData] = useState({\n    locations: [],\n    services: [],\n    appointments: [],\n    users: [],\n    queues: []\n  });\n  const [loading, setLoading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [modalMode, setModalMode] = useState('create');\n  const [selectedItem, setSelectedItem] = useState(null);\n\n  const tabs = [\n    { id: 'locations', name: 'Locations', icon: BuildingOfficeIcon, color: 'blue' },\n    { id: 'services', name: 'Services', icon: WrenchScrewdriverIcon, color: 'green' },\n    { id: 'appointments', name: 'Appointments', icon: CalendarDaysIcon, color: 'purple' },\n    { id: 'users', name: 'Users', icon: UsersIcon, color: 'indigo' },\n    { id: 'queues', name: 'Queues', icon: ClockIcon, color: 'orange' }\n  ];\n\n  useEffect(() => {\n    fetchData(activeTab);\n  }, [activeTab]);\n\n  const fetchData = async (type) => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`/api/${type}`);\n      setData(prev => ({\n        ...prev,\n        [type]: response.data.data[type] || response.data.data\n      }));\n    } catch (error) {\n      console.error(`Failed to fetch ${type}:`, error);\n      toast.error(`Failed to load ${type}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreate = () => {\n    setModalMode('create');\n    setSelectedItem(null);\n    setShowModal(true);\n  };\n\n  const handleEdit = (item) => {\n    setModalMode('edit');\n    setSelectedItem(item);\n    setShowModal(true);\n  };\n\n  const handleView = (item) => {\n    setModalMode('view');\n    setSelectedItem(item);\n    setShowModal(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm('Are you sure you want to delete this item?')) return;\n\n    try {\n      await axios.delete(`/api/${activeTab}/${id}`);\n      toast.success('Item deleted successfully');\n      fetchData(activeTab);\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to delete item');\n    }\n  };\n\n  const handleSave = async (formData) => {\n    try {\n      if (modalMode === 'create') {\n        await axios.post(`/api/${activeTab}`, formData);\n        toast.success('Item created successfully');\n      } else if (modalMode === 'edit') {\n        await axios.put(`/api/${activeTab}/${selectedItem.id}`, formData);\n        toast.success('Item updated successfully');\n      }\n      setShowModal(false);\n      fetchData(activeTab);\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to save item');\n    }\n  };\n\n  const filteredData = data[activeTab]?.filter(item => {\n    const searchFields = getSearchFields(activeTab, item);\n    return searchFields.some(field =>\n      field?.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n  }) || [];\n\n  const getSearchFields = (type, item) => {\n    switch (type) {\n      case 'locations':\n        return [item.name, item.address, item.phone, item.email];\n      case 'services':\n        return [item.name, item.description, item.category];\n      case 'appointments':\n        return [item.service_name, item.location_name, item.notes];\n      case 'users':\n        return [item.first_name, item.last_name, item.email, item.phone];\n      case 'queues':\n        return [item.service_name, item.location_name];\n      default:\n        return [];\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\"\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Admin Dashboard</h1>\n          <p className=\"text-gray-600 mt-2\">Manage all system components with full CRUD operations</p>\n        </div>\n        <button\n          onClick={handleCreate}\n          className=\"mt-4 sm:mt-0 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center\"\n        >\n          <PlusIcon className=\"w-4 h-4 mr-2\" />\n          Add New {activeTab.slice(0, -1)}\n        </button>\n      </motion.div>\n\n      {/* Tabs */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n        className=\"border-b border-gray-200\"\n      >\n        <nav className=\"-mb-px flex space-x-8 overflow-x-auto\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${\n                  activeTab === tab.id\n                    ? 'border-primary-500 text-primary-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <Icon className=\"w-4 h-4 mr-2\" />\n                {tab.name}\n              </button>\n            );\n          })}\n        </nav>\n      </motion.div>\n\n      {/* Search */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n        className=\"relative\"\n      >\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <input\n          type=\"text\"\n          placeholder={`Search ${activeTab}...`}\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n          className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n        />\n      </motion.div>\n\n      {/* Simple CRUD Interface */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-white shadow-soft rounded-xl p-6\"\n      >\n        {loading ? (\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin\"></div>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 capitalize\">\n              {activeTab} Management ({filteredData.length} items)\n            </h3>\n\n            {filteredData.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <p className=\"text-gray-500\">No {activeTab} found</p>\n                <button\n                  onClick={handleCreate}\n                  className=\"mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\"\n                >\n                  Create First {activeTab.slice(0, -1)}\n                </button>\n              </div>\n            ) : (\n              <div className=\"grid gap-4\">\n                {filteredData.map((item) => (\n                  <div key={item.id} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-medium text-gray-900\">\n                          {item.name || `${item.first_name} ${item.last_name}` || item.service_name || `${activeTab.slice(0, -1)} #${item.id}`}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 mt-1\">\n                          {activeTab === 'locations' && `${item.address} • ${item.phone}`}\n                          {activeTab === 'services' && `${item.category} • ${item.duration}min • ${item.price} RWF`}\n                          {activeTab === 'appointments' && `${item.appointment_date} ${item.appointment_time} • ${item.status}`}\n                          {activeTab === 'users' && `${item.phone} • ${item.role}`}\n                          {activeTab === 'queues' && `Position #${item.position} • ${item.status}`}\n                        </p>\n                      </div>\n                      <div className=\"flex space-x-2 ml-4\">\n                        <button\n                          onClick={() => handleView(item)}\n                          className=\"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors\"\n                          title=\"View\"\n                        >\n                          <EyeIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleEdit(item)}\n                          className=\"p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors\"\n                          title=\"Edit\"\n                        >\n                          <PencilIcon className=\"w-4 h-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(item.id)}\n                          className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n                          title=\"Delete\"\n                        >\n                          <TrashIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </motion.div>\n\n      {/* Simple Modal for CRUD operations */}\n      {showModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\"\n          >\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              {modalMode === 'create' ? 'Create' : modalMode === 'edit' ? 'Edit' : 'View'} {activeTab.slice(0, -1)}\n            </h3>\n\n            <div className=\"space-y-4\">\n              {selectedItem && (\n                <div className=\"text-sm text-gray-600\">\n                  <pre className=\"whitespace-pre-wrap bg-gray-50 p-3 rounded-lg\">\n                    {JSON.stringify(selectedItem, null, 2)}\n                  </pre>\n                </div>\n              )}\n\n              {modalMode !== 'view' && (\n                <div className=\"text-center py-4\">\n                  <p className=\"text-gray-500\">\n                    Full form interface will be implemented here for {modalMode} operations.\n                  </p>\n                  <p className=\"text-sm text-gray-400 mt-2\">\n                    For now, you can test the API endpoints directly.\n                  </p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex gap-3 mt-6\">\n              <button\n                onClick={() => setShowModal(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Close\n              </button>\n              {modalMode !== 'view' && (\n                <button\n                  onClick={() => {\n                    // For demo, just close modal\n                    setShowModal(false);\n                    toast.success(`${modalMode} operation would be performed here`);\n                  }}\n                  className=\"flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n                >\n                  {modalMode === 'create' ? 'Create' : 'Update'}\n                </button>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,kBAAkB,EAClBC,qBAAqB,EACrBC,gBAAgB,EAChBC,SAAS,EACTC,SAAS,QACJ,6BAA6B;AACpC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACsB,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC;IAC/BwB,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMuC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAElC,kBAAkB;IAAEmC,KAAK,EAAE;EAAO,CAAC,EAC/E;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEjC,qBAAqB;IAAEkC,KAAK,EAAE;EAAQ,CAAC,EACjF;IAAEH,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAEhC,gBAAgB;IAAEiC,KAAK,EAAE;EAAS,CAAC,EACrF;IAAEH,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE/B,SAAS;IAAEgC,KAAK,EAAE;EAAS,CAAC,EAChE;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE9B,SAAS;IAAE+B,KAAK,EAAE;EAAS,CAAC,CACnE;EAED1C,SAAS,CAAC,MAAM;IACd2C,SAAS,CAACxB,SAAS,CAAC;EACtB,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMwB,SAAS,GAAG,MAAOC,IAAI,IAAK;IAChCf,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,QAAQF,IAAI,EAAE,CAAC;MAChDtB,OAAO,CAACyB,IAAI,KAAK;QACf,GAAGA,IAAI;QACP,CAACH,IAAI,GAAGC,QAAQ,CAACxB,IAAI,CAACA,IAAI,CAACuB,IAAI,CAAC,IAAIC,QAAQ,CAACxB,IAAI,CAACA;MACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmBJ,IAAI,GAAG,EAAEI,KAAK,CAAC;MAChDnC,KAAK,CAACmC,KAAK,CAAC,kBAAkBJ,IAAI,EAAE,CAAC;IACvC,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzBf,YAAY,CAAC,QAAQ,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrBJ,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMkB,UAAU,GAAIC,IAAI,IAAK;IAC3BjB,YAAY,CAAC,MAAM,CAAC;IACpBE,eAAe,CAACe,IAAI,CAAC;IACrBnB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoB,UAAU,GAAID,IAAI,IAAK;IAC3BjB,YAAY,CAAC,MAAM,CAAC;IACpBE,eAAe,CAACe,IAAI,CAAC;IACrBnB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAOf,EAAE,IAAK;IACjC,IAAI,CAACgB,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM5C,KAAK,CAAC6C,MAAM,CAAC,QAAQtC,SAAS,IAAIoB,EAAE,EAAE,CAAC;MAC7C1B,KAAK,CAAC6C,OAAO,CAAC,2BAA2B,CAAC;MAC1Cf,SAAS,CAACxB,SAAS,CAAC;IACtB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACd/C,KAAK,CAACmC,KAAK,CAAC,EAAAW,eAAA,GAAAX,KAAK,CAACH,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBtC,IAAI,cAAAuC,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,uBAAuB,CAAC;IACvE;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACrC,IAAI;MACF,IAAI7B,SAAS,KAAK,QAAQ,EAAE;QAC1B,MAAMtB,KAAK,CAACoD,IAAI,CAAC,QAAQ7C,SAAS,EAAE,EAAE4C,QAAQ,CAAC;QAC/ClD,KAAK,CAAC6C,OAAO,CAAC,2BAA2B,CAAC;MAC5C,CAAC,MAAM,IAAIxB,SAAS,KAAK,MAAM,EAAE;QAC/B,MAAMtB,KAAK,CAACqD,GAAG,CAAC,QAAQ9C,SAAS,IAAIiB,YAAY,CAACG,EAAE,EAAE,EAAEwB,QAAQ,CAAC;QACjElD,KAAK,CAAC6C,OAAO,CAAC,2BAA2B,CAAC;MAC5C;MACAzB,YAAY,CAAC,KAAK,CAAC;MACnBU,SAAS,CAACxB,SAAS,CAAC;IACtB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAAkB,gBAAA,EAAAC,qBAAA;MACdtD,KAAK,CAACmC,KAAK,CAAC,EAAAkB,gBAAA,GAAAlB,KAAK,CAACH,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7C,IAAI,cAAA8C,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,qBAAqB,CAAC;IACrE;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,EAAAlD,eAAA,GAAAG,IAAI,CAACF,SAAS,CAAC,cAAAD,eAAA,uBAAfA,eAAA,CAAiBmD,MAAM,CAACjB,IAAI,IAAI;IACnD,MAAMkB,YAAY,GAAGC,eAAe,CAACpD,SAAS,EAAEiC,IAAI,CAAC;IACrD,OAAOkB,YAAY,CAACE,IAAI,CAACC,KAAK,IAC5BA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7C,UAAU,CAAC4C,WAAW,CAAC,CAAC,CACxD,CAAC;EACH,CAAC,CAAC,KAAI,EAAE;EAER,MAAMH,eAAe,GAAGA,CAAC3B,IAAI,EAAEQ,IAAI,KAAK;IACtC,QAAQR,IAAI;MACV,KAAK,WAAW;QACd,OAAO,CAACQ,IAAI,CAACZ,IAAI,EAAEY,IAAI,CAACwB,OAAO,EAAExB,IAAI,CAACyB,KAAK,EAAEzB,IAAI,CAAC0B,KAAK,CAAC;MAC1D,KAAK,UAAU;QACb,OAAO,CAAC1B,IAAI,CAACZ,IAAI,EAAEY,IAAI,CAAC2B,WAAW,EAAE3B,IAAI,CAAC4B,QAAQ,CAAC;MACrD,KAAK,cAAc;QACjB,OAAO,CAAC5B,IAAI,CAAC6B,YAAY,EAAE7B,IAAI,CAAC8B,aAAa,EAAE9B,IAAI,CAAC+B,KAAK,CAAC;MAC5D,KAAK,OAAO;QACV,OAAO,CAAC/B,IAAI,CAACgC,UAAU,EAAEhC,IAAI,CAACiC,SAAS,EAAEjC,IAAI,CAAC0B,KAAK,EAAE1B,IAAI,CAACyB,KAAK,CAAC;MAClE,KAAK,QAAQ;QACX,OAAO,CAACzB,IAAI,CAAC6B,YAAY,EAAE7B,IAAI,CAAC8B,aAAa,CAAC;MAChD;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,oBACEnE,OAAA;IAAKuE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBxE,OAAA,CAACd,MAAM,CAACuF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAExExE,OAAA;QAAAwE,QAAA,gBACExE,OAAA;UAAIuE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEjF,OAAA;UAAGuE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAsD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CAAC,eACNjF,OAAA;QACEkF,OAAO,EAAE/C,YAAa;QACtBoC,SAAS,EAAC,+IAA+I;QAAAC,QAAA,gBAEzJxE,OAAA,CAACb,QAAQ;UAACoF,SAAS,EAAC;QAAc;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,YAC7B,EAAC7E,SAAS,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGbjF,OAAA,CAACd,MAAM,CAACuF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3Bd,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eAEpCxE,OAAA;QAAKuE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDjD,IAAI,CAAC+D,GAAG,CAAEC,GAAG,IAAK;UACjB,MAAMC,IAAI,GAAGD,GAAG,CAAC7D,IAAI;UACrB,oBACE1B,OAAA;YAEEkF,OAAO,EAAEA,CAAA,KAAM7E,YAAY,CAACkF,GAAG,CAAC/D,EAAE,CAAE;YACpC+C,SAAS,EAAE,gFACTnE,SAAS,KAAKmF,GAAG,CAAC/D,EAAE,GAChB,qCAAqC,GACrC,4EAA4E,EAC/E;YAAAgD,QAAA,gBAEHxE,OAAA,CAACwF,IAAI;cAACjB,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChCM,GAAG,CAAC9D,IAAI;UAAA,GATJ8D,GAAG,CAAC/D,EAAE;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUL,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbjF,OAAA,CAACd,MAAM,CAACuF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3Bd,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAEpBxE,OAAA;QAAKuE,SAAS,EAAC,sEAAsE;QAAAC,QAAA,eACnFxE,OAAA,CAACT,mBAAmB;UAACgF,SAAS,EAAC;QAAuB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACNjF,OAAA;QACE6B,IAAI,EAAC,MAAM;QACX4D,WAAW,EAAE,UAAUrF,SAAS,KAAM;QACtCsF,KAAK,EAAE3E,UAAW;QAClB4E,QAAQ,EAAGC,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CnB,SAAS,EAAC;MAAgJ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3J,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGbjF,OAAA,CAACd,MAAM,CAACuF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BQ,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3Bd,SAAS,EAAC,qCAAqC;MAAAC,QAAA,EAE9C3D,OAAO,gBACNb,OAAA;QAAKuE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDxE,OAAA;UAAKuE,SAAS,EAAC;QAAoF;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,gBAENjF,OAAA;QAAKuE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxE,OAAA;UAAIuE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAC3DpE,SAAS,EAAC,eAAa,EAACiD,YAAY,CAACyC,MAAM,EAAC,SAC/C;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJ5B,YAAY,CAACyC,MAAM,KAAK,CAAC,gBACxB9F,OAAA;UAAKuE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxE,OAAA;YAAGuE,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,KAAG,EAACpE,SAAS,EAAC,QAAM;UAAA;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDjF,OAAA;YACEkF,OAAO,EAAE/C,YAAa;YACtBoC,SAAS,EAAC,qHAAqH;YAAAC,QAAA,GAChI,eACc,EAACpE,SAAS,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENjF,OAAA;UAAKuE,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBnB,YAAY,CAACiC,GAAG,CAAEjD,IAAI,iBACrBrC,OAAA;YAAmBuE,SAAS,EAAC,yEAAyE;YAAAC,QAAA,eACpGxE,OAAA;cAAKuE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDxE,OAAA;gBAAKuE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBxE,OAAA;kBAAIuE,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EACtCnC,IAAI,CAACZ,IAAI,IAAI,GAAGY,IAAI,CAACgC,UAAU,IAAIhC,IAAI,CAACiC,SAAS,EAAE,IAAIjC,IAAI,CAAC6B,YAAY,IAAI,GAAG9D,SAAS,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK9C,IAAI,CAACb,EAAE;gBAAE;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH,CAAC,eACLjF,OAAA;kBAAGuE,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GACtCpE,SAAS,KAAK,WAAW,IAAI,GAAGiC,IAAI,CAACwB,OAAO,MAAMxB,IAAI,CAACyB,KAAK,EAAE,EAC9D1D,SAAS,KAAK,UAAU,IAAI,GAAGiC,IAAI,CAAC4B,QAAQ,MAAM5B,IAAI,CAAC0D,QAAQ,SAAS1D,IAAI,CAAC2D,KAAK,MAAM,EACxF5F,SAAS,KAAK,cAAc,IAAI,GAAGiC,IAAI,CAAC4D,gBAAgB,IAAI5D,IAAI,CAAC6D,gBAAgB,MAAM7D,IAAI,CAAC8D,MAAM,EAAE,EACpG/F,SAAS,KAAK,OAAO,IAAI,GAAGiC,IAAI,CAACyB,KAAK,MAAMzB,IAAI,CAAC+D,IAAI,EAAE,EACvDhG,SAAS,KAAK,QAAQ,IAAI,aAAaiC,IAAI,CAACgE,QAAQ,MAAMhE,IAAI,CAAC8D,MAAM,EAAE;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNjF,OAAA;gBAAKuE,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCxE,OAAA;kBACEkF,OAAO,EAAEA,CAAA,KAAM5C,UAAU,CAACD,IAAI,CAAE;kBAChCkC,SAAS,EAAC,iEAAiE;kBAC3E+B,KAAK,EAAC,MAAM;kBAAA9B,QAAA,eAEZxE,OAAA,CAACV,OAAO;oBAACiF,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACTjF,OAAA;kBACEkF,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAACC,IAAI,CAAE;kBAChCkC,SAAS,EAAC,qEAAqE;kBAC/E+B,KAAK,EAAC,MAAM;kBAAA9B,QAAA,eAEZxE,OAAA,CAACZ,UAAU;oBAACmF,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACTjF,OAAA;kBACEkF,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAACF,IAAI,CAACb,EAAE,CAAE;kBACrC+C,SAAS,EAAC,+DAA+D;kBACzE+B,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,eAEdxE,OAAA,CAACX,SAAS;oBAACkF,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GArCE5C,IAAI,CAACb,EAAE;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsCZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,EAGZhE,SAAS,iBACRjB,OAAA;MAAKuE,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5FxE,OAAA,CAACd,MAAM,CAACuF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAE4B,KAAK,EAAE;QAAK,CAAE;QACrC1B,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAE4B,KAAK,EAAE;QAAE,CAAE;QAClChC,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAE/FxE,OAAA;UAAIuE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GACrDrD,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAGA,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,EAAC,GAAC,EAACf,SAAS,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,eAELjF,OAAA;UAAKuE,SAAS,EAAC,WAAW;UAAAC,QAAA,GACvBnD,YAAY,iBACXrB,OAAA;YAAKuE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCxE,OAAA;cAAKuE,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAC3DgC,IAAI,CAACC,SAAS,CAACpF,YAAY,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA9D,SAAS,KAAK,MAAM,iBACnBnB,OAAA;YAAKuE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BxE,OAAA;cAAGuE,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,mDACsB,EAACrD,SAAS,EAAC,cAC9D;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJjF,OAAA;cAAGuE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjF,OAAA;UAAKuE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BxE,OAAA;YACEkF,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC,KAAK,CAAE;YACnCqD,SAAS,EAAC,qGAAqG;YAAAC,QAAA,EAChH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACR9D,SAAS,KAAK,MAAM,iBACnBnB,OAAA;YACEkF,OAAO,EAAEA,CAAA,KAAM;cACb;cACAhE,YAAY,CAAC,KAAK,CAAC;cACnBpB,KAAK,CAAC6C,OAAO,CAAC,GAAGxB,SAAS,oCAAoC,CAAC;YACjE,CAAE;YACFoD,SAAS,EAAC,8FAA8F;YAAAC,QAAA,EAEvGrD,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG;UAAQ;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA3TID,cAAc;AAAAyG,EAAA,GAAdzG,cAAc;AA6TpB,eAAeA,cAAc;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}