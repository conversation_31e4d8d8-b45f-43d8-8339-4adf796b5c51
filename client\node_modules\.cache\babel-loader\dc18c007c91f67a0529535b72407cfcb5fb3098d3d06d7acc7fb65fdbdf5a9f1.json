{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\pages\\\\AppointmentsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useApp } from '../contexts/AppContext';\nimport { CalendarDaysIcon, ClockIcon, MapPinIcon, XMarkIcon, CheckIcon, ExclamationTriangleIcon, PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';\nimport { format, parseISO, isToday, isTomorrow, isPast } from 'date-fns';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppointmentsPage = () => {\n  _s();\n  const {\n    appointments,\n    fetchAppointments,\n    cancelAppointment,\n    loading\n  } = useApp();\n  const [filter, setFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showCancelModal, setShowCancelModal] = useState(false);\n  const [selectedAppointment, setSelectedAppointment] = useState(null);\n  useEffect(() => {\n    fetchAppointments();\n  }, []);\n\n  // Filter appointments based on status and search\n  const filteredAppointments = appointments.filter(appointment => {\n    var _appointment$service_, _appointment$location;\n    const matchesFilter = filter === 'all' || appointment.status === filter;\n    const matchesSearch = ((_appointment$service_ = appointment.service_name) === null || _appointment$service_ === void 0 ? void 0 : _appointment$service_.toLowerCase().includes(searchTerm.toLowerCase())) || ((_appointment$location = appointment.location_name) === null || _appointment$location === void 0 ? void 0 : _appointment$location.toLowerCase().includes(searchTerm.toLowerCase()));\n    return matchesFilter && matchesSearch;\n  });\n\n  // Group appointments by status\n  const upcomingAppointments = appointments.filter(apt => apt.status === 'confirmed' && new Date(apt.appointment_date) >= new Date());\n  const pastAppointments = appointments.filter(apt => ['completed', 'cancelled'].includes(apt.status));\n  const pendingAppointments = appointments.filter(apt => apt.status === 'pending');\n  const handleCancelAppointment = async () => {\n    if (!selectedAppointment) return;\n    const result = await cancelAppointment(selectedAppointment.id);\n    if (result.success) {\n      setShowCancelModal(false);\n      setSelectedAppointment(null);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      case 'in_progress':\n        return 'bg-purple-100 text-purple-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getDateLabel = dateString => {\n    const date = parseISO(dateString);\n    if (isToday(date)) return 'Today';\n    if (isTomorrow(date)) return 'Tomorrow';\n    return format(date, 'MMM dd, yyyy');\n  };\n  const canCancelAppointment = appointment => {\n    return ['confirmed', 'pending'].includes(appointment.status) && !isPast(parseISO(appointment.appointment_date));\n  };\n  if (loading && appointments.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"My Appointments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2\",\n          children: \"Manage your appointments and view booking history.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 sm:mt-0\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/services',\n          className: \"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), \"Book New Appointment\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6,\n        delay: 0.1\n      },\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-soft p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-100 p-3 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(CalendarDaysIcon, {\n              className: \"w-6 h-6 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: appointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-soft p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-100 p-3 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n              className: \"w-6 h-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Upcoming\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: upcomingAppointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-soft p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-yellow-100 p-3 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n              className: \"w-6 h-6 text-yellow-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: pendingAppointments.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-soft p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-purple-100 p-3 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n              className: \"w-6 h-6 text-purple-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: appointments.filter(apt => apt.status === 'completed').length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6,\n        delay: 0.2\n      },\n      className: \"bg-white rounded-xl shadow-soft p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search appointments...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:w-48\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filter,\n            onChange: e => setFilter(e.target.value),\n            className: \"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"confirmed\",\n              children: \"Confirmed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"pending\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"completed\",\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"cancelled\",\n              children: \"Cancelled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6,\n        delay: 0.3\n      },\n      children: filteredAppointments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-soft p-12 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(CalendarDaysIcon, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: appointments.length === 0 ? 'No Appointments Yet' : 'No Matching Appointments'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: appointments.length === 0 ? 'Start by booking your first appointment with one of our services.' : 'Try adjusting your search or filter criteria.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), appointments.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/services',\n          className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 inline-flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this), \"Book Your First Appointment\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: filteredAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.4,\n            delay: index * 0.1\n          },\n          className: \"bg-white rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: appointment.service_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`,\n                  children: appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(CalendarDaysIcon, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: getDateLabel(appointment.appointment_date)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: appointment.appointment_time\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(MapPinIcon, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: appointment.location_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this), appointment.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3 p-3 bg-gray-50 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-700\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Notes:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 27\n                  }, this), \" \", appointment.notes]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 23\n              }, this), appointment.created_at && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-2\",\n                children: [\"Booked on \", format(parseISO(appointment.created_at), 'MMM dd, yyyy')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4 flex flex-col gap-2\",\n              children: [canCancelAppointment(appointment) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSelectedAppointment(appointment);\n                  setShowCancelModal(true);\n                },\n                className: \"px-3 py-1 text-sm text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 23\n              }, this), appointment.status === 'confirmed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-3 py-1 text-sm text-primary-600 border border-primary-300 rounded-lg hover:bg-primary-50 transition-colors\",\n                children: \"Reschedule\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this)\n        }, appointment.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), showCancelModal && selectedAppointment && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-100 p-2 rounded-lg mr-3\",\n            children: /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n              className: \"w-6 h-6 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Cancel Appointment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: [\"Are you sure you want to cancel your appointment for\", ' ', /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: selectedAppointment.service_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), \" on\", ' ', /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: getDateLabel(selectedAppointment.appointment_date)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this), \" at\", ' ', /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: selectedAppointment.appointment_time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), \"?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500 mb-6\",\n          children: \"This action cannot be undone. You'll need to book a new appointment if you change your mind.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCancelModal(false),\n            className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n            children: \"Keep Appointment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCancelAppointment,\n            disabled: loading,\n            className: \"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50\",\n            children: loading ? 'Cancelling...' : 'Cancel Appointment'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n_s(AppointmentsPage, \"bquN6nr1ExAApEielixjGjQPw/o=\", false, function () {\n  return [useApp];\n});\n_c = AppointmentsPage;\nexport default AppointmentsPage;\nvar _c;\n$RefreshReg$(_c, \"AppointmentsPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "useApp", "CalendarDaysIcon", "ClockIcon", "MapPinIcon", "XMarkIcon", "CheckIcon", "ExclamationTriangleIcon", "PlusIcon", "MagnifyingGlassIcon", "format", "parseISO", "isToday", "isTomorrow", "isPast", "toast", "jsxDEV", "_jsxDEV", "AppointmentsPage", "_s", "appointments", "fetchAppointments", "cancelAppointment", "loading", "filter", "setFilter", "searchTerm", "setSearchTerm", "showCancelModal", "setShowCancelModal", "selectedAppointment", "setSelectedAppointment", "filteredAppointments", "appointment", "_appointment$service_", "_appointment$location", "matchesFilter", "status", "matchesSearch", "service_name", "toLowerCase", "includes", "location_name", "upcomingAppointments", "apt", "Date", "appointment_date", "pastAppointments", "pendingAppointments", "handleCancelAppointment", "result", "id", "success", "getStatusColor", "getDateLabel", "dateString", "date", "canCancelAppointment", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "onClick", "window", "location", "href", "delay", "type", "placeholder", "value", "onChange", "e", "target", "map", "index", "char<PERSON>t", "toUpperCase", "slice", "appointment_time", "notes", "created_at", "scale", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/pages/AppointmentsPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useApp } from '../contexts/AppContext';\nimport {\n  CalendarDaysIcon,\n  ClockIcon,\n  MapPinIcon,\n  XMarkIcon,\n  CheckIcon,\n  ExclamationTriangleIcon,\n  PlusIcon,\n  MagnifyingGlassIcon\n} from '@heroicons/react/24/outline';\nimport { format, parseISO, isToday, isTomorrow, isPast } from 'date-fns';\nimport toast from 'react-hot-toast';\n\nconst AppointmentsPage = () => {\n  const {\n    appointments,\n    fetchAppointments,\n    cancelAppointment,\n    loading\n  } = useApp();\n\n  const [filter, setFilter] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showCancelModal, setShowCancelModal] = useState(false);\n  const [selectedAppointment, setSelectedAppointment] = useState(null);\n\n  useEffect(() => {\n    fetchAppointments();\n  }, []);\n\n  // Filter appointments based on status and search\n  const filteredAppointments = appointments.filter(appointment => {\n    const matchesFilter = filter === 'all' || appointment.status === filter;\n    const matchesSearch = appointment.service_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         appointment.location_name?.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesFilter && matchesSearch;\n  });\n\n  // Group appointments by status\n  const upcomingAppointments = appointments.filter(apt =>\n    apt.status === 'confirmed' && new Date(apt.appointment_date) >= new Date()\n  );\n  const pastAppointments = appointments.filter(apt =>\n    ['completed', 'cancelled'].includes(apt.status)\n  );\n  const pendingAppointments = appointments.filter(apt =>\n    apt.status === 'pending'\n  );\n\n  const handleCancelAppointment = async () => {\n    if (!selectedAppointment) return;\n\n    const result = await cancelAppointment(selectedAppointment.id);\n    if (result.success) {\n      setShowCancelModal(false);\n      setSelectedAppointment(null);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-green-100 text-green-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      case 'in_progress':\n        return 'bg-purple-100 text-purple-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getDateLabel = (dateString) => {\n    const date = parseISO(dateString);\n    if (isToday(date)) return 'Today';\n    if (isTomorrow(date)) return 'Tomorrow';\n    return format(date, 'MMM dd, yyyy');\n  };\n\n  const canCancelAppointment = (appointment) => {\n    return ['confirmed', 'pending'].includes(appointment.status) &&\n           !isPast(parseISO(appointment.appointment_date));\n  };\n\n  if (loading && appointments.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\"\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">My Appointments</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Manage your appointments and view booking history.\n          </p>\n        </div>\n        <div className=\"mt-4 sm:mt-0\">\n          <button\n            onClick={() => window.location.href = '/services'}\n            className=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center\"\n          >\n            <PlusIcon className=\"w-4 h-4 mr-2\" />\n            Book New Appointment\n          </button>\n        </div>\n      </motion.div>\n\n      {/* Stats Cards */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.1 }}\n        className=\"grid grid-cols-1 md:grid-cols-4 gap-6\"\n      >\n        <div className=\"bg-white rounded-xl shadow-soft p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-blue-100 p-3 rounded-lg\">\n              <CalendarDaysIcon className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{appointments.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-soft p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-green-100 p-3 rounded-lg\">\n              <CheckIcon className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Upcoming</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{upcomingAppointments.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-soft p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-yellow-100 p-3 rounded-lg\">\n              <ClockIcon className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{pendingAppointments.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-soft p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-purple-100 p-3 rounded-lg\">\n              <CheckIcon className=\"w-6 h-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {appointments.filter(apt => apt.status === 'completed').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Filters and Search */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.2 }}\n        className=\"bg-white rounded-xl shadow-soft p-6\"\n      >\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search appointments...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Status Filter */}\n          <div className=\"sm:w-48\">\n            <select\n              value={filter}\n              onChange={(e) => setFilter(e.target.value)}\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"confirmed\">Confirmed</option>\n              <option value=\"pending\">Pending</option>\n              <option value=\"completed\">Completed</option>\n              <option value=\"cancelled\">Cancelled</option>\n            </select>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Appointments List */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.3 }}\n      >\n        {filteredAppointments.length === 0 ? (\n          <div className=\"bg-white rounded-xl shadow-soft p-12 text-center\">\n            <CalendarDaysIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {appointments.length === 0 ? 'No Appointments Yet' : 'No Matching Appointments'}\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              {appointments.length === 0\n                ? 'Start by booking your first appointment with one of our services.'\n                : 'Try adjusting your search or filter criteria.'\n              }\n            </p>\n            {appointments.length === 0 && (\n              <button\n                onClick={() => window.location.href = '/services'}\n                className=\"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 inline-flex items-center\"\n              >\n                <PlusIcon className=\"w-4 h-4 mr-2\" />\n                Book Your First Appointment\n              </button>\n            )}\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {filteredAppointments.map((appointment, index) => (\n              <motion.div\n                key={appointment.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.4, delay: index * 0.1 }}\n                className=\"bg-white rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-3 mb-2\">\n                      <h3 className=\"text-lg font-semibold text-gray-900\">\n                        {appointment.service_name}\n                      </h3>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>\n                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}\n                      </span>\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <CalendarDaysIcon className=\"w-4 h-4 mr-2\" />\n                        <span>{getDateLabel(appointment.appointment_date)}</span>\n                      </div>\n\n                      <div className=\"flex items-center\">\n                        <ClockIcon className=\"w-4 h-4 mr-2\" />\n                        <span>{appointment.appointment_time}</span>\n                      </div>\n\n                      <div className=\"flex items-center\">\n                        <MapPinIcon className=\"w-4 h-4 mr-2\" />\n                        <span>{appointment.location_name}</span>\n                      </div>\n                    </div>\n\n                    {appointment.notes && (\n                      <div className=\"mt-3 p-3 bg-gray-50 rounded-lg\">\n                        <p className=\"text-sm text-gray-700\">\n                          <strong>Notes:</strong> {appointment.notes}\n                        </p>\n                      </div>\n                    )}\n\n                    {appointment.created_at && (\n                      <p className=\"text-xs text-gray-500 mt-2\">\n                        Booked on {format(parseISO(appointment.created_at), 'MMM dd, yyyy')}\n                      </p>\n                    )}\n                  </div>\n\n                  <div className=\"ml-4 flex flex-col gap-2\">\n                    {canCancelAppointment(appointment) && (\n                      <button\n                        onClick={() => {\n                          setSelectedAppointment(appointment);\n                          setShowCancelModal(true);\n                        }}\n                        className=\"px-3 py-1 text-sm text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors\"\n                      >\n                        Cancel\n                      </button>\n                    )}\n\n                    {appointment.status === 'confirmed' && (\n                      <button className=\"px-3 py-1 text-sm text-primary-600 border border-primary-300 rounded-lg hover:bg-primary-50 transition-colors\">\n                        Reschedule\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        )}\n      </motion.div>\n\n      {/* Cancel Confirmation Modal */}\n      {showCancelModal && selectedAppointment && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"bg-red-100 p-2 rounded-lg mr-3\">\n                <ExclamationTriangleIcon className=\"w-6 h-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">\n                Cancel Appointment\n              </h3>\n            </div>\n\n            <p className=\"text-gray-600 mb-4\">\n              Are you sure you want to cancel your appointment for{' '}\n              <strong>{selectedAppointment.service_name}</strong> on{' '}\n              <strong>{getDateLabel(selectedAppointment.appointment_date)}</strong> at{' '}\n              <strong>{selectedAppointment.appointment_time}</strong>?\n            </p>\n\n            <p className=\"text-sm text-gray-500 mb-6\">\n              This action cannot be undone. You'll need to book a new appointment if you change your mind.\n            </p>\n\n            <div className=\"flex gap-3\">\n              <button\n                onClick={() => setShowCancelModal(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Keep Appointment\n              </button>\n              <button\n                onClick={handleCancelAppointment}\n                disabled={loading}\n                className=\"flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50\"\n              >\n                {loading ? 'Cancelling...' : 'Cancel Appointment'}\n              </button>\n            </div>\n          </motion.div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AppointmentsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SACEC,gBAAgB,EAChBC,SAAS,EACTC,UAAU,EACVC,SAAS,EACTC,SAAS,EACTC,uBAAuB,EACvBC,QAAQ,EACRC,mBAAmB,QACd,6BAA6B;AACpC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,QAAQ,UAAU;AACxE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IACJC,YAAY;IACZC,iBAAiB;IACjBC,iBAAiB;IACjBC;EACF,CAAC,GAAGtB,MAAM,CAAC,CAAC;EAEZ,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAEpED,SAAS,CAAC,MAAM;IACduB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,oBAAoB,GAAGZ,YAAY,CAACI,MAAM,CAACS,WAAW,IAAI;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IAC9D,MAAMC,aAAa,GAAGZ,MAAM,KAAK,KAAK,IAAIS,WAAW,CAACI,MAAM,KAAKb,MAAM;IACvE,MAAMc,aAAa,GAAG,EAAAJ,qBAAA,GAAAD,WAAW,CAACM,YAAY,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,CAAC,CAAC,CAAC,OAAAL,qBAAA,GAC3EF,WAAW,CAACS,aAAa,cAAAP,qBAAA,uBAAzBA,qBAAA,CAA2BK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,CAAC,CAAC,CAAC;IAChG,OAAOJ,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMK,oBAAoB,GAAGvB,YAAY,CAACI,MAAM,CAACoB,GAAG,IAClDA,GAAG,CAACP,MAAM,KAAK,WAAW,IAAI,IAAIQ,IAAI,CAACD,GAAG,CAACE,gBAAgB,CAAC,IAAI,IAAID,IAAI,CAAC,CAC3E,CAAC;EACD,MAAME,gBAAgB,GAAG3B,YAAY,CAACI,MAAM,CAACoB,GAAG,IAC9C,CAAC,WAAW,EAAE,WAAW,CAAC,CAACH,QAAQ,CAACG,GAAG,CAACP,MAAM,CAChD,CAAC;EACD,MAAMW,mBAAmB,GAAG5B,YAAY,CAACI,MAAM,CAACoB,GAAG,IACjDA,GAAG,CAACP,MAAM,KAAK,SACjB,CAAC;EAED,MAAMY,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAACnB,mBAAmB,EAAE;IAE1B,MAAMoB,MAAM,GAAG,MAAM5B,iBAAiB,CAACQ,mBAAmB,CAACqB,EAAE,CAAC;IAC9D,IAAID,MAAM,CAACE,OAAO,EAAE;MAClBvB,kBAAkB,CAAC,KAAK,CAAC;MACzBE,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMsB,cAAc,GAAIhB,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,6BAA6B;MACtC,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,WAAW;QACd,OAAO,yBAAyB;MAClC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,aAAa;QAChB,OAAO,+BAA+B;MACxC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMiB,YAAY,GAAIC,UAAU,IAAK;IACnC,MAAMC,IAAI,GAAG7C,QAAQ,CAAC4C,UAAU,CAAC;IACjC,IAAI3C,OAAO,CAAC4C,IAAI,CAAC,EAAE,OAAO,OAAO;IACjC,IAAI3C,UAAU,CAAC2C,IAAI,CAAC,EAAE,OAAO,UAAU;IACvC,OAAO9C,MAAM,CAAC8C,IAAI,EAAE,cAAc,CAAC;EACrC,CAAC;EAED,MAAMC,oBAAoB,GAAIxB,WAAW,IAAK;IAC5C,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,CAACQ,QAAQ,CAACR,WAAW,CAACI,MAAM,CAAC,IACrD,CAACvB,MAAM,CAACH,QAAQ,CAACsB,WAAW,CAACa,gBAAgB,CAAC,CAAC;EACxD,CAAC;EAED,IAAIvB,OAAO,IAAIH,YAAY,CAACsC,MAAM,KAAK,CAAC,EAAE;IACxC,oBACEzC,OAAA;MAAK0C,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD3C,OAAA;QAAK0C,SAAS,EAAC;MAAoF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CAAC;EAEV;EAEA,oBACE/C,OAAA;IAAK0C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB3C,OAAA,CAACjB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BZ,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAExE3C,OAAA;QAAA2C,QAAA,gBACE3C,OAAA;UAAI0C,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE/C,OAAA;UAAG0C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/C,OAAA;QAAK0C,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B3C,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAY;UAClDhB,SAAS,EAAC,kIAAkI;UAAAC,QAAA,gBAE5I3C,OAAA,CAACT,QAAQ;YAACmD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEK,KAAK,EAAE;MAAI,CAAE;MAC1CjB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEjD3C,OAAA;QAAK0C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD3C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3C,OAAA;YAAK0C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC3C,OAAA,CAACf,gBAAgB;cAACyD,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB3C,OAAA;cAAG0C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D/C,OAAA;cAAG0C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAExC,YAAY,CAACsC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA;QAAK0C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD3C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3C,OAAA;YAAK0C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C3C,OAAA,CAACX,SAAS;cAACqD,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB3C,OAAA;cAAG0C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7D/C,OAAA;cAAG0C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEjB,oBAAoB,CAACe;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA;QAAK0C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD3C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3C,OAAA;YAAK0C,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C3C,OAAA,CAACd,SAAS;cAACwD,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB3C,OAAA;cAAG0C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5D/C,OAAA;cAAG0C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEZ,mBAAmB,CAACU;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA;QAAK0C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClD3C,OAAA;UAAK0C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3C,OAAA;YAAK0C,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3C3C,OAAA,CAACX,SAAS;cAACqD,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB3C,OAAA;cAAG0C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D/C,OAAA;cAAG0C,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC5CxC,YAAY,CAACI,MAAM,CAACoB,GAAG,IAAIA,GAAG,CAACP,MAAM,KAAK,WAAW,CAAC,CAACqB;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEK,KAAK,EAAE;MAAI,CAAE;MAC1CjB,SAAS,EAAC,qCAAqC;MAAAC,QAAA,eAE/C3C,OAAA;QAAK0C,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAE9C3C,OAAA;UAAK0C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B3C,OAAA;YAAK0C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnF3C,OAAA,CAACR,mBAAmB;cAACkD,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN/C,OAAA;YACE4D,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wBAAwB;YACpCC,KAAK,EAAErD,UAAW;YAClBsD,QAAQ,EAAGC,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CpB,SAAS,EAAC;UAAgJ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3J,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/C,OAAA;UAAK0C,SAAS,EAAC,SAAS;UAAAC,QAAA,eACtB3C,OAAA;YACE8D,KAAK,EAAEvD,MAAO;YACdwD,QAAQ,EAAGC,CAAC,IAAKxD,SAAS,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3CpB,SAAS,EAAC,0IAA0I;YAAAC,QAAA,gBAEpJ3C,OAAA;cAAQ8D,KAAK,EAAC,KAAK;cAAAnB,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC/C,OAAA;cAAQ8D,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C/C,OAAA;cAAQ8D,KAAK,EAAC,SAAS;cAAAnB,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC/C,OAAA;cAAQ8D,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C/C,OAAA;cAAQ8D,KAAK,EAAC,WAAW;cAAAnB,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACiE,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEK,KAAK,EAAE;MAAI,CAAE;MAAAhB,QAAA,EAEzC5B,oBAAoB,CAAC0B,MAAM,KAAK,CAAC,gBAChCzC,OAAA;QAAK0C,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/D3C,OAAA,CAACf,gBAAgB;UAACyD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrE/C,OAAA;UAAI0C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EACnDxC,YAAY,CAACsC,MAAM,KAAK,CAAC,GAAG,qBAAqB,GAAG;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACL/C,OAAA;UAAG0C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9BxC,YAAY,CAACsC,MAAM,KAAK,CAAC,GACtB,mEAAmE,GACnE;QAA+C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElD,CAAC,EACH5C,YAAY,CAACsC,MAAM,KAAK,CAAC,iBACxBzC,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAY;UAClDhB,SAAS,EAAC,yIAAyI;UAAAC,QAAA,gBAEnJ3C,OAAA,CAACT,QAAQ;YAACmD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,+BAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAEN/C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB5B,oBAAoB,CAACmD,GAAG,CAAC,CAAClD,WAAW,EAAEmD,KAAK,kBAC3CnE,OAAA,CAACjB,MAAM,CAACiE,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEK,KAAK,EAAEQ,KAAK,GAAG;UAAI,CAAE;UAClDzB,SAAS,EAAC,wFAAwF;UAAAC,QAAA,eAElG3C,OAAA;YAAK0C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C3C,OAAA;cAAK0C,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrB3C,OAAA;gBAAK0C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C3C,OAAA;kBAAI0C,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAChD3B,WAAW,CAACM;gBAAY;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACL/C,OAAA;kBAAM0C,SAAS,EAAE,8CAA8CN,cAAc,CAACpB,WAAW,CAACI,MAAM,CAAC,EAAG;kBAAAuB,QAAA,EACjG3B,WAAW,CAACI,MAAM,CAACgD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrD,WAAW,CAACI,MAAM,CAACkD,KAAK,CAAC,CAAC;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEN/C,OAAA;gBAAK0C,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1E3C,OAAA;kBAAK0C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3C,OAAA,CAACf,gBAAgB;oBAACyD,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7C/C,OAAA;oBAAA2C,QAAA,EAAON,YAAY,CAACrB,WAAW,CAACa,gBAAgB;kBAAC;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eAEN/C,OAAA;kBAAK0C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3C,OAAA,CAACd,SAAS;oBAACwD,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtC/C,OAAA;oBAAA2C,QAAA,EAAO3B,WAAW,CAACuD;kBAAgB;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eAEN/C,OAAA;kBAAK0C,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3C,OAAA,CAACb,UAAU;oBAACuD,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvC/C,OAAA;oBAAA2C,QAAA,EAAO3B,WAAW,CAACS;kBAAa;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL/B,WAAW,CAACwD,KAAK,iBAChBxE,OAAA;gBAAK0C,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C3C,OAAA;kBAAG0C,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBAClC3C,OAAA;oBAAA2C,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC/B,WAAW,CAACwD,KAAK;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EAEA/B,WAAW,CAACyD,UAAU,iBACrBzE,OAAA;gBAAG0C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,GAAC,YAC9B,EAAClD,MAAM,CAACC,QAAQ,CAACsB,WAAW,CAACyD,UAAU,CAAC,EAAE,cAAc,CAAC;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN/C,OAAA;cAAK0C,SAAS,EAAC,0BAA0B;cAAAC,QAAA,GACtCH,oBAAoB,CAACxB,WAAW,CAAC,iBAChChB,OAAA;gBACEuD,OAAO,EAAEA,CAAA,KAAM;kBACbzC,sBAAsB,CAACE,WAAW,CAAC;kBACnCJ,kBAAkB,CAAC,IAAI,CAAC;gBAC1B,CAAE;gBACF8B,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,EAC9G;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,EAEA/B,WAAW,CAACI,MAAM,KAAK,WAAW,iBACjCpB,OAAA;gBAAQ0C,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,EAAC;cAElI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GApED/B,WAAW,CAACkB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqET,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,EAGZpC,eAAe,IAAIE,mBAAmB,iBACrCb,OAAA;MAAK0C,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5F3C,OAAA,CAACjB,MAAM,CAACiE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEwB,KAAK,EAAE;QAAK,CAAE;QACrCtB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEwB,KAAK,EAAE;QAAE,CAAE;QAClChC,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBAElE3C,OAAA;UAAK0C,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC3C,OAAA;YAAK0C,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7C3C,OAAA,CAACV,uBAAuB;cAACoD,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN/C,OAAA;YAAI0C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN/C,OAAA;UAAG0C,SAAS,EAAC,oBAAoB;UAAAC,QAAA,GAAC,sDACoB,EAAC,GAAG,eACxD3C,OAAA;YAAA2C,QAAA,EAAS9B,mBAAmB,CAACS;UAAY;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,OAAG,EAAC,GAAG,eAC1D/C,OAAA;YAAA2C,QAAA,EAASN,YAAY,CAACxB,mBAAmB,CAACgB,gBAAgB;UAAC;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,OAAG,EAAC,GAAG,eAC5E/C,OAAA;YAAA2C,QAAA,EAAS9B,mBAAmB,CAAC0D;UAAgB;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ/C,OAAA;UAAG0C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJ/C,OAAA;UAAK0C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3C,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC,KAAK,CAAE;YACzC8B,SAAS,EAAC,qGAAqG;YAAAC,QAAA,EAChH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/C,OAAA;YACEuD,OAAO,EAAEvB,uBAAwB;YACjC2C,QAAQ,EAAErE,OAAQ;YAClBoC,SAAS,EAAC,0GAA0G;YAAAC,QAAA,EAEnHrC,OAAO,GAAG,eAAe,GAAG;UAAoB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAzWID,gBAAgB;EAAA,QAMhBjB,MAAM;AAAA;AAAA4F,EAAA,GANN3E,gBAAgB;AA2WtB,eAAeA,gBAAgB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}