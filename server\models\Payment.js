const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Payment = sequelize.define('Payment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  payment_reference: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  appointment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'appointments',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'RWF'
  },
  payment_method: {
    type: DataTypes.ENUM('mobile_money', 'card', 'cash', 'bank_transfer'),
    allowNull: false
  },
  payment_provider: {
    type: DataTypes.ENUM('mtn_momo', 'airtel_money', 'visa', 'mastercard', 'cash'),
    allowNull: true
  },
  phone_number: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      is: /^(\+250|250)?[0-9]{9}$/
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },
  transaction_id: {
    type: DataTypes.STRING,
    allowNull: true
  },
  external_reference: {
    type: DataTypes.STRING,
    allowNull: true
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  failure_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  refund_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  refund_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  refund_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'payments',
  indexes: [
    {
      fields: ['appointment_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['payment_method']
    },
    {
      fields: ['payment_reference']
    },
    {
      fields: ['transaction_id']
    }
  ],
  hooks: {
    beforeCreate: async (payment) => {
      if (!payment.payment_reference) {
        const timestamp = Date.now();
        const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        payment.payment_reference = `PAY${timestamp}${randomNum}`;
      }
    }
  }
});

// Instance methods
Payment.prototype.isSuccessful = function() {
  return this.status === 'completed';
};

Payment.prototype.canBeRefunded = function() {
  return this.status === 'completed' && !this.refund_date;
};

Payment.prototype.getRefundableAmount = function() {
  if (!this.canBeRefunded()) return 0;
  return this.amount - (this.refund_amount || 0);
};

// Class methods
Payment.findByReference = function(reference) {
  return this.findOne({ where: { payment_reference: reference } });
};

Payment.findByTransactionId = function(transactionId) {
  return this.findOne({ where: { transaction_id: transactionId } });
};

module.exports = Payment;
