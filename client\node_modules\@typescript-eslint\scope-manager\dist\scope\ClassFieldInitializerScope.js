"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassFieldInitializerScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class ClassFieldInitializerScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scopeManager, ScopeType_1.ScopeType.classFieldInitializer, upperScope, block, false);
    }
}
exports.ClassFieldInitializerScope = ClassFieldInitializerScope;
//# sourceMappingURL=ClassFieldInitializerScope.js.map