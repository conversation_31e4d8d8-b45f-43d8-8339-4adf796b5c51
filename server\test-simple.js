const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5002;

// Middleware
app.use(cors());
app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Umurongo API is running',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// Simple locations endpoint
app.get('/api/locations', (req, res) => {
  res.json({
    success: true,
    data: {
      locations: [
        {
          id: 1,
          name: 'Test Location',
          address: 'Kigali, Rwanda',
          type: 'health_center'
        }
      ]
    }
  });
});

// Simple appointments endpoint
app.get('/api/appointments', (req, res) => {
  res.json({
    success: true,
    data: {
      appointments: []
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple Umurongo API server running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
