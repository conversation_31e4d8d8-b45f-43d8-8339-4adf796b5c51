{"ast": null, "code": "import startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport addWeeks from \"../addWeeks/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of ISO weeks in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\nexport default function getISOWeeksInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var thisYear = startOfISOWeekYear(dirtyDate);\n  var nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  var diff = nextYear.valueOf() - thisYear.valueOf();\n  // Round the number of weeks to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round(diff / MILLISECONDS_IN_WEEK);\n}", "map": {"version": 3, "names": ["startOfISOWeekYear", "addWeeks", "requiredArgs", "MILLISECONDS_IN_WEEK", "getISOWeeksInYear", "dirtyDate", "arguments", "thisYear", "nextYear", "diff", "valueOf", "Math", "round"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/getISOWeeksInYear/index.js"], "sourcesContent": ["import startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport addWeeks from \"../addWeeks/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of ISO weeks in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\nexport default function getISOWeeksInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var thisYear = startOfISOWeekYear(dirtyDate);\n  var nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  var diff = nextYear.valueOf() - thisYear.valueOf();\n  // Round the number of weeks to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round(diff / MILLISECONDS_IN_WEEK);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,YAAY,MAAM,+BAA+B;AACxD,IAAIC,oBAAoB,GAAG,SAAS;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGP,kBAAkB,CAACK,SAAS,CAAC;EAC5C,IAAIG,QAAQ,GAAGR,kBAAkB,CAACC,QAAQ,CAACM,QAAQ,EAAE,EAAE,CAAC,CAAC;EACzD,IAAIE,IAAI,GAAGD,QAAQ,CAACE,OAAO,CAAC,CAAC,GAAGH,QAAQ,CAACG,OAAO,CAAC,CAAC;EAClD;EACA;EACA;EACA,OAAOC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGN,oBAAoB,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}