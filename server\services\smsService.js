const twilio = require('twilio');

class SMSService {
  constructor() {
    this.client = null;
    this.fromNumber = process.env.TWILIO_PHONE_NUMBER;

    // Only initialize Twilio if credentials are properly configured
    if (process.env.TWILIO_ACCOUNT_SID &&
        process.env.TWILIO_AUTH_TOKEN &&
        process.env.TWILIO_ACCOUNT_SID.startsWith('AC') &&
        process.env.TWILIO_ACCOUNT_SID !== 'your_twilio_account_sid') {
      try {
        this.client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        console.log('📱 SMS service initialized with <PERSON>wi<PERSON>');
      } catch (error) {
        console.log('⚠️  SMS service failed to initialize:', error.message);
      }
    } else {
      console.log('📱 SMS service running in mock mode (Twilio not configured)');
    }
  }

  // Format phone number to international format
  formatPhoneNumber(phone) {
    // Remove any spaces, dashes, or parentheses
    let cleaned = phone.replace(/[\s\-\(\)]/g, '');

    // If it starts with 250, add +
    if (cleaned.startsWith('250')) {
      return '+' + cleaned;
    }

    // If it starts with +250, return as is
    if (cleaned.startsWith('+250')) {
      return cleaned;
    }

    // If it's 9 digits, assume it's a local Rwanda number
    if (cleaned.length === 9) {
      return '+250' + cleaned;
    }

    return cleaned;
  }

  // Send verification code
  async sendVerificationCode(phone, code) {
    const formattedPhone = this.formatPhoneNumber(phone);
    const message = `Your Umurongo verification code is: ${code}. This code will expire in 10 minutes.`;

    return await this.sendSMS(formattedPhone, message);
  }

  // Send appointment confirmation
  async sendAppointmentConfirmation(phone, appointmentDetails) {
    const formattedPhone = this.formatPhoneNumber(phone);
    const { appointmentNumber, serviceName, locationName, date, time } = appointmentDetails;

    const message = `Appointment confirmed!
Ref: ${appointmentNumber}
Service: ${serviceName}
Location: ${locationName}
Date: ${date} at ${time}
Please arrive 15 minutes early.`;

    return await this.sendSMS(formattedPhone, message);
  }

  // Send queue position update
  async sendQueueUpdate(phone, queueDetails) {
    const formattedPhone = this.formatPhoneNumber(phone);
    const { queueNumber, position, estimatedWaitTime, locationName } = queueDetails;

    let message;
    if (position === 1) {
      message = `You are NEXT in line! Queue #${queueNumber} at ${locationName}. Please be ready.`;
    } else if (position <= 3) {
      message = `You are #${position} in line. Queue #${queueNumber} at ${locationName}. Estimated wait: ${estimatedWaitTime} minutes.`;
    } else {
      message = `Queue update: You are #${position} in line. Queue #${queueNumber} at ${locationName}. Estimated wait: ${estimatedWaitTime} minutes.`;
    }

    return await this.sendSMS(formattedPhone, message);
  }

  // Send appointment reminder
  async sendAppointmentReminder(phone, appointmentDetails) {
    const formattedPhone = this.formatPhoneNumber(phone);
    const { appointmentNumber, serviceName, locationName, date, time } = appointmentDetails;

    const message = `Reminder: You have an appointment tomorrow.
Ref: ${appointmentNumber}
Service: ${serviceName}
Location: ${locationName}
Time: ${time}
Please arrive 15 minutes early.`;

    return await this.sendSMS(formattedPhone, message);
  }

  // Send service ready notification
  async sendServiceReady(phone, serviceDetails) {
    const formattedPhone = this.formatPhoneNumber(phone);
    const { queueNumber, roomNumber, locationName } = serviceDetails;

    const message = `Now serving Queue #${queueNumber}! Please proceed to ${roomNumber ? `Room ${roomNumber}` : 'the service area'} at ${locationName}.`;

    return await this.sendSMS(formattedPhone, message);
  }

  // Send appointment cancellation
  async sendAppointmentCancellation(phone, appointmentDetails) {
    const formattedPhone = this.formatPhoneNumber(phone);
    const { appointmentNumber, serviceName, date, time, reason } = appointmentDetails;

    const message = `Your appointment has been cancelled.
Ref: ${appointmentNumber}
Service: ${serviceName}
Date: ${date} at ${time}
${reason ? `Reason: ${reason}` : ''}
You can book a new appointment anytime.`;

    return await this.sendSMS(formattedPhone, message);
  }

  // Generic SMS sending method
  async sendSMS(phone, message) {
    try {
      if (!this.client) {
        console.log('SMS Service not configured. Would send:', { phone, message });
        return { success: true, mock: true };
      }

      if (!this.fromNumber) {
        throw new Error('Twilio phone number not configured');
      }

      const result = await this.client.messages.create({
        body: message,
        from: this.fromNumber,
        to: phone
      });

      console.log(`SMS sent successfully to ${phone}. SID: ${result.sid}`);
      return { success: true, sid: result.sid };
    } catch (error) {
      console.error('SMS sending failed:', error);
      throw new Error(`Failed to send SMS: ${error.message}`);
    }
  }

  // Send bulk SMS (for notifications to multiple users)
  async sendBulkSMS(phoneNumbers, message) {
    const results = [];

    for (const phone of phoneNumbers) {
      try {
        const result = await this.sendSMS(phone, message);
        results.push({ phone, success: true, result });
      } catch (error) {
        results.push({ phone, success: false, error: error.message });
      }
    }

    return results;
  }

  // Send USSD-style menu (for future USSD integration)
  generateUSSDMenu(options) {
    let menu = 'Umurongo - Queue Management\n';
    options.forEach((option, index) => {
      menu += `${index + 1}. ${option}\n`;
    });
    return menu;
  }
}

module.exports = new SMSService();
