{"version": 3, "file": "InferTypesFromRule.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/InferTypesFromRule.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAEnE;;GAEG;AACH,KAAK,wBAAwB,CAAC,CAAC,IAAI,CAAC,SAAS,UAAU,CACrD,MAAM,YAAY,EAClB,MAAM,QAAQ,CACf,GACG,QAAQ,GACR,CAAC,SAAS,kBAAkB,CAAC,MAAM,YAAY,EAAE,MAAM,QAAQ,CAAC,GAChE,QAAQ,GACR,OAAO,CAAC;AAEZ;;GAEG;AACH,KAAK,2BAA2B,CAAC,CAAC,IAAI,CAAC,SAAS,UAAU,CACxD,MAAM,WAAW,EACjB,MAAM,SAAS,CAChB,GACG,WAAW,GACX,CAAC,SAAS,kBAAkB,CAAC,MAAM,WAAW,EAAE,MAAM,SAAS,CAAC,GAChE,WAAW,GACX,OAAO,CAAC;AAEZ,OAAO,EAAE,wBAAwB,EAAE,2BAA2B,EAAE,CAAC"}