# 🔧 Login Error - Complete Fix Guide

## 🎯 **Quick Fix Steps**

### **Step 1: Start Simple Test Server**
```bash
cd server
node start-with-data.js
```

This starts a simplified backend with working authentication.

### **Step 2: Test Login Credentials**
Use these **exact credentials**:

| Role | Phone | Password |
|------|-------|----------|
| **Customer** | `+************` | `customer123` |
| **Admin** | `+************` | `admin123456` |
| **Business Admin** | `+************` | `admin123456` |

### **Step 3: Test Frontend**
1. Start frontend: `cd client && npm start`
2. Visit: `http://localhost:3000`
3. Click "Sign In"
4. Use customer credentials: `+************` / `customer123`

## 🔍 **Common Login Issues & Solutions**

### **Issue 1: "Login failed" Error**
**Causes:**
- Backend not running
- Wrong credentials
- Network connection issue

**Solutions:**
```bash
# 1. Check if backend is running
curl http://localhost:5002/health

# 2. Use exact credentials (copy-paste)
Phone: +************
Password: customer123

# 3. Check browser console for errors (F12)
```

### **Issue 2: "Cannot connect to server"**
**Causes:**
- Backend not started
- Wrong port
- Firewall blocking

**Solutions:**
```bash
# 1. Start test server
cd server
node start-with-data.js

# 2. Check port in browser
http://localhost:5002/health

# 3. Check frontend proxy
# In client/package.json should be:
"proxy": "http://localhost:5002"
```

### **Issue 3: "Invalid phone number or password"**
**Causes:**
- Typo in credentials
- Wrong format
- Database not seeded

**Solutions:**
```bash
# Use EXACT credentials:
+************
customer123

# Note: Include the + symbol
# Note: Password is case-sensitive
```

### **Issue 4: CORS Errors**
**Causes:**
- Frontend and backend on different ports
- CORS not configured

**Solutions:**
```bash
# 1. Restart both servers
# Backend:
cd server && node start-with-data.js

# Frontend (new terminal):
cd client && npm start

# 2. Clear browser cache
Ctrl+Shift+R (hard refresh)
```

## 🐛 **Debug Steps**

### **1. Check Backend Status**
```bash
# Test health endpoint
curl http://localhost:5002/health

# Expected response:
{"status":"OK","message":"Umurongo API is running",...}
```

### **2. Test Login API Directly**
```bash
# Test login endpoint
curl -X POST http://localhost:5002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+************","password":"customer123"}'

# Expected response:
{"success":true,"message":"Login successful",...}
```

### **3. Check Frontend Console**
1. Open browser DevTools (F12)
2. Go to Console tab
3. Try to login
4. Look for error messages

**Common errors:**
- `Failed to fetch` = Backend not running
- `CORS policy` = CORS issue
- `401 Unauthorized` = Wrong credentials

### **4. Check Network Tab**
1. Open DevTools (F12)
2. Go to Network tab
3. Try to login
4. Look for failed requests

**What to check:**
- Login request status (should be 200)
- Request URL (should be localhost:5002)
- Response data

## 🔧 **Frontend Debug Panel**

Use the debug panel in the frontend:
1. Login to the app (or try to)
2. Look for 🐛 button (bottom-right)
3. Click "Run Tests"
4. Check results:
   - 🟢 Green = Working
   - 🔴 Red = Problem

## 📋 **Step-by-Step Recovery**

### **Complete Reset Process:**

1. **Stop all servers**
   ```bash
   # Press Ctrl+C in all terminals
   ```

2. **Clear browser data**
   ```javascript
   // In browser console (F12)
   localStorage.clear();
   sessionStorage.clear();
   location.reload();
   ```

3. **Start test backend**
   ```bash
   cd server
   node start-with-data.js
   ```

4. **Start frontend**
   ```bash
   cd client
   npm start
   ```

5. **Test login**
   - Visit: `http://localhost:3000`
   - Use: `+************` / `customer123`

## 🎯 **Verification Checklist**

- [ ] Backend shows: "🚀 Umurongo Test API server running on port 5002"
- [ ] Health check works: `http://localhost:5002/health`
- [ ] Frontend loads without errors
- [ ] Login form accepts input
- [ ] Credentials are exactly: `+************` / `customer123`
- [ ] No CORS errors in console
- [ ] Login redirects to dashboard

## 🚨 **If Still Not Working**

### **Try Alternative Credentials:**
```
# Try these one by one:
+************ / admin123456
+************ / admin123456
+************ / customer123
```

### **Check Browser:**
- Try different browser (Chrome, Firefox, Edge)
- Try incognito/private mode
- Disable browser extensions

### **Check Firewall:**
- Windows Defender might block localhost
- Antivirus might interfere
- Try disabling temporarily

### **Manual API Test:**
```bash
# Test with PowerShell
$body = @{
    phone = "+************"
    password = "customer123"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5002/api/auth/login" -Method POST -Body $body -ContentType "application/json"
```

## 📞 **Getting Help**

If login still fails, provide this information:

1. **Backend terminal output**
2. **Browser console errors** (F12 → Console)
3. **Network tab errors** (F12 → Network)
4. **Debug panel results** (🐛 button)
5. **Exact steps taken**

## ✅ **Success Indicators**

You know login is working when:
- ✅ Backend shows login attempt in console
- ✅ Frontend redirects to dashboard
- ✅ User name appears in top-right corner
- ✅ No error toasts appear
- ✅ Debug panel shows green status

## 🎉 **Expected Flow**

1. **Enter credentials** → `+************` / `customer123`
2. **Click "Sign in"** → Loading spinner appears
3. **Success message** → "Welcome back, Alice Uwimana!"
4. **Redirect to dashboard** → Shows user stats and data
5. **Navigation works** → Can click between pages

---

**The test server includes working authentication with the exact credentials above! 🚀**
