const axios = require('axios');

async function checkSystemHealth() {
  console.log('🔍 Umurongo System Health Check\n');
  console.log('=' .repeat(50));

  const results = {
    backend: false,
    database: false,
    endpoints: {
      health: false,
      locations: false,
      services: false,
      auth: false
    }
  };

  // 1. Check Backend Server
  console.log('\n📡 Checking Backend Server...');
  try {
    const response = await axios.get('http://localhost:5001/health', { timeout: 5000 });
    if (response.status === 200) {
      console.log('✅ Backend server is running on port 5001');
      console.log(`   Status: ${response.data.status}`);
      console.log(`   Message: ${response.data.message}`);
      results.backend = true;
      results.endpoints.health = true;
    }
  } catch (error) {
    console.log('❌ Backend server is not responding');
    if (error.code === 'ECONNREFUSED') {
      console.log('   Error: Connection refused - server not running');
      console.log('   Solution: Run "cd server && node server.js"');
    } else {
      console.log(`   Error: ${error.message}`);
    }
  }

  if (!results.backend) {
    console.log('\n🚨 Backend server must be running for other tests to pass');
    console.log('💡 Start backend: cd server && node server.js');
    return results;
  }

  // 2. Check Database Connection
  console.log('\n🗄️  Checking Database...');
  try {
    const response = await axios.get('http://localhost:5001/api/locations', { timeout: 5000 });
    if (response.status === 200) {
      console.log('✅ Database connection working');
      console.log(`   Found ${response.data?.data?.locations?.length || 0} locations`);
      results.database = true;
      results.endpoints.locations = true;
    }
  } catch (error) {
    console.log('❌ Database connection failed');
    console.log(`   Error: ${error.response?.status || error.message}`);
  }

  // 3. Check Services Endpoint
  console.log('\n🛠️  Checking Services API...');
  try {
    const response = await axios.get('http://localhost:5001/api/services', { timeout: 5000 });
    if (response.status === 200) {
      console.log('✅ Services API working');
      console.log(`   Found ${response.data?.data?.services?.length || 0} services`);
      results.endpoints.services = true;
    }
  } catch (error) {
    console.log('❌ Services API failed');
    console.log(`   Error: ${error.response?.status || error.message}`);
  }

  // 4. Check Auth Endpoint
  console.log('\n🔐 Checking Authentication API...');
  try {
    const response = await axios.post('http://localhost:5001/api/auth/login', {
      phone: '+250788000003',
      password: 'customer123'
    }, { timeout: 5000 });
    
    if (response.status === 200) {
      console.log('✅ Authentication API working');
      console.log(`   Demo login successful`);
      results.endpoints.auth = true;
      
      // Test authenticated endpoint
      const token = response.data.data.token;
      try {
        const appointmentsResponse = await axios.get('http://localhost:5001/api/appointments', {
          headers: { Authorization: `Bearer ${token}` },
          timeout: 5000
        });
        console.log('✅ Authenticated endpoints working');
        console.log(`   Found ${appointmentsResponse.data?.data?.appointments?.length || 0} appointments`);
      } catch (authError) {
        console.log('⚠️  Authenticated endpoints may have issues');
        console.log(`   Error: ${authError.response?.status || authError.message}`);
      }
    }
  } catch (error) {
    console.log('❌ Authentication API failed');
    console.log(`   Error: ${error.response?.status || error.message}`);
    if (error.response?.status === 401) {
      console.log('   This might be expected if demo credentials changed');
    }
  }

  // 5. Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 SYSTEM HEALTH SUMMARY');
  console.log('=' .repeat(50));
  
  const allGood = results.backend && results.database && 
                  results.endpoints.health && results.endpoints.locations && 
                  results.endpoints.services && results.endpoints.auth;

  if (allGood) {
    console.log('🎉 ALL SYSTEMS OPERATIONAL!');
    console.log('✅ Backend server running');
    console.log('✅ Database connected');
    console.log('✅ All API endpoints working');
    console.log('✅ Authentication working');
    console.log('\n🚀 Your Umurongo system is ready!');
    console.log('💻 Frontend: http://localhost:3000');
    console.log('🔧 Backend: http://localhost:5001');
  } else {
    console.log('⚠️  SYSTEM ISSUES DETECTED');
    console.log('\n🔧 Issues found:');
    if (!results.backend) console.log('❌ Backend server not running');
    if (!results.database) console.log('❌ Database connection failed');
    if (!results.endpoints.health) console.log('❌ Health endpoint failed');
    if (!results.endpoints.locations) console.log('❌ Locations endpoint failed');
    if (!results.endpoints.services) console.log('❌ Services endpoint failed');
    if (!results.endpoints.auth) console.log('❌ Authentication endpoint failed');
  }

  console.log('\n📋 Quick Commands:');
  console.log('Backend: cd server && node server.js');
  console.log('Frontend: cd client && npm start');
  console.log('System Check: node system-check.js');
  
  console.log('\n📱 Demo Credentials:');
  console.log('Customer: +250788000003 / customer123');
  console.log('Admin: +250788000001 / admin123456');

  return results;
}

// Run the check
checkSystemHealth().catch(console.error);
