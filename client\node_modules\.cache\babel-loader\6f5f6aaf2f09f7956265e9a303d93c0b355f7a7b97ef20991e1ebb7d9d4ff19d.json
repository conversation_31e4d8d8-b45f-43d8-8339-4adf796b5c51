{"ast": null, "code": "import addHours from \"../addHours/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachHourOfInterval\n * @category Interval Helpers\n * @summary Return the array of hours within the specified time interval.\n *\n * @description\n * Return the array of hours within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The value should be more than 1.\n * @returns {Date[]} the array with starts of hours from the hour of the interval start to the hour of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.step` must be a number greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each hour between 6 October 2014, 12:00 and 6 October 2014, 15:00\n * const result = eachHourOfInterval({\n *   start: new Date(2014, 9, 6, 12),\n *   end: new Date(2014, 9, 6, 15)\n * })\n * //=> [\n * //   Mon Oct 06 2014 12:00:00,\n * //   Mon Oct 06 2014 13:00:00,\n * //   Mon Oct 06 2014 14:00:00,\n * //   Mon Oct 06 2014 15:00:00\n * // ]\n */\nexport default function eachHourOfInterval(dirtyInterval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setMinutes(0, 0, 0);\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addHours(currentDate, step);\n  }\n  return dates;\n}", "map": {"version": 3, "names": ["addHours", "toDate", "requiredArgs", "eachHourOfInterval", "dirtyInterval", "options", "_options$step", "arguments", "interval", "startDate", "start", "endDate", "end", "startTime", "getTime", "endTime", "RangeError", "dates", "currentDate", "setMinutes", "step", "Number", "isNaN", "push"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/eachHourOfInterval/index.js"], "sourcesContent": ["import addHours from \"../addHours/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachHourOfInterval\n * @category Interval Helpers\n * @summary Return the array of hours within the specified time interval.\n *\n * @description\n * Return the array of hours within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The value should be more than 1.\n * @returns {Date[]} the array with starts of hours from the hour of the interval start to the hour of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.step` must be a number greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each hour between 6 October 2014, 12:00 and 6 October 2014, 15:00\n * const result = eachHourOfInterval({\n *   start: new Date(2014, 9, 6, 12),\n *   end: new Date(2014, 9, 6, 15)\n * })\n * //=> [\n * //   Mon Oct 06 2014 12:00:00,\n * //   Mon Oct 06 2014 13:00:00,\n * //   Mon Oct 06 2014 14:00:00,\n * //   Mon Oct 06 2014 15:00:00\n * // ]\n */\nexport default function eachHourOfInterval(dirtyInterval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setMinutes(0, 0, 0);\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addHours(currentDate, step);\n  }\n  return dates;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EACjE,IAAIC,aAAa;EACjBJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGJ,aAAa,IAAI,CAAC,CAAC;EAClC,IAAIK,SAAS,GAAGR,MAAM,CAACO,QAAQ,CAACE,KAAK,CAAC;EACtC,IAAIC,OAAO,GAAGV,MAAM,CAACO,QAAQ,CAACI,GAAG,CAAC;EAClC,IAAIC,SAAS,GAAGJ,SAAS,CAACK,OAAO,CAAC,CAAC;EACnC,IAAIC,OAAO,GAAGJ,OAAO,CAACG,OAAO,CAAC,CAAC;;EAE/B;EACA,IAAI,EAAED,SAAS,IAAIE,OAAO,CAAC,EAAE;IAC3B,MAAM,IAAIC,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,WAAW,GAAGT,SAAS;EAC3BS,WAAW,CAACC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC/B,IAAIC,IAAI,GAAGC,MAAM,CAAC,CAACf,aAAa,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,IAAI,MAAM,IAAI,IAAId,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC;EAC5J,IAAIc,IAAI,GAAG,CAAC,IAAIE,KAAK,CAACF,IAAI,CAAC,EAAE,MAAM,IAAIJ,UAAU,CAAC,gDAAgD,CAAC;EACnG,OAAOE,WAAW,CAACJ,OAAO,CAAC,CAAC,IAAIC,OAAO,EAAE;IACvCE,KAAK,CAACM,IAAI,CAACtB,MAAM,CAACiB,WAAW,CAAC,CAAC;IAC/BA,WAAW,GAAGlB,QAAQ,CAACkB,WAAW,EAAEE,IAAI,CAAC;EAC3C;EACA,OAAOH,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}