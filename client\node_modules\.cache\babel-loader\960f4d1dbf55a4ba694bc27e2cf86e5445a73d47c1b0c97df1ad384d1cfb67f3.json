{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { useAuth } from '../contexts/AuthContext';\nimport { EyeIcon, EyeSlashIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    setError\n  } = useForm();\n  const onSubmit = async data => {\n    console.log('🔐 LoginPage: Form submitted with:', {\n      phone: data.phone,\n      password: '***'\n    });\n    setLoading(true);\n    try {\n      const result = await login(data.phone, data.password);\n      console.log('🔐 LoginPage: Login result:', {\n        success: result.success\n      });\n      if (result.success) {\n        console.log('🔐 LoginPage: Login successful, navigating to:', from);\n        navigate(from, {\n          replace: true\n        });\n      } else {\n        console.log('🔐 LoginPage: Login failed:', result.error);\n        setError('root', {\n          message: result.error\n        });\n      }\n    } catch (error) {\n      console.error('🔐 LoginPage: Unexpected error:', error);\n      setError('root', {\n        message: 'An unexpected error occurred. Please try again.'\n      });\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-20 left-20 w-72 h-72 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-20 right-20 w-72 h-72 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow\",\n        style: {\n          animationDelay: '2s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative max-w-md w-full space-y-8\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"inline-block\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-primary-600 mb-2\",\n              children: \"Umurongo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-2\",\n            children: \"Welcome back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Sign in to your account to continue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          className: \"bg-white rounded-2xl shadow-large p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"space-y-6\",\n            onSubmit: handleSubmit(onSubmit),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"phone\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Phone Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(DevicePhoneMobileIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"phone\",\n                  type: \"tel\",\n                  placeholder: \"+250788000000\",\n                  className: `block w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.phone ? 'border-red-300' : 'border-gray-300'}`,\n                  ...register('phone', {\n                    required: 'Phone number is required',\n                    pattern: {\n                      value: /^\\+250[0-9]{9}$/,\n                      message: 'Please enter a valid Rwandan phone number (+250XXXXXXXXX)'\n                    }\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), errors.phone && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.phone.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"password\",\n                  type: showPassword ? 'text' : 'password',\n                  placeholder: \"Enter your password\",\n                  className: `block w-full pr-10 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.password ? 'border-red-300' : 'border-gray-300'}`,\n                  ...register('password', {\n                    required: 'Password is required',\n                    minLength: {\n                      value: 6,\n                      message: 'Password must be at least 6 characters'\n                    }\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                  onClick: () => setShowPassword(!showPassword),\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(EyeIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: errors.password.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"remember-me\",\n                  name: \"remember-me\",\n                  type: \"checkbox\",\n                  className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"remember-me\",\n                  className: \"ml-2 block text-sm text-gray-700\",\n                  children: \"Remember me\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"font-medium text-primary-600 hover:text-primary-500\",\n                  children: \"Forgot your password?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), errors.root && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-red-600\",\n                children: errors.root.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"submit\",\n              disabled: loading,\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), \"Signing in...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this) : 'Sign in'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"font-medium text-primary-600 hover:text-primary-500\",\n                children: \"Sign up for free\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          className: \"bg-gray-50 rounded-lg p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 mb-2\",\n            children: \"Demo Credentials:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Customer:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 18\n              }, this), \" +************ / customer123\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Admin:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 18\n              }, this), \" +************ / admin123456\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"V0kDlzmYg6aDzConUz4WEtMZk/s=\", false, function () {\n  return [useAuth, useNavigate, useLocation, useForm];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useLocation", "motion", "useForm", "useAuth", "EyeIcon", "EyeSlashIcon", "DevicePhoneMobileIcon", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "_location$state", "_location$state$from", "showPassword", "setShowPassword", "loading", "setLoading", "login", "navigate", "location", "from", "state", "pathname", "register", "handleSubmit", "formState", "errors", "setError", "onSubmit", "data", "console", "log", "phone", "password", "result", "success", "replace", "error", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "animationDelay", "div", "initial", "opacity", "y", "animate", "transition", "duration", "to", "delay", "htmlFor", "id", "type", "placeholder", "required", "pattern", "value", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "name", "href", "root", "button", "disabled", "whileHover", "scale", "whileTap", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { useAuth } from '../contexts/AuthContext';\nimport { EyeIcon, EyeSlashIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';\n\nconst LoginPage = () => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const { login } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const from = location.state?.from?.pathname || '/dashboard';\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    setError\n  } = useForm();\n\n  const onSubmit = async (data) => {\n    console.log('🔐 LoginPage: Form submitted with:', { phone: data.phone, password: '***' });\n\n    setLoading(true);\n\n    try {\n      const result = await login(data.phone, data.password);\n\n      console.log('🔐 LoginPage: Login result:', { success: result.success });\n\n      if (result.success) {\n        console.log('🔐 LoginPage: Login successful, navigating to:', from);\n        navigate(from, { replace: true });\n      } else {\n        console.log('🔐 LoginPage: Login failed:', result.error);\n        setError('root', { message: result.error });\n      }\n    } catch (error) {\n      console.error('🔐 LoginPage: Unexpected error:', error);\n      setError('root', { message: 'An unexpected error occurred. Please try again.' });\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-20 left-20 w-72 h-72 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow\"></div>\n        <div className=\"absolute bottom-20 right-20 w-72 h-72 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow\" style={{animationDelay: '2s'}}></div>\n      </div>\n\n      <div className=\"relative max-w-md w-full space-y-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          {/* Header */}\n          <div className=\"text-center\">\n            <Link to=\"/\" className=\"inline-block\">\n              <h1 className=\"text-3xl font-bold text-primary-600 mb-2\">Umurongo</h1>\n            </Link>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Welcome back</h2>\n            <p className=\"text-gray-600\">Sign in to your account to continue</p>\n          </div>\n\n          {/* Login Form */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"bg-white rounded-2xl shadow-large p-8\"\n          >\n            <form className=\"space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n              {/* Phone Number */}\n              <div>\n                <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Phone Number\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <DevicePhoneMobileIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    id=\"phone\"\n                    type=\"tel\"\n                    placeholder=\"+250788000000\"\n                    className={`block w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${\n                      errors.phone ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    {...register('phone', {\n                      required: 'Phone number is required',\n                      pattern: {\n                        value: /^\\+250[0-9]{9}$/,\n                        message: 'Please enter a valid Rwandan phone number (+250XXXXXXXXX)'\n                      }\n                    })}\n                  />\n                </div>\n                {errors.phone && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.phone.message}</p>\n                )}\n              </div>\n\n              {/* Password */}\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    placeholder=\"Enter your password\"\n                    className={`block w-full pr-10 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${\n                      errors.password ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    {...register('password', {\n                      required: 'Password is required',\n                      minLength: {\n                        value: 6,\n                        message: 'Password must be at least 6 characters'\n                      }\n                    })}\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                    ) : (\n                      <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n                {errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n                )}\n              </div>\n\n              {/* Remember me and Forgot password */}\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"remember-me\"\n                    name=\"remember-me\"\n                    type=\"checkbox\"\n                    className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-700\">\n                    Remember me\n                  </label>\n                </div>\n                <div className=\"text-sm\">\n                  <a href=\"#\" className=\"font-medium text-primary-600 hover:text-primary-500\">\n                    Forgot your password?\n                  </a>\n                </div>\n              </div>\n\n              {/* Error message */}\n              {errors.root && (\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-3\">\n                  <p className=\"text-sm text-red-600\">{errors.root.message}</p>\n                </div>\n              )}\n\n              {/* Submit button */}\n              <motion.button\n                type=\"submit\"\n                disabled={loading}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                className=\"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\n                    Signing in...\n                  </div>\n                ) : (\n                  'Sign in'\n                )}\n              </motion.button>\n            </form>\n\n            {/* Sign up link */}\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-sm text-gray-600\">\n                Don't have an account?{' '}\n                <Link\n                  to=\"/register\"\n                  className=\"font-medium text-primary-600 hover:text-primary-500\"\n                >\n                  Sign up for free\n                </Link>\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Demo credentials */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"bg-gray-50 rounded-lg p-4 text-center\"\n          >\n            <p className=\"text-xs text-gray-500 mb-2\">Demo Credentials:</p>\n            <div className=\"text-xs text-gray-600 space-y-1\">\n              <p><strong>Customer:</strong> +************ / customer123</p>\n              <p><strong>Admin:</strong> +************ / admin123456</p>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,EAAEC,YAAY,EAAEC,qBAAqB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3F,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEoB;EAAM,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC3B,MAAMe,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,IAAI,GAAG,EAAAT,eAAA,GAAAQ,QAAQ,CAACE,KAAK,cAAAV,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBS,IAAI,cAAAR,oBAAA,uBAApBA,oBAAA,CAAsBU,QAAQ,KAAI,YAAY;EAE3D,MAAM;IACJC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC;EACF,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAEb,MAAM0B,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;MAAEC,KAAK,EAAEH,IAAI,CAACG,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAAC;IAEzFjB,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMkB,MAAM,GAAG,MAAMjB,KAAK,CAACY,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACI,QAAQ,CAAC;MAErDH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;QAAEI,OAAO,EAAED,MAAM,CAACC;MAAQ,CAAC,CAAC;MAEvE,IAAID,MAAM,CAACC,OAAO,EAAE;QAClBL,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEX,IAAI,CAAC;QACnEF,QAAQ,CAACE,IAAI,EAAE;UAAEgB,OAAO,EAAE;QAAK,CAAC,CAAC;MACnC,CAAC,MAAM;QACLN,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,MAAM,CAACG,KAAK,CAAC;QACxDV,QAAQ,CAAC,MAAM,EAAE;UAAEW,OAAO,EAAEJ,MAAM,CAACG;QAAM,CAAC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDV,QAAQ,CAAC,MAAM,EAAE;QAAEW,OAAO,EAAE;MAAkD,CAAC,CAAC;IAClF;IAEAtB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACER,OAAA;IAAK+B,SAAS,EAAC,sIAAsI;IAAAC,QAAA,gBAEnJhC,OAAA;MAAK+B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/ChC,OAAA;QAAK+B,SAAS,EAAC;MAA+H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrJpC,OAAA;QAAK+B,SAAS,EAAC,qIAAqI;QAACM,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvL,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDhC,OAAA,CAACP,MAAM,CAAC8C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAG9BhC,OAAA;UAAK+B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhC,OAAA,CAACV,IAAI;YAACwD,EAAE,EAAC,GAAG;YAACf,SAAS,EAAC,cAAc;YAAAC,QAAA,eACnChC,OAAA;cAAI+B,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACPpC,OAAA;YAAI+B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEpC,OAAA;YAAG+B,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAGNpC,OAAA,CAACP,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAC1ChB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEjDhC,OAAA;YAAM+B,SAAS,EAAC,WAAW;YAACX,QAAQ,EAAEJ,YAAY,CAACI,QAAQ,CAAE;YAAAY,QAAA,gBAE3DhC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAOgD,OAAO,EAAC,OAAO;gBAACjB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpC,OAAA;gBAAK+B,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBhC,OAAA;kBAAK+B,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFhC,OAAA,CAACF,qBAAqB;oBAACiC,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNpC,OAAA;kBACEiD,EAAE,EAAC,OAAO;kBACVC,IAAI,EAAC,KAAK;kBACVC,WAAW,EAAC,eAAe;kBAC3BpB,SAAS,EAAE,iKACTb,MAAM,CAACM,KAAK,GAAG,gBAAgB,GAAG,iBAAiB,EAClD;kBAAA,GACCT,QAAQ,CAAC,OAAO,EAAE;oBACpBqC,QAAQ,EAAE,0BAA0B;oBACpCC,OAAO,EAAE;sBACPC,KAAK,EAAE,iBAAiB;sBACxBxB,OAAO,EAAE;oBACX;kBACF,CAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACLlB,MAAM,CAACM,KAAK,iBACXxB,OAAA;gBAAG+B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEd,MAAM,CAACM,KAAK,CAACM;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACnE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNpC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAOgD,OAAO,EAAC,UAAU;gBAACjB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpC,OAAA;gBAAK+B,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBhC,OAAA;kBACEiD,EAAE,EAAC,UAAU;kBACbC,IAAI,EAAE7C,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzC8C,WAAW,EAAC,qBAAqB;kBACjCpB,SAAS,EAAE,4JACTb,MAAM,CAACO,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,EACrD;kBAAA,GACCV,QAAQ,CAAC,UAAU,EAAE;oBACvBqC,QAAQ,EAAE,sBAAsB;oBAChCG,SAAS,EAAE;sBACTD,KAAK,EAAE,CAAC;sBACRxB,OAAO,EAAE;oBACX;kBACF,CAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFpC,OAAA;kBACEkD,IAAI,EAAC,QAAQ;kBACbnB,SAAS,EAAC,mDAAmD;kBAC7DyB,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAAC,CAACD,YAAY,CAAE;kBAAA2B,QAAA,EAE7C3B,YAAY,gBACXL,OAAA,CAACH,YAAY;oBAACkC,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElDpC,OAAA,CAACJ,OAAO;oBAACmC,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC7C;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACLlB,MAAM,CAACO,QAAQ,iBACdzB,OAAA;gBAAG+B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAEd,MAAM,CAACO,QAAQ,CAACK;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNpC,OAAA;cAAK+B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDhC,OAAA;gBAAK+B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChC,OAAA;kBACEiD,EAAE,EAAC,aAAa;kBAChBQ,IAAI,EAAC,aAAa;kBAClBP,IAAI,EAAC,UAAU;kBACfnB,SAAS,EAAC;gBAAyE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACFpC,OAAA;kBAAOgD,OAAO,EAAC,aAAa;kBAACjB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAE1E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNpC,OAAA;gBAAK+B,SAAS,EAAC,SAAS;gBAAAC,QAAA,eACtBhC,OAAA;kBAAG0D,IAAI,EAAC,GAAG;kBAAC3B,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLlB,MAAM,CAACyC,IAAI,iBACV3D,OAAA;cAAK+B,SAAS,EAAC,gDAAgD;cAAAC,QAAA,eAC7DhC,OAAA;gBAAG+B,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAEd,MAAM,CAACyC,IAAI,CAAC7B;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CACN,eAGDpC,OAAA,CAACP,MAAM,CAACmE,MAAM;cACZV,IAAI,EAAC,QAAQ;cACbW,QAAQ,EAAEtD,OAAQ;cAClBuD,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BhC,SAAS,EAAC,qSAAqS;cAAAC,QAAA,EAE9SzB,OAAO,gBACNP,OAAA;gBAAK+B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChC,OAAA;kBAAK+B,SAAS,EAAC;gBAAmF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iBAE3G;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,GAEN;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAGPpC,OAAA;YAAK+B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BhC,OAAA;cAAG+B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBACb,EAAC,GAAG,eAC1BhC,OAAA,CAACV,IAAI;gBACHwD,EAAE,EAAC,WAAW;gBACdf,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAChE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbpC,OAAA,CAACP,MAAM,CAAC8C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAC1ChB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEjDhC,OAAA;YAAG+B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/DpC,OAAA;YAAK+B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9ChC,OAAA;cAAAgC,QAAA,gBAAGhC,OAAA;gBAAAgC,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gCAA4B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7DpC,OAAA;cAAAgC,QAAA,gBAAGhC,OAAA;gBAAAgC,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gCAA4B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAzNID,SAAS;EAAA,QAGKN,OAAO,EACRJ,WAAW,EACXC,WAAW,EASxBE,OAAO;AAAA;AAAAuE,EAAA,GAdPhE,SAAS;AA2Nf,eAAeA,SAAS;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}