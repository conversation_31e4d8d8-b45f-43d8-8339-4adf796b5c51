const express = require('express');
const { Op } = require('sequelize');
const { Appointment, Service, Location, User, Queue } = require('../models');
const { authenticateToken, requireLocationAdmin } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');
const smsService = require('../services/smsService');

const router = express.Router();

// Get all appointments (with filters)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      location_id,
      service_id,
      date_from,
      date_to,
      user_id
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Regular users can only see their own appointments
    if (req.user.role === 'customer') {
      whereClause.user_id = req.user.id;
    } else if (user_id) {
      whereClause.user_id = user_id;
    }

    if (status) {
      whereClause.status = status;
    }

    if (location_id) {
      whereClause.location_id = location_id;
    }

    if (service_id) {
      whereClause.service_id = service_id;
    }

    if (date_from || date_to) {
      whereClause.appointment_date = {};
      if (date_from) whereClause.appointment_date[Op.gte] = new Date(date_from);
      if (date_to) whereClause.appointment_date[Op.lte] = new Date(date_to);
    }

    const { count, rows: appointments } = await Appointment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'phone']
        },
        {
          model: Service,
          as: 'service',
          attributes: ['id', 'name', 'category', 'duration']
        },
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name', 'address', 'district']
        },
        {
          model: Queue,
          as: 'queue',
          required: false
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['appointment_date', 'DESC'], ['appointment_time', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        appointments,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointments'
    });
  }
});

// Get appointment by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const appointment = await Appointment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'phone', 'email']
        },
        {
          model: Service,
          as: 'service'
        },
        {
          model: Location,
          as: 'location'
        },
        {
          model: Queue,
          as: 'queue'
        }
      ]
    });

    if (!appointment) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found'
      });
    }

    // Check authorization
    if (req.user.role === 'customer' && appointment.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { appointment }
    });
  } catch (error) {
    console.error('Get appointment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointment'
    });
  }
});

// Create new appointment
router.post('/',
  authenticateToken,
  validate(schemas.createAppointment),
  async (req, res) => {
    try {
      const { service_id, appointment_date, appointment_time, notes, patient_notes } = req.body;

      // Get service with location
      const service = await Service.findByPk(service_id, {
        include: [
          {
            model: Location,
            as: 'location'
          }
        ]
      });

      if (!service) {
        return res.status(404).json({
          success: false,
          message: 'Service not found'
        });
      }

      if (!service.is_active) {
        return res.status(400).json({
          success: false,
          message: 'Service is not available'
        });
      }

      // Check if date is available
      if (!service.isAvailableForDate(appointment_date)) {
        return res.status(400).json({
          success: false,
          message: 'Selected date is outside the booking window'
        });
      }

      // Check for existing appointment at the same time
      const existingAppointment = await Appointment.findOne({
        where: {
          service_id,
          appointment_date,
          appointment_time,
          status: ['scheduled', 'confirmed', 'in_progress']
        }
      });

      if (existingAppointment) {
        return res.status(400).json({
          success: false,
          message: 'Time slot is already booked'
        });
      }

      // Create appointment
      const appointment = await Appointment.create({
        user_id: req.user.id,
        service_id,
        location_id: service.location_id,
        appointment_date,
        appointment_time,
        notes,
        patient_notes,
        estimated_duration: service.duration,
        payment_status: service.requires_payment ? 'pending' : 'not_required'
      });

      // Create queue entry
      const queueCount = await Queue.count({
        where: {
          location_id: service.location_id,
          queue_date: appointment_date,
          status: ['waiting', 'called', 'in_service']
        }
      });

      const queue = await Queue.create({
        appointment_id: appointment.id,
        location_id: service.location_id,
        service_id: service_id,
        user_id: req.user.id,
        queue_date: appointment_date,
        position: queueCount + 1,
        queue_number: queueCount + 1,
        estimated_wait_time: service.calculateEstimatedWaitTime(queueCount + 1)
      });

      // Send confirmation SMS
      try {
        await smsService.sendAppointmentConfirmation(req.user.phone, {
          appointmentNumber: appointment.appointment_number,
          serviceName: service.name,
          locationName: service.location.name,
          date: appointment_date,
          time: appointment_time
        });
      } catch (smsError) {
        console.error('SMS sending failed:', smsError);
      }

      // Get complete appointment data
      const completeAppointment = await Appointment.findByPk(appointment.id, {
        include: [
          {
            model: Service,
            as: 'service'
          },
          {
            model: Location,
            as: 'location'
          },
          {
            model: Queue,
            as: 'queue'
          }
        ]
      });

      res.status(201).json({
        success: true,
        message: 'Appointment booked successfully',
        data: { appointment: completeAppointment }
      });
    } catch (error) {
      console.error('Create appointment error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create appointment',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
);

// Update appointment status (admin only)
router.put('/:id/status',
  authenticateToken,
  validate(schemas.updateAppointmentStatus),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { status, notes, room_number } = req.body;

      const appointment = await Appointment.findByPk(id, {
        include: [
          {
            model: Service,
            as: 'service',
            include: [
              {
                model: Location,
                as: 'location'
              }
            ]
          },
          {
            model: User,
            as: 'user'
          },
          {
            model: Queue,
            as: 'queue'
          }
        ]
      });

      if (!appointment) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      // Check authorization
      if (req.user.role === 'customer') {
        return res.status(403).json({
          success: false,
          message: 'Only admins can update appointment status'
        });
      }

      if (req.user.role === 'business_admin' &&
          appointment.service.location.admin_user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to manage this appointment'
        });
      }

      // Update appointment
      const updateData = { status };
      if (notes) updateData.admin_notes = notes;

      if (status === 'in_progress') {
        updateData.actual_start_time = new Date();
      } else if (status === 'completed') {
        updateData.actual_end_time = new Date();
      } else if (status === 'cancelled') {
        updateData.cancelled_by = req.user.id;
        updateData.cancelled_at = new Date();
      }

      await appointment.update(updateData);

      // Update queue status if exists
      if (appointment.queue) {
        let queueStatus = 'waiting';
        if (status === 'in_progress') queueStatus = 'in_service';
        else if (status === 'completed') queueStatus = 'completed';
        else if (status === 'cancelled') queueStatus = 'cancelled';
        else if (status === 'no_show') queueStatus = 'no_show';

        await appointment.queue.update({
          status: queueStatus,
          room_number,
          served_by: req.user.id,
          ...(status === 'in_progress' && { service_started_at: new Date() }),
          ...(status === 'completed' && { service_completed_at: new Date() })
        });
      }

      res.json({
        success: true,
        message: 'Appointment status updated successfully',
        data: { appointment }
      });
    } catch (error) {
      console.error('Update appointment status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update appointment status'
      });
    }
  }
);

// Cancel appointment (user or admin)
router.put('/:id/cancel', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const appointment = await Appointment.findByPk(id, {
      include: [
        {
          model: Service,
          as: 'service',
          include: [
            {
              model: Location,
              as: 'location'
            }
          ]
        },
        {
          model: User,
          as: 'user'
        },
        {
          model: Queue,
          as: 'queue'
        }
      ]
    });

    if (!appointment) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found'
      });
    }

    // Check authorization
    if (req.user.role === 'customer' && appointment.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only cancel your own appointments'
      });
    }

    if (req.user.role === 'business_admin' &&
        appointment.service.location.admin_user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to cancel this appointment'
      });
    }

    // Check if appointment can be cancelled
    if (!appointment.canBeCancelled()) {
      return res.status(400).json({
        success: false,
        message: 'Appointment cannot be cancelled (too close to appointment time or already completed)'
      });
    }

    // Cancel appointment
    await appointment.update({
      status: 'cancelled',
      cancelled_by: req.user.id,
      cancelled_at: new Date(),
      cancellation_reason: reason
    });

    // Update queue status
    if (appointment.queue) {
      await appointment.queue.update({
        status: 'cancelled'
      });
    }

    // Send cancellation SMS
    try {
      await smsService.sendAppointmentCancellation(appointment.user.phone, {
        appointmentNumber: appointment.appointment_number,
        serviceName: appointment.service.name,
        date: appointment.appointment_date,
        time: appointment.appointment_time,
        reason
      });
    } catch (smsError) {
      console.error('SMS sending failed:', smsError);
    }

    res.json({
      success: true,
      message: 'Appointment cancelled successfully'
    });
  } catch (error) {
    console.error('Cancel appointment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel appointment'
    });
  }
});

// Submit feedback for appointment
router.post('/:id/feedback',
  authenticateToken,
  validate(schemas.submitFeedback),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { rating, comment } = req.body;

      const appointment = await Appointment.findByPk(id);

      if (!appointment) {
        return res.status(404).json({
          success: false,
          message: 'Appointment not found'
        });
      }

      // Only the appointment owner can submit feedback
      if (appointment.user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'You can only submit feedback for your own appointments'
        });
      }

      // Only completed appointments can have feedback
      if (appointment.status !== 'completed') {
        return res.status(400).json({
          success: false,
          message: 'Feedback can only be submitted for completed appointments'
        });
      }

      await appointment.update({
        feedback_rating: rating,
        feedback_comment: comment
      });

      res.json({
        success: true,
        message: 'Feedback submitted successfully'
      });
    } catch (error) {
      console.error('Submit feedback error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to submit feedback'
      });
    }
  }
);

module.exports = router;
