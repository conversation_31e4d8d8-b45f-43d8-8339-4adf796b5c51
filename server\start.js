#!/usr/bin/env node

// Startup script for Umurongo Backend
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Umurongo Backend...\n');

// Check if .env file exists
if (!fs.existsSync('.env')) {
  console.log('❌ .env file not found!');
  console.log('📝 Please copy .env.example to .env and configure your settings');
  process.exit(1);
}

// Check if node_modules exists
if (!fs.existsSync('node_modules')) {
  console.log('❌ Dependencies not installed!');
  console.log('📦 Please run: npm install');
  process.exit(1);
}

// Load environment variables
require('dotenv').config();

// Check required environment variables
const requiredEnvVars = [
  'DB_HOST',
  'DB_NAME',
  'DB_USER',
  'DB_PASSWORD',
  'JWT_SECRET'
];

const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.log('❌ Missing required environment variables:');
  missingEnvVars.forEach(varName => {
    console.log(`   - ${varName}`);
  });
  console.log('\n📝 Please update your .env file');
  process.exit(1);
}

// Test database connection
async function testDatabaseConnection() {
  try {
    const sequelize = require('./config/database');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Sync models in development
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ Database models synchronized');
    }
    
    return true;
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    console.log('\n💡 Make sure:');
    console.log('   - MySQL server is running');
    console.log('   - Database exists');
    console.log('   - Credentials in .env are correct');
    return false;
  }
}

// Start the server
async function startServer() {
  try {
    // Test database first
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      process.exit(1);
    }

    // Start the Express server
    require('./server');
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down gracefully...');
  process.exit(0);
});

// Start the application
startServer();
