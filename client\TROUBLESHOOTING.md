# 🔧 Troubleshooting "Failed to load appointments" Error

## 🎯 **Quick Fix Steps**

### 1. **Check Backend Server Status**
```bash
# Make sure backend is running on port 5001
cd server
node server.js
```

**Expected output:**
```
🗄️  Using SQLite database for development
📱 SMS service running in mock mode
✅ Database connection established successfully.
✅ Database connection ready (sync skipped).
🚀 Umurongo API server running on port 5001
```

### 2. **Test Backend Health**
Open browser and visit: `http://localhost:5001/health`

**Expected response:**
```json
{
  "status": "OK",
  "message": "Umurongo API is running",
  "timestamp": "2024-01-XX...",
  "environment": "development"
}
```

### 3. **Check Frontend Connection**
1. Start frontend: `cd client && npm start`
2. Login with demo credentials:
   - Phone: `+250788000003`
   - Password: `customer123`
3. Look for the **🐛 Debug Panel** button (bottom-right corner)
4. Click it and run tests

## 🔍 **Common Issues & Solutions**

### Issue 1: Backend Not Running
**Symptoms:** "Cannot connect to server" error

**Solution:**
```bash
cd server
node server.js
```

### Issue 2: Wrong Port
**Symptoms:** Connection refused on port 5000

**Solution:** Backend runs on port 5001, not 5000
- Check `client/package.json` proxy: `"proxy": "http://localhost:5001"`

### Issue 3: Authentication Issues
**Symptoms:** 401 Unauthorized errors

**Solutions:**
1. **Clear browser storage:**
   ```javascript
   // In browser console
   localStorage.clear();
   sessionStorage.clear();
   ```

2. **Login again** with demo credentials

3. **Check token in localStorage:**
   ```javascript
   // In browser console
   console.log(localStorage.getItem('token'));
   ```

### Issue 4: CORS Issues
**Symptoms:** CORS policy errors in browser console

**Solution:** Backend already has CORS enabled, but if issues persist:
```bash
# Restart both servers
cd server && node server.js
# In new terminal
cd client && npm start
```

### Issue 5: Database Issues
**Symptoms:** Database connection errors

**Solution:**
```bash
cd server
# Remove and recreate database
rm umurongo_dev.db
node server.js
# Database will be recreated with sample data
```

## 🐛 **Using the Debug Panel**

### Access Debug Panel
1. Login to the app
2. Look for 🐛 button in bottom-right corner
3. Click to open debug panel
4. Click "Run Tests" to diagnose issues

### Debug Panel Tests
- **Health Check:** Tests backend connectivity
- **API Health:** Tests API endpoint
- **Auth Status:** Checks authentication
- **Locations:** Tests locations API
- **Appointments:** Tests appointments API

### Reading Test Results
- 🟢 **Green dot:** Test passed
- 🔴 **Red dot:** Test failed (check error message)

## 📋 **Manual API Testing**

### Test with Browser
1. **Health Check:** `http://localhost:5001/health`
2. **Locations:** `http://localhost:5001/api/locations`

### Test with PowerShell
```powershell
# Test health endpoint
Invoke-WebRequest -Uri "http://localhost:5001/health"

# Test locations (no auth needed)
Invoke-WebRequest -Uri "http://localhost:5001/api/locations"

# Test appointments (needs auth token)
$headers = @{"Authorization"="Bearer YOUR_TOKEN_HERE"}
Invoke-WebRequest -Uri "http://localhost:5001/api/appointments" -Headers $headers
```

## 🔧 **Advanced Troubleshooting**

### Check Network Tab
1. Open browser DevTools (F12)
2. Go to Network tab
3. Try to load appointments
4. Look for failed requests

**Common error codes:**
- **404:** Endpoint not found (check URL)
- **401:** Not authenticated (login again)
- **500:** Server error (check backend logs)
- **ECONNREFUSED:** Backend not running

### Check Console Logs
1. Open browser DevTools (F12)
2. Go to Console tab
3. Look for error messages

**Common console errors:**
```
Failed to fetch
Network Error
CORS policy error
401 Unauthorized
```

### Backend Logs
Check backend terminal for error messages:
```
Error: connect ECONNREFUSED
Database connection failed
JWT token invalid
```

## 🚀 **Step-by-Step Recovery**

### Complete Reset Process
1. **Stop all servers** (Ctrl+C in terminals)

2. **Clear browser data:**
   ```javascript
   localStorage.clear();
   sessionStorage.clear();
   location.reload();
   ```

3. **Restart backend:**
   ```bash
   cd server
   node server.js
   ```

4. **Restart frontend:**
   ```bash
   cd client
   npm start
   ```

5. **Test connection:**
   - Visit `http://localhost:3000`
   - Login with: `+250788000003` / `customer123`
   - Check debug panel

### If Still Not Working
1. **Check ports:**
   ```bash
   netstat -an | findstr :5001  # Backend
   netstat -an | findstr :3000  # Frontend
   ```

2. **Check firewall/antivirus** - may be blocking connections

3. **Try different browser** - clear cache issues

4. **Check package.json proxy:**
   ```json
   "proxy": "http://localhost:5001"
   ```

## 📞 **Getting Help**

### Information to Provide
1. **Error messages** from browser console
2. **Backend logs** from terminal
3. **Debug panel results**
4. **Operating system** and browser version
5. **Steps taken** before error occurred

### Debug Info Export
1. Open debug panel
2. Click "Copy Info"
3. Paste the JSON data when asking for help

## ✅ **Success Indicators**

You know everything is working when:
- ✅ Backend shows "🚀 Umurongo API server running on port 5001"
- ✅ Frontend loads without errors
- ✅ Debug panel shows all green dots
- ✅ Dashboard displays user data
- ✅ No error toasts appear

## 🎯 **Prevention Tips**

1. **Always start backend first** before frontend
2. **Use demo credentials** for testing
3. **Check debug panel** when issues arise
4. **Keep both terminals open** to see logs
5. **Clear browser cache** if strange behavior occurs

---

**Need more help? Check the debug panel and provide the exported info! 🐛**
