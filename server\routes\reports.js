const express = require('express');
const { Op, sequelize } = require('sequelize');
const { Appointment, Queue, Service, Location, User } = require('../models');
const { authenticateToken, requireRole, requireLocationAdmin } = require('../middleware/auth');

const router = express.Router();

// Get dashboard statistics (admin only)
router.get('/dashboard', authenticateToken, requireRole('business_admin', 'system_admin'), async (req, res) => {
  try {
    const { location_id, start_date, end_date } = req.query;

    const today = new Date().toISOString().split('T')[0];
    const startDate = start_date || today;
    const endDate = end_date || today;

    let whereClause = {
      appointment_date: {
        [Op.between]: [startDate, endDate]
      }
    };

    // Filter by location for business admins
    if (req.user.role === 'business_admin') {
      const userLocations = await Location.findAll({
        where: { admin_user_id: req.user.id },
        attributes: ['id']
      });
      whereClause.location_id = userLocations.map(loc => loc.id);
    } else if (location_id) {
      whereClause.location_id = location_id;
    }

    // Total appointments
    const totalAppointments = await Appointment.count({ where: whereClause });

    // Appointments by status
    const appointmentsByStatus = await Appointment.findAll({
      where: whereClause,
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Today's queue statistics
    const todayQueueStats = await Queue.findAll({
      where: {
        queue_date: today,
        ...(whereClause.location_id && { location_id: whereClause.location_id })
      },
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Service popularity
    const serviceStats = await Appointment.findAll({
      where: whereClause,
      include: [
        {
          model: Service,
          as: 'service',
          attributes: ['name']
        }
      ],
      attributes: [
        'service_id',
        [sequelize.fn('COUNT', sequelize.col('Appointment.id')), 'count']
      ],
      group: ['service_id', 'service.name'],
      order: [[sequelize.fn('COUNT', sequelize.col('Appointment.id')), 'DESC']],
      limit: 10,
      raw: true
    });

    // Revenue (if payments are tracked)
    const revenue = await Appointment.sum('payment_amount', {
      where: {
        ...whereClause,
        payment_status: 'paid'
      }
    });

    res.json({
      success: true,
      data: {
        period: { start_date: startDate, end_date: endDate },
        total_appointments: totalAppointments,
        appointments_by_status: appointmentsByStatus,
        today_queue_stats: todayQueueStats,
        popular_services: serviceStats,
        total_revenue: revenue || 0
      }
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics'
    });
  }
});

// Get detailed appointment report
router.get('/appointments', authenticateToken, requireRole('business_admin', 'system_admin'), async (req, res) => {
  try {
    const {
      location_id,
      service_id,
      start_date,
      end_date,
      status,
      page = 1,
      limit = 50
    } = req.query;

    const offset = (page - 1) * limit;

    let whereClause = {};

    if (start_date || end_date) {
      whereClause.appointment_date = {};
      if (start_date) whereClause.appointment_date[Op.gte] = start_date;
      if (end_date) whereClause.appointment_date[Op.lte] = end_date;
    }

    if (status) whereClause.status = status;
    if (service_id) whereClause.service_id = service_id;

    // Filter by location for business admins
    if (req.user.role === 'business_admin') {
      const userLocations = await Location.findAll({
        where: { admin_user_id: req.user.id },
        attributes: ['id']
      });
      whereClause.location_id = userLocations.map(loc => loc.id);
    } else if (location_id) {
      whereClause.location_id = location_id;
    }

    const { count, rows: appointments } = await Appointment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'phone']
        },
        {
          model: Service,
          as: 'service',
          attributes: ['id', 'name', 'category']
        },
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name', 'district']
        },
        {
          model: Queue,
          as: 'queue',
          required: false
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['appointment_date', 'DESC'], ['appointment_time', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        appointments,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get appointment report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch appointment report'
    });
  }
});

// Get queue performance report
router.get('/queue-performance', authenticateToken, requireRole('business_admin', 'system_admin'), async (req, res) => {
  try {
    const { location_id, start_date, end_date } = req.query;

    const today = new Date().toISOString().split('T')[0];
    const startDate = start_date || today;
    const endDate = end_date || today;

    let whereClause = {
      queue_date: {
        [Op.between]: [startDate, endDate]
      }
    };

    // Filter by location for business admins
    if (req.user.role === 'business_admin') {
      const userLocations = await Location.findAll({
        where: { admin_user_id: req.user.id },
        attributes: ['id']
      });
      whereClause.location_id = userLocations.map(loc => loc.id);
    } else if (location_id) {
      whereClause.location_id = location_id;
    }

    // Average wait times
    const avgWaitTimes = await Queue.findAll({
      where: {
        ...whereClause,
        service_started_at: { [Op.not]: null }
      },
      include: [
        {
          model: Location,
          as: 'location',
          attributes: ['name']
        }
      ],
      attributes: [
        'location_id',
        [sequelize.fn('AVG',
          sequelize.literal('TIMESTAMPDIFF(MINUTE, created_at, service_started_at)')
        ), 'avg_wait_time'],
        [sequelize.fn('COUNT', sequelize.col('Queue.id')), 'total_served']
      ],
      group: ['location_id', 'location.name'],
      raw: true
    });

    // Service times
    const avgServiceTimes = await Queue.findAll({
      where: {
        ...whereClause,
        service_started_at: { [Op.not]: null },
        service_completed_at: { [Op.not]: null }
      },
      include: [
        {
          model: Service,
          as: 'service',
          attributes: ['name']
        }
      ],
      attributes: [
        'service_id',
        [sequelize.fn('AVG',
          sequelize.literal('TIMESTAMPDIFF(MINUTE, service_started_at, service_completed_at)')
        ), 'avg_service_time'],
        [sequelize.fn('COUNT', sequelize.col('Queue.id')), 'total_completed']
      ],
      group: ['service_id', 'service.name'],
      raw: true
    });

    // Daily queue volumes
    const dailyVolumes = await Queue.findAll({
      where: whereClause,
      attributes: [
        'queue_date',
        [sequelize.fn('COUNT', sequelize.col('id')), 'total_queue_items']
      ],
      group: ['queue_date'],
      order: [['queue_date', 'ASC']],
      raw: true
    });

    res.json({
      success: true,
      data: {
        period: { start_date: startDate, end_date: endDate },
        average_wait_times: avgWaitTimes,
        average_service_times: avgServiceTimes,
        daily_volumes: dailyVolumes
      }
    });
  } catch (error) {
    console.error('Get queue performance report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch queue performance report'
    });
  }
});

// Get user activity report (system admin only)
router.get('/user-activity', authenticateToken, requireRole('system_admin'), async (req, res) => {
  try {
    const { start_date, end_date, user_role } = req.query;

    const today = new Date().toISOString().split('T')[0];
    const startDate = start_date || today;
    const endDate = end_date || today;

    let userWhereClause = {};
    if (user_role) userWhereClause.role = user_role;

    // User registration trends
    const userRegistrations = await User.findAll({
      where: {
        ...userWhereClause,
        created_at: {
          [Op.between]: [startDate + ' 00:00:00', endDate + ' 23:59:59']
        }
      },
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        'role',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: [sequelize.fn('DATE', sequelize.col('created_at')), 'role'],
      order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
      raw: true
    });

    // Active users (users who made appointments)
    const activeUsers = await User.findAll({
      where: userWhereClause,
      include: [
        {
          model: Appointment,
          as: 'appointments',
          where: {
            appointment_date: {
              [Op.between]: [startDate, endDate]
            }
          },
          required: true
        }
      ],
      attributes: [
        'role',
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('User.id'))), 'active_users']
      ],
      group: ['role'],
      raw: true
    });

    res.json({
      success: true,
      data: {
        period: { start_date: startDate, end_date: endDate },
        user_registrations: userRegistrations,
        active_users: activeUsers
      }
    });
  } catch (error) {
    console.error('Get user activity report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user activity report'
    });
  }
});

// Get feedback summary
router.get('/feedback', authenticateToken, requireRole('business_admin', 'system_admin'), async (req, res) => {
  try {
    const { location_id, service_id, start_date, end_date } = req.query;

    let whereClause = {
      feedback_rating: { [Op.not]: null }
    };

    if (start_date || end_date) {
      whereClause.appointment_date = {};
      if (start_date) whereClause.appointment_date[Op.gte] = start_date;
      if (end_date) whereClause.appointment_date[Op.lte] = end_date;
    }

    if (service_id) whereClause.service_id = service_id;

    // Filter by location for business admins
    if (req.user.role === 'business_admin') {
      const userLocations = await Location.findAll({
        where: { admin_user_id: req.user.id },
        attributes: ['id']
      });
      whereClause.location_id = userLocations.map(loc => loc.id);
    } else if (location_id) {
      whereClause.location_id = location_id;
    }

    // Average ratings
    const avgRatings = await Appointment.findAll({
      where: whereClause,
      include: [
        {
          model: Service,
          as: 'service',
          attributes: ['name']
        },
        {
          model: Location,
          as: 'location',
          attributes: ['name']
        }
      ],
      attributes: [
        'service_id',
        'location_id',
        [sequelize.fn('AVG', sequelize.col('feedback_rating')), 'avg_rating'],
        [sequelize.fn('COUNT', sequelize.col('Appointment.id')), 'total_feedback']
      ],
      group: ['service_id', 'location_id', 'service.name', 'location.name'],
      raw: true
    });

    // Rating distribution
    const ratingDistribution = await Appointment.findAll({
      where: whereClause,
      attributes: [
        'feedback_rating',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['feedback_rating'],
      order: [['feedback_rating', 'ASC']],
      raw: true
    });

    // Recent feedback with comments
    const recentFeedback = await Appointment.findAll({
      where: {
        ...whereClause,
        feedback_comment: { [Op.not]: null }
      },
      include: [
        {
          model: Service,
          as: 'service',
          attributes: ['name']
        },
        {
          model: Location,
          as: 'location',
          attributes: ['name']
        }
      ],
      attributes: ['feedback_rating', 'feedback_comment', 'appointment_date'],
      order: [['updated_at', 'DESC']],
      limit: 20
    });

    res.json({
      success: true,
      data: {
        average_ratings: avgRatings,
        rating_distribution: ratingDistribution,
        recent_feedback: recentFeedback
      }
    });
  } catch (error) {
    console.error('Get feedback report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch feedback report'
    });
  }
});

// Export report data (CSV format)
router.get('/export/:reportType', authenticateToken, requireRole('business_admin', 'system_admin'), async (req, res) => {
  try {
    const { reportType } = req.params;
    const { location_id, start_date, end_date, format = 'json' } = req.query;

    // This is a simplified export - in production you'd want to use a proper CSV library
    let data = [];
    let filename = `${reportType}_report_${new Date().toISOString().split('T')[0]}`;

    switch (reportType) {
      case 'appointments':
        // Implementation would fetch and format appointment data
        break;
      case 'queue-performance':
        // Implementation would fetch and format queue data
        break;
      case 'feedback':
        // Implementation would fetch and format feedback data
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid report type'
        });
    }

    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      // Convert data to CSV format
      res.send('CSV data would be here');
    } else {
      res.json({
        success: true,
        data: data,
        filename: filename
      });
    }
  } catch (error) {
    console.error('Export report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export report'
    });
  }
});

module.exports = router;
