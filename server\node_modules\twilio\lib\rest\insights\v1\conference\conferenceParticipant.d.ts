/// <reference types="node" />
import { inspect, InspectOptions } from "util";
import Page, { TwilioResponsePayload } from "../../../../base/Page";
import Response from "../../../../http/response";
import V1 from "../../V1";
export type ConferenceParticipantCallDirection = "inbound" | "outbound";
export type ConferenceParticipantCallStatus = "answered" | "completed" | "busy" | "fail" | "noanswer" | "ringing" | "canceled";
export type ConferenceParticipantCallType = "carrier" | "client" | "sip";
export type ConferenceParticipantJitterBufferSize = "large" | "small" | "medium" | "off";
export type ConferenceParticipantProcessingState = "complete" | "in_progress" | "timeout";
export type ConferenceParticipantRegion = "us1" | "us2" | "au1" | "br1" | "ie1" | "jp1" | "sg1" | "de1";
/**
 * Options to pass to fetch a ConferenceParticipantInstance
 */
export interface ConferenceParticipantContextFetchOptions {
    /** Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc. */
    events?: string;
    /** Object. Contains participant call quality metrics. */
    metrics?: string;
}
/**
 * Options to pass to each
 */
export interface ConferenceParticipantListInstanceEachOptions {
    /** The unique SID identifier of the Participant. */
    participantSid?: string;
    /** User-specified label for a participant. */
    label?: string;
    /** Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc. */
    events?: string;
    /** How many resources to return in each list page. The default is 50, and the maximum is 1000. */
    pageSize?: number;
    /** Function to process each record. If this and a positional callback are passed, this one will be used */
    callback?: (item: ConferenceParticipantInstance, done: (err?: Error) => void) => void;
    /** Function to be called upon completion of streaming */
    done?: Function;
    /** Upper limit for the number of records to return. each() guarantees never to return more than limit. Default is no limit */
    limit?: number;
}
/**
 * Options to pass to list
 */
export interface ConferenceParticipantListInstanceOptions {
    /** The unique SID identifier of the Participant. */
    participantSid?: string;
    /** User-specified label for a participant. */
    label?: string;
    /** Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc. */
    events?: string;
    /** How many resources to return in each list page. The default is 50, and the maximum is 1000. */
    pageSize?: number;
    /** Upper limit for the number of records to return. list() guarantees never to return more than limit. Default is no limit */
    limit?: number;
}
/**
 * Options to pass to page
 */
export interface ConferenceParticipantListInstancePageOptions {
    /** The unique SID identifier of the Participant. */
    participantSid?: string;
    /** User-specified label for a participant. */
    label?: string;
    /** Conference events generated by application or participant activity; e.g. `hold`, `mute`, etc. */
    events?: string;
    /** How many resources to return in each list page. The default is 50, and the maximum is 1000. */
    pageSize?: number;
    /** Page Number, this value is simply for client state */
    pageNumber?: number;
    /** PageToken provided by the API */
    pageToken?: string;
}
export interface ConferenceParticipantContext {
    /**
     * Fetch a ConferenceParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConferenceParticipantInstance
     */
    fetch(callback?: (error: Error | null, item?: ConferenceParticipantInstance) => any): Promise<ConferenceParticipantInstance>;
    /**
     * Fetch a ConferenceParticipantInstance
     *
     * @param params - Parameter for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConferenceParticipantInstance
     */
    fetch(params: ConferenceParticipantContextFetchOptions, callback?: (error: Error | null, item?: ConferenceParticipantInstance) => any): Promise<ConferenceParticipantInstance>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export interface ConferenceParticipantContextSolution {
    conferenceSid: string;
    participantSid: string;
}
export declare class ConferenceParticipantContextImpl implements ConferenceParticipantContext {
    protected _version: V1;
    protected _solution: ConferenceParticipantContextSolution;
    protected _uri: string;
    constructor(_version: V1, conferenceSid: string, participantSid: string);
    fetch(params?: ConferenceParticipantContextFetchOptions | ((error: Error | null, item?: ConferenceParticipantInstance) => any), callback?: (error: Error | null, item?: ConferenceParticipantInstance) => any): Promise<ConferenceParticipantInstance>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): ConferenceParticipantContextSolution;
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
interface ConferenceParticipantPayload extends TwilioResponsePayload {
    participants: ConferenceParticipantResource[];
}
interface ConferenceParticipantResource {
    participant_sid: string;
    label: string;
    conference_sid: string;
    call_sid: string;
    account_sid: string;
    call_direction: ConferenceParticipantCallDirection;
    from: string;
    to: string;
    call_status: ConferenceParticipantCallStatus;
    country_code: string;
    is_moderator: boolean;
    join_time: Date;
    leave_time: Date;
    duration_seconds: number;
    outbound_queue_length: number;
    outbound_time_in_queue: number;
    jitter_buffer_size: ConferenceParticipantJitterBufferSize;
    is_coach: boolean;
    coached_participants: Array<string>;
    participant_region: ConferenceParticipantRegion;
    conference_region: ConferenceParticipantRegion;
    call_type: ConferenceParticipantCallType;
    processing_state: ConferenceParticipantProcessingState;
    properties: any;
    events: any;
    metrics: any;
    url: string;
}
export declare class ConferenceParticipantInstance {
    protected _version: V1;
    protected _solution: ConferenceParticipantContextSolution;
    protected _context?: ConferenceParticipantContext;
    constructor(_version: V1, payload: ConferenceParticipantResource, conferenceSid: string, participantSid?: string);
    /**
     * SID for this participant.
     */
    participantSid: string;
    /**
     * The user-specified label of this participant.
     */
    label: string;
    /**
     * The unique SID identifier of the Conference.
     */
    conferenceSid: string;
    /**
     * Unique SID identifier of the call that generated the Participant resource.
     */
    callSid: string;
    /**
     * The unique SID identifier of the Account.
     */
    accountSid: string;
    callDirection: ConferenceParticipantCallDirection;
    /**
     * Caller ID of the calling party.
     */
    from: string;
    /**
     * Called party.
     */
    to: string;
    callStatus: ConferenceParticipantCallStatus;
    /**
     * ISO alpha-2 country code of the participant based on caller ID or called number.
     */
    countryCode: string;
    /**
     * Boolean. Indicates whether participant had startConferenceOnEnter=true or endConferenceOnExit=true.
     */
    isModerator: boolean;
    /**
     * ISO 8601 timestamp of participant join event.
     */
    joinTime: Date;
    /**
     * ISO 8601 timestamp of participant leave event.
     */
    leaveTime: Date;
    /**
     * Participant durations in seconds.
     */
    durationSeconds: number;
    /**
     * Add Participant API only. Estimated time in queue at call creation.
     */
    outboundQueueLength: number;
    /**
     * Add Participant API only. Actual time in queue in seconds.
     */
    outboundTimeInQueue: number;
    jitterBufferSize: ConferenceParticipantJitterBufferSize;
    /**
     * Boolean. Indicated whether participant was a coach.
     */
    isCoach: boolean;
    /**
     * Call SIDs coached by this participant.
     */
    coachedParticipants: Array<string>;
    participantRegion: ConferenceParticipantRegion;
    conferenceRegion: ConferenceParticipantRegion;
    callType: ConferenceParticipantCallType;
    processingState: ConferenceParticipantProcessingState;
    /**
     * Participant properties and metadata.
     */
    properties: any;
    /**
     * Object containing information of actions taken by participants. Contains a dictionary of URL links to nested resources of this Conference Participant.
     */
    events: any;
    /**
     * Object. Contains participant call quality metrics.
     */
    metrics: any;
    /**
     * The URL of this resource.
     */
    url: string;
    private get _proxy();
    /**
     * Fetch a ConferenceParticipantInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConferenceParticipantInstance
     */
    fetch(callback?: (error: Error | null, item?: ConferenceParticipantInstance) => any): Promise<ConferenceParticipantInstance>;
    /**
     * Fetch a ConferenceParticipantInstance
     *
     * @param params - Parameter for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConferenceParticipantInstance
     */
    fetch(params: ConferenceParticipantContextFetchOptions, callback?: (error: Error | null, item?: ConferenceParticipantInstance) => any): Promise<ConferenceParticipantInstance>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): {
        participantSid: string;
        label: string;
        conferenceSid: string;
        callSid: string;
        accountSid: string;
        callDirection: ConferenceParticipantCallDirection;
        from: string;
        to: string;
        callStatus: ConferenceParticipantCallStatus;
        countryCode: string;
        isModerator: boolean;
        joinTime: Date;
        leaveTime: Date;
        durationSeconds: number;
        outboundQueueLength: number;
        outboundTimeInQueue: number;
        jitterBufferSize: ConferenceParticipantJitterBufferSize;
        isCoach: boolean;
        coachedParticipants: string[];
        participantRegion: ConferenceParticipantRegion;
        conferenceRegion: ConferenceParticipantRegion;
        callType: ConferenceParticipantCallType;
        processingState: ConferenceParticipantProcessingState;
        properties: any;
        events: any;
        metrics: any;
        url: string;
    };
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
export interface ConferenceParticipantSolution {
    conferenceSid: string;
}
export interface ConferenceParticipantListInstance {
    _version: V1;
    _solution: ConferenceParticipantSolution;
    _uri: string;
    (participantSid: string): ConferenceParticipantContext;
    get(participantSid: string): ConferenceParticipantContext;
    /**
     * Streams ConferenceParticipantInstance records from the API.
     *
     * This operation lazily loads records as efficiently as possible until the limit
     * is reached.
     *
     * The results are passed into the callback function, so this operation is memory
     * efficient.
     *
     * If a function is passed as the first argument, it will be used as the callback
     * function.
     *
     * @param { ConferenceParticipantListInstanceEachOptions } [params] - Options for request
     * @param { function } [callback] - Function to process each record
     */
    each(callback?: (item: ConferenceParticipantInstance, done: (err?: Error) => void) => void): void;
    each(params: ConferenceParticipantListInstanceEachOptions, callback?: (item: ConferenceParticipantInstance, done: (err?: Error) => void) => void): void;
    /**
     * Retrieve a single target page of ConferenceParticipantInstance records from the API.
     *
     * The request is executed immediately.
     *
     * @param { string } [targetUrl] - API-generated URL for the requested results page
     * @param { function } [callback] - Callback to handle list of records
     */
    getPage(targetUrl: string, callback?: (error: Error | null, items: ConferenceParticipantPage) => any): Promise<ConferenceParticipantPage>;
    /**
     * Lists ConferenceParticipantInstance records from the API as a list.
     *
     * If a function is passed as the first argument, it will be used as the callback
     * function.
     *
     * @param { ConferenceParticipantListInstanceOptions } [params] - Options for request
     * @param { function } [callback] - Callback to handle list of records
     */
    list(callback?: (error: Error | null, items: ConferenceParticipantInstance[]) => any): Promise<ConferenceParticipantInstance[]>;
    list(params: ConferenceParticipantListInstanceOptions, callback?: (error: Error | null, items: ConferenceParticipantInstance[]) => any): Promise<ConferenceParticipantInstance[]>;
    /**
     * Retrieve a single page of ConferenceParticipantInstance records from the API.
     *
     * The request is executed immediately.
     *
     * If a function is passed as the first argument, it will be used as the callback
     * function.
     *
     * @param { ConferenceParticipantListInstancePageOptions } [params] - Options for request
     * @param { function } [callback] - Callback to handle list of records
     */
    page(callback?: (error: Error | null, items: ConferenceParticipantPage) => any): Promise<ConferenceParticipantPage>;
    page(params: ConferenceParticipantListInstancePageOptions, callback?: (error: Error | null, items: ConferenceParticipantPage) => any): Promise<ConferenceParticipantPage>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export declare function ConferenceParticipantListInstance(version: V1, conferenceSid: string): ConferenceParticipantListInstance;
export declare class ConferenceParticipantPage extends Page<V1, ConferenceParticipantPayload, ConferenceParticipantResource, ConferenceParticipantInstance> {
    /**
     * Initialize the ConferenceParticipantPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version: V1, response: Response<string>, solution: ConferenceParticipantSolution);
    /**
     * Build an instance of ConferenceParticipantInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload: ConferenceParticipantResource): ConferenceParticipantInstance;
    [inspect.custom](depth: any, options: InspectOptions): string;
}
export {};
