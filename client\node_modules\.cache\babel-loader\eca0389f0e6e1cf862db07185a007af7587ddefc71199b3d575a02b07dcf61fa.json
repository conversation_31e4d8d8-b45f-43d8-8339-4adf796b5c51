{"ast": null, "code": "import { isCSSVar } from '../../render/dom/is-css-var.mjs';\nimport { transformProps } from '../../render/utils/keys-transform.mjs';\nimport { resolveElements } from '../../utils/resolve-elements.mjs';\nimport { MotionValue } from '../../value/index.mjs';\nimport { MotionValueState } from '../MotionValueState.mjs';\nimport { buildTransform } from './transform.mjs';\nconst stateMap = new WeakMap();\nfunction styleEffect(subject, values) {\n  const elements = resolveElements(subject);\n  const subscriptions = [];\n  for (let i = 0; i < elements.length; i++) {\n    const element = elements[i];\n    const state = stateMap.get(element) ?? new MotionValueState();\n    stateMap.set(element, state);\n    for (const key in values) {\n      const value = values[key];\n      const remove = addValue(element, state, key, value);\n      subscriptions.push(remove);\n    }\n  }\n  return () => {\n    for (const cancel of subscriptions) cancel();\n  };\n}\nfunction addValue(element, state, key, value) {\n  let render = undefined;\n  let computed = undefined;\n  if (transformProps.has(key)) {\n    if (!state.get(\"transform\")) {\n      state.set(\"transform\", new MotionValue(\"none\"), () => {\n        element.style.transform = buildTransform(state);\n      });\n    }\n    computed = state.get(\"transform\");\n  } else if (isCSSVar(key)) {\n    render = () => {\n      element.style.setProperty(key, state.latest[key]);\n    };\n  } else {\n    render = () => {\n      element.style[key] = state.latest[key];\n    };\n  }\n  return state.set(key, value, render, computed);\n}\nexport { styleEffect };", "map": {"version": 3, "names": ["isCSSVar", "transformProps", "resolveElements", "MotionValue", "MotionValueState", "buildTransform", "stateMap", "WeakMap", "styleEffect", "subject", "values", "elements", "subscriptions", "i", "length", "element", "state", "get", "set", "key", "value", "remove", "addValue", "push", "cancel", "render", "undefined", "computed", "has", "style", "transform", "setProperty", "latest"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/motion-dom/dist/es/effects/style/index.mjs"], "sourcesContent": ["import { isCSSVar } from '../../render/dom/is-css-var.mjs';\nimport { transformProps } from '../../render/utils/keys-transform.mjs';\nimport { resolveElements } from '../../utils/resolve-elements.mjs';\nimport { MotionValue } from '../../value/index.mjs';\nimport { MotionValueState } from '../MotionValueState.mjs';\nimport { buildTransform } from './transform.mjs';\n\nconst stateMap = new WeakMap();\nfunction styleEffect(subject, values) {\n    const elements = resolveElements(subject);\n    const subscriptions = [];\n    for (let i = 0; i < elements.length; i++) {\n        const element = elements[i];\n        const state = stateMap.get(element) ?? new MotionValueState();\n        stateMap.set(element, state);\n        for (const key in values) {\n            const value = values[key];\n            const remove = addValue(element, state, key, value);\n            subscriptions.push(remove);\n        }\n    }\n    return () => {\n        for (const cancel of subscriptions)\n            cancel();\n    };\n}\nfunction addValue(element, state, key, value) {\n    let render = undefined;\n    let computed = undefined;\n    if (transformProps.has(key)) {\n        if (!state.get(\"transform\")) {\n            state.set(\"transform\", new MotionValue(\"none\"), () => {\n                element.style.transform = buildTransform(state);\n            });\n        }\n        computed = state.get(\"transform\");\n    }\n    else if (isCSSVar(key)) {\n        render = () => {\n            element.style.setProperty(key, state.latest[key]);\n        };\n    }\n    else {\n        render = () => {\n            element.style[key] = state.latest[key];\n        };\n    }\n    return state.set(key, value, render, computed);\n}\n\nexport { styleEffect };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,cAAc,QAAQ,iBAAiB;AAEhD,MAAMC,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC9B,SAASC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAClC,MAAMC,QAAQ,GAAGT,eAAe,CAACO,OAAO,CAAC;EACzC,MAAMG,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAME,OAAO,GAAGJ,QAAQ,CAACE,CAAC,CAAC;IAC3B,MAAMG,KAAK,GAAGV,QAAQ,CAACW,GAAG,CAACF,OAAO,CAAC,IAAI,IAAIX,gBAAgB,CAAC,CAAC;IAC7DE,QAAQ,CAACY,GAAG,CAACH,OAAO,EAAEC,KAAK,CAAC;IAC5B,KAAK,MAAMG,GAAG,IAAIT,MAAM,EAAE;MACtB,MAAMU,KAAK,GAAGV,MAAM,CAACS,GAAG,CAAC;MACzB,MAAME,MAAM,GAAGC,QAAQ,CAACP,OAAO,EAAEC,KAAK,EAAEG,GAAG,EAAEC,KAAK,CAAC;MACnDR,aAAa,CAACW,IAAI,CAACF,MAAM,CAAC;IAC9B;EACJ;EACA,OAAO,MAAM;IACT,KAAK,MAAMG,MAAM,IAAIZ,aAAa,EAC9BY,MAAM,CAAC,CAAC;EAChB,CAAC;AACL;AACA,SAASF,QAAQA,CAACP,OAAO,EAAEC,KAAK,EAAEG,GAAG,EAAEC,KAAK,EAAE;EAC1C,IAAIK,MAAM,GAAGC,SAAS;EACtB,IAAIC,QAAQ,GAAGD,SAAS;EACxB,IAAIzB,cAAc,CAAC2B,GAAG,CAACT,GAAG,CAAC,EAAE;IACzB,IAAI,CAACH,KAAK,CAACC,GAAG,CAAC,WAAW,CAAC,EAAE;MACzBD,KAAK,CAACE,GAAG,CAAC,WAAW,EAAE,IAAIf,WAAW,CAAC,MAAM,CAAC,EAAE,MAAM;QAClDY,OAAO,CAACc,KAAK,CAACC,SAAS,GAAGzB,cAAc,CAACW,KAAK,CAAC;MACnD,CAAC,CAAC;IACN;IACAW,QAAQ,GAAGX,KAAK,CAACC,GAAG,CAAC,WAAW,CAAC;EACrC,CAAC,MACI,IAAIjB,QAAQ,CAACmB,GAAG,CAAC,EAAE;IACpBM,MAAM,GAAGA,CAAA,KAAM;MACXV,OAAO,CAACc,KAAK,CAACE,WAAW,CAACZ,GAAG,EAAEH,KAAK,CAACgB,MAAM,CAACb,GAAG,CAAC,CAAC;IACrD,CAAC;EACL,CAAC,MACI;IACDM,MAAM,GAAGA,CAAA,KAAM;MACXV,OAAO,CAACc,KAAK,CAACV,GAAG,CAAC,GAAGH,KAAK,CAACgB,MAAM,CAACb,GAAG,CAAC;IAC1C,CAAC;EACL;EACA,OAAOH,KAAK,CAACE,GAAG,CAACC,GAAG,EAAEC,KAAK,EAAEK,MAAM,EAAEE,QAAQ,CAAC;AAClD;AAEA,SAASnB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}