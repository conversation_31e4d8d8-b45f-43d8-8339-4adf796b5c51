{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\nconst ReorderContext = createContext(null);\nexport { ReorderContext };", "map": {"version": 3, "names": ["createContext", "ReorderContext"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/framer-motion/dist/es/context/ReorderContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst ReorderContext = createContext(null);\n\nexport { ReorderContext };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,cAAc,GAAGD,aAAa,CAAC,IAAI,CAAC;AAE1C,SAASC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}