require('dotenv').config();
const { User } = require('./models');
const { sequelize } = require('./models');

async function createUsers() {
  try {
    console.log('🔗 Connecting to database...');
    await sequelize.sync({ force: false });
    console.log('✅ Database connected');

    console.log('👤 Creating test users...');

    // Create customer
    const [customer, customerCreated] = await User.findOrCreate({
      where: { phone: '+250788000003' },
      defaults: {
        name: '<PERSON> Uwimana',
        phone: '+250788000003',
        email: '<EMAIL>',
        password: 'customer123',
        role: 'customer',
        is_verified: true,
        is_active: true
      }
    });

    if (customerCreated) {
      console.log('✅ Customer created:', customer.name);
    } else {
      console.log('✅ Customer already exists:', customer.name);
    }

    // Create admin
    const [admin, adminCreated] = await User.findOrCreate({
      where: { phone: '+250788000001' },
      defaults: {
        name: 'System Administrator',
        phone: '+250788000001',
        email: '<EMAIL>',
        password: 'admin123456',
        role: 'system_admin',
        is_verified: true,
        is_active: true
      }
    });

    if (adminCreated) {
      console.log('✅ Admin created:', admin.name);
    } else {
      console.log('✅ Admin already exists:', admin.name);
    }

    console.log('\n📋 Test Credentials:');
    console.log('Customer: +250788000003 / customer123');
    console.log('Admin: +250788000001 / admin123456');
    
    console.log('\n🧪 Testing password verification...');
    const testCustomer = await User.findOne({ where: { phone: '+250788000003' } });
    if (testCustomer) {
      const isValid = await testCustomer.comparePassword('customer123');
      console.log('Password test result:', isValid ? '✅ PASS' : '❌ FAIL');
    }

    console.log('\n✅ User creation completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating users:', error);
    console.error('Error details:', error.message);
    process.exit(1);
  }
}

createUsers();
