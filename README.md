# 🎯 Umurongo - Queue Management System

## 🚀 **Quick Start (One Command)**

```bash
npm start
```

This will automatically:
- ✅ Start backend server
- ✅ Start frontend server  
- ✅ Run system health checks
- ✅ Open your browser to the app

## 📋 **Alternative Commands**

```bash
# Check system health
npm run check

# Start backend only
npm run backend

# Start frontend only
npm run frontend

# Install all dependencies
npm run install-all
```

## 🎯 **Demo Credentials**

**Customer Account:**
- Phone: `+************`
- Password: `customer123`

**Admin Account:**
- Phone: `+************`
- Password: `admin123456`

## 🌟 **Features**

### ✅ **Fully Functional Pages**
- **🏠 Landing Page** - Beautiful animated homepage
- **🔐 Authentication** - Login/Register with validation
- **📊 Dashboard** - Real-time stats and quick actions
- **📍 Locations** - Browse and search service locations
- **🛠️ Services** - Book appointments with real data
- **📅 Appointments** - Manage bookings and history
- **⏰ Queue** - Real-time queue management
- **👤 Profile** - User account management
- **🔧 Admin** - System administration

### 🎨 **Design Features**
- **Modern UI** with Tailwind CSS
- **Smooth animations** with Framer Motion
- **Responsive design** for all devices
- **Real-time updates** and notifications
- **Interactive components** and modals

### 🔧 **Technical Features**
- **React 19** with modern hooks
- **Node.js/Express** backend
- **SQLite** database (development)
- **JWT** authentication
- **SMS** notifications (mock mode)
- **Real-time** queue updates

## 🏗️ **Project Structure**

```
umurongo/
├── 📁 server/              # Backend (Node.js/Express)
│   ├── server.js          # Main server file
│   ├── models/            # Database models
│   ├── routes/            # API endpoints
│   └── umurongo_dev.db    # SQLite database
├── 📁 client/              # Frontend (React)
│   ├── src/
│   │   ├── pages/         # Page components
│   │   ├── components/    # Reusable components
│   │   └── contexts/      # State management
│   └── public/            # Static assets
├── 🔧 start-system.js     # Automated startup
├── 🔍 system-check.js     # Health checker
└── 📖 Documentation files
```

## 🔍 **Troubleshooting**

### **Issue: "Failed to load data"**
```bash
# Check system health
npm run check

# If backend not running
npm run backend
```

### **Issue: "Cannot connect to server"**
```bash
# Check if ports are free
netstat -an | findstr :5001
netstat -an | findstr :3000

# Restart system
npm start
```

### **Issue: Login not working**
```bash
# Clear browser storage
# In browser console:
localStorage.clear();
location.reload();

# Use demo credentials
```

## 🐛 **Debug Tools**

### **System Health Check**
```bash
npm run check
```

### **Debug Panel**
1. Login to the app
2. Look for 🐛 button (bottom-right)
3. Click "Run Tests"
4. Check results

### **Manual API Testing**
```bash
# Health check
curl http://localhost:5001/health

# Login test
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+************","password":"customer123"}'
```

## 📱 **How to Use**

### **1. Customer Journey**
1. **Register/Login** with phone number
2. **Browse Locations** to find services
3. **Book Appointments** with preferred time
4. **Join Queues** for walk-in services
5. **Track Progress** in real-time

### **2. Admin Journey**
1. **Login** with admin credentials
2. **Manage Locations** and services
3. **Monitor Queues** and appointments
4. **View Analytics** and reports
5. **Handle Customer** requests

## 🎯 **API Endpoints**

### **Authentication**
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile

### **Locations & Services**
- `GET /api/locations` - Get all locations
- `GET /api/services` - Get all services
- `GET /api/services/:id/availability` - Check availability

### **Appointments**
- `POST /api/appointments` - Book appointment
- `GET /api/appointments` - Get user appointments
- `PUT /api/appointments/:id/cancel` - Cancel appointment

### **Queue Management**
- `POST /api/queues/join` - Join queue
- `GET /api/queues/my-position` - Get position
- `POST /api/queues/leave` - Leave queue

## 🚀 **Deployment**

### **Development**
```bash
npm start
```

### **Production**
1. **Backend**: Deploy to Heroku/DigitalOcean
2. **Frontend**: Deploy to Netlify/Vercel
3. **Database**: Switch to MySQL/PostgreSQL
4. **SMS**: Configure Twilio credentials

## 📊 **System Requirements**

- **Node.js** 16+ 
- **npm** 8+
- **Modern browser** (Chrome, Firefox, Safari)
- **4GB RAM** minimum
- **Ports 3000 & 5001** available

## 🎉 **Success Indicators**

You know everything is working when:
- ✅ `npm start` completes without errors
- ✅ Browser opens to `http://localhost:3000`
- ✅ Can login with demo credentials
- ✅ Dashboard shows welcome message
- ✅ All pages load with real data
- ✅ Debug panel shows green dots

## 📞 **Support**

### **Quick Help**
1. Run `npm run check` for diagnostics
2. Check browser console for errors
3. Verify both servers are running
4. Use demo credentials for testing

### **Documentation**
- `SYSTEM_FIX_GUIDE.md` - Detailed troubleshooting
- `REAL_DATA_IMPLEMENTATION.md` - Feature overview
- `client/FRONTEND_README.md` - Frontend guide
- `server/API_DOCUMENTATION.md` - API reference

## 🏆 **Built With**

- **Frontend**: React 19, Tailwind CSS, Framer Motion
- **Backend**: Node.js, Express, Sequelize
- **Database**: SQLite (dev), MySQL (prod)
- **Authentication**: JWT tokens
- **SMS**: Twilio integration
- **Deployment**: Docker ready

---

**🎯 Ready to revolutionize queue management in Rwanda! 🇷🇼**

**Start with: `npm start` and visit `http://localhost:3000`**
