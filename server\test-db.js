// Simple database test
require('dotenv').config();

async function testDatabase() {
  try {
    console.log('🧪 Testing database connection...');
    
    // Test database config
    const sequelize = require('./config/database');
    console.log('✅ Database config loaded');
    
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Test model loading
    const { User } = require('./models');
    console.log('✅ User model loaded');
    
    // Test sync
    await sequelize.sync({ force: true }); // This will recreate tables
    console.log('✅ Database sync successful');
    
    // Test creating a user
    const testUser = await User.create({
      name: 'Test User',
      phone: '+250788999999',
      password: 'test123456',
      role: 'customer',
      is_verified: true
    });
    console.log('✅ Test user created:', testUser.name);
    
    // Clean up
    await sequelize.close();
    console.log('✅ Database connection closed');
    
    console.log('\n🎉 Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('Full error:', error);
  }
}

testDatabase();
