# Umurongo Backend API

Umurongo is a comprehensive queue and appointment management system designed for Rwanda. This backend API provides all the necessary endpoints for managing appointments, queues, locations, services, and user authentication.

## Features

### Core Features
- **User Authentication**: Registration, login, phone verification with SMS
- **Location Management**: Health centers, clinics, salons, government offices
- **Service Management**: Different types of services with scheduling
- **Appointment Booking**: Advanced booking with time slot management
- **Queue Management**: Real-time queue tracking and notifications
- **SMS Notifications**: Automated SMS for appointments and queue updates
- **Role-based Access**: Customer, Business Admin, System Admin roles
- **Reports & Analytics**: Comprehensive reporting for admins

### Technical Features
- RESTful API with Express.js
- MySQL database with Sequelize ORM
- JWT authentication
- SMS integration with Twilio
- Input validation with Joi
- Rate limiting and security headers
- Comprehensive error handling

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd umurongo/server
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database Setup**
   - Create a MySQL database named `umurongo_db`
   - Update database credentials in `.env`

5. **Start the server**
   ```bash
   # Development
   npm run dev

   # Production
   npm start
   ```

## Environment Variables

```env
# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=umurongo_db
DB_USER=root
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# Server
PORT=5000
NODE_ENV=development

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Frontend
CLIENT_URL=http://localhost:3000
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/verify-phone` - Verify phone number
- `POST /api/auth/resend-verification` - Resend verification code
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - Logout user

### Users
- `GET /api/users` - Get all users (admin)
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/change-password` - Change password
- `GET /api/users/:id/appointments` - Get user appointments

### Locations
- `GET /api/locations` - Get all locations
- `GET /api/locations/:id` - Get location by ID
- `POST /api/locations` - Create location (admin)
- `PUT /api/locations/:id` - Update location (admin)
- `GET /api/locations/:id/stats` - Get location statistics

### Services
- `GET /api/services` - Get all services
- `GET /api/services/:id` - Get service by ID
- `POST /api/services` - Create service (admin)
- `PUT /api/services/:id` - Update service (admin)
- `GET /api/services/:id/availability` - Get service availability

### Appointments
- `GET /api/appointments` - Get appointments
- `GET /api/appointments/:id` - Get appointment by ID
- `POST /api/appointments` - Create appointment
- `PUT /api/appointments/:id/status` - Update appointment status (admin)
- `PUT /api/appointments/:id/cancel` - Cancel appointment
- `POST /api/appointments/:id/feedback` - Submit feedback

### Queues
- `GET /api/queues/location/:locationId` - Get location queue
- `GET /api/queues/my-position` - Get user's queue position
- `POST /api/queues/location/:locationId/call-next` - Call next in queue (admin)
- `PUT /api/queues/:id/status` - Update queue status (admin)
- `POST /api/queues/location/:locationId/notify` - Send queue notifications

### Reports
- `GET /api/reports/dashboard` - Dashboard statistics (admin)
- `GET /api/reports/appointments` - Appointment reports (admin)
- `GET /api/reports/queue-performance` - Queue performance (admin)
- `GET /api/reports/feedback` - Feedback summary (admin)

## Database Schema

### Users
- Authentication and profile information
- Role-based access (customer, business_admin, system_admin)
- Phone verification and preferences

### Locations
- Business locations (health centers, clinics, etc.)
- Operating hours and contact information
- Admin assignment

### Services
- Services offered at locations
- Pricing, duration, and availability
- Category-based organization

### Appointments
- User bookings with date/time
- Status tracking and notes
- Payment integration

### Queues
- Real-time queue management
- Position tracking and notifications
- Service time monitoring

### Payments
- Payment tracking and status
- Mobile money integration
- Refund management

## SMS Integration

The system uses Twilio for SMS notifications:
- Phone verification codes
- Appointment confirmations
- Queue position updates
- Service ready notifications
- Appointment reminders

## Security Features

- JWT token authentication
- Password hashing with bcrypt
- Rate limiting
- Input validation
- SQL injection prevention
- CORS configuration
- Security headers with Helmet

## Development

### Running Tests
```bash
npm test
```

### Database Migrations
```bash
npm run migrate
```

### Seeding Data
```bash
npm run seed
```

## Production Deployment

1. Set `NODE_ENV=production`
2. Configure production database
3. Set up SSL certificates
4. Configure reverse proxy (nginx)
5. Set up process manager (PM2)
6. Configure monitoring and logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
