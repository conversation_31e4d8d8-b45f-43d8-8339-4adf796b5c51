{"ast": null, "code": "import { interpolate } from './interpolate.mjs';\nfunction transform(...args) {\n  const useImmediate = !Array.isArray(args[0]);\n  const argOffset = useImmediate ? 0 : -1;\n  const inputValue = args[0 + argOffset];\n  const inputRange = args[1 + argOffset];\n  const outputRange = args[2 + argOffset];\n  const options = args[3 + argOffset];\n  const interpolator = interpolate(inputRange, outputRange, options);\n  return useImmediate ? interpolator(inputValue) : interpolator;\n}\nexport { transform };", "map": {"version": 3, "names": ["interpolate", "transform", "args", "useImmediate", "Array", "isArray", "argOffset", "inputValue", "inputRange", "outputRange", "options", "interpolator"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/motion-dom/dist/es/utils/transform.mjs"], "sourcesContent": ["import { interpolate } from './interpolate.mjs';\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = interpolate(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\nexport { transform };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAE/C,SAASC,SAASA,CAAC,GAAGC,IAAI,EAAE;EACxB,MAAMC,YAAY,GAAG,CAACC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMI,SAAS,GAAGH,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,MAAMI,UAAU,GAAGL,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACtC,MAAME,UAAU,GAAGN,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACtC,MAAMG,WAAW,GAAGP,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACvC,MAAMI,OAAO,GAAGR,IAAI,CAAC,CAAC,GAAGI,SAAS,CAAC;EACnC,MAAMK,YAAY,GAAGX,WAAW,CAACQ,UAAU,EAAEC,WAAW,EAAEC,OAAO,CAAC;EAClE,OAAOP,YAAY,GAAGQ,YAAY,CAACJ,UAAU,CAAC,GAAGI,YAAY;AACjE;AAEA,SAASV,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}