{"ast": null, "code": "import * as React from \"react\";\nfunction GiftTopIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.25 3v4.046a3 3 0 0 0-4.277 4.204H1.5v-6A2.25 2.25 0 0 1 3.75 3h7.5ZM12.75 3v4.011a3 3 0 0 1 4.239 4.239H22.5v-6A2.25 2.25 0 0 0 20.25 3h-7.5ZM22.5 12.75h-8.983a4.125 4.125 0 0 0 4.108 3.75.75.75 0 0 1 0 1.5 5.623 5.623 0 0 1-4.875-2.817V21h7.5a2.25 2.25 0 0 0 2.25-2.25v-6ZM11.25 21v-5.817A5.623 5.623 0 0 1 6.375 18a.75.75 0 0 1 0-1.5 4.126 4.126 0 0 0 4.108-3.75H1.5v6A2.25 2.25 0 0 0 3.75 21h7.5Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.085 10.354c.03.297.038.575.036.805a7.484 7.484 0 0 1-.805-.036c-.833-.084-1.677-.325-2.195-.843a1.5 1.5 0 0 1 2.122-2.12c.517.517.759 1.36.842 2.194ZM12.877 10.354c-.03.297-.038.575-.036.805.23.002.508-.006.805-.036.833-.084 1.677-.325 2.195-.843A1.5 1.5 0 0 0 13.72 8.16c-.518.518-.76 1.362-.843 2.194Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(GiftTopIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "GiftTopIcon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "d", "ForwardRef", "forwardRef"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/@heroicons/react/24/solid/esm/GiftTopIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction GiftTopIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.25 3v4.046a3 3 0 0 0-4.277 4.204H1.5v-6A2.25 2.25 0 0 1 3.75 3h7.5ZM12.75 3v4.011a3 3 0 0 1 4.239 4.239H22.5v-6A2.25 2.25 0 0 0 20.25 3h-7.5ZM22.5 12.75h-8.983a4.125 4.125 0 0 0 4.108 3.75.75.75 0 0 1 0 1.5 5.623 5.623 0 0 1-4.875-2.817V21h7.5a2.25 2.25 0 0 0 2.25-2.25v-6ZM11.25 21v-5.817A5.623 5.623 0 0 1 6.375 18a.75.75 0 0 1 0-1.5 4.126 4.126 0 0 0 4.108-3.75H1.5v6A2.25 2.25 0 0 0 3.75 21h7.5Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M11.085 10.354c.03.297.038.575.036.805a7.484 7.484 0 0 1-.805-.036c-.833-.084-1.677-.325-2.195-.843a1.5 1.5 0 0 1 2.122-2.12c.517.517.759 1.36.842 2.194ZM12.877 10.354c-.03.297-.038.575-.036.805.23.002.508-.006.805-.036.833-.084 1.677-.325 2.195-.843A1.5 1.5 0 0 0 13.72 8.16c-.518.518-.76 1.362-.843 2.194Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(GiftTopIcon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAWA,CAAC;EACnBC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEP,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEV;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDQ,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAad,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IAC3CQ,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcf,KAAK,CAACgB,UAAU,CAACf,WAAW,CAAC;AAC9D,eAAec,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}