{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\contexts\\\\AppContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContext = /*#__PURE__*/createContext();\nexport const useApp = () => {\n  _s();\n  const context = useContext(AppContext);\n  if (!context) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n};\n_s(useApp, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AppProvider = ({\n  children\n}) => {\n  _s2();\n  const [locations, setLocations] = useState([]);\n  const [services, setServices] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [queuePosition, setQueuePosition] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [selectedLocation, setSelectedLocation] = useState(null);\n  const [selectedService, setSelectedService] = useState(null);\n\n  // Fetch locations\n  const fetchLocations = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/locations');\n\n      // Handle different response structures\n      if (response.data && response.data.data && response.data.data.locations) {\n        setLocations(response.data.data.locations);\n      } else if (response.data && Array.isArray(response.data)) {\n        setLocations(response.data);\n      } else {\n        console.log('No locations found or unexpected response structure');\n        setLocations([]);\n      }\n    } catch (error) {\n      console.error('Failed to fetch locations:', error);\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        toast.error('Cannot connect to server. Please check if the backend is running.');\n      } else {\n        toast.error('Failed to load locations');\n      }\n      setLocations([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch services\n  const fetchServices = async (locationId = null) => {\n    try {\n      setLoading(true);\n      const url = locationId ? `/api/services?location_id=${locationId}` : '/api/services';\n      const response = await axios.get(url);\n\n      // Handle different response structures\n      let servicesData = [];\n      if (response.data && response.data.data && response.data.data.services) {\n        servicesData = response.data.data.services;\n      } else if (response.data && Array.isArray(response.data)) {\n        servicesData = response.data;\n      } else if (response.data && response.data.services) {\n        servicesData = response.data.services;\n      } else {\n        console.log('No services found or unexpected response structure');\n        servicesData = [];\n      }\n      setServices(servicesData);\n\n      // Return services for location-specific calls\n      if (locationId) {\n        return servicesData;\n      }\n    } catch (error) {\n      console.error('Failed to fetch services:', error);\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        toast.error('Cannot connect to server. Please check if the backend is running.');\n      } else {\n        toast.error('Failed to load services');\n      }\n      setServices([]);\n      return [];\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch user appointments\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n\n      // Check if user is authenticated\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('No token found, skipping appointments fetch');\n        setAppointments([]);\n        return;\n      }\n      const response = await axios.get('/api/appointments');\n\n      // Handle different response structures\n      if (response.data && response.data.data && response.data.data.appointments) {\n        setAppointments(response.data.data.appointments);\n      } else if (response.data && Array.isArray(response.data)) {\n        setAppointments(response.data);\n      } else {\n        console.log('No appointments found or unexpected response structure');\n        setAppointments([]);\n      }\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('Failed to fetch appointments:', error);\n\n      // Handle specific error cases\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        console.log('Authentication failed - user may need to log in again');\n        setAppointments([]);\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 404) {\n        console.log('Appointments endpoint not found');\n        setAppointments([]);\n      } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        toast.error('Cannot connect to server. Please check if the backend is running.');\n      } else {\n        toast.error('Failed to load appointments');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Book appointment\n  const bookAppointment = async appointmentData => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/appointments', appointmentData);\n\n      // Add new appointment to state\n      setAppointments(prev => [response.data.data.appointment, ...prev]);\n      toast.success('Appointment booked successfully!');\n      return {\n        success: true,\n        appointment: response.data.data.appointment\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to book appointment';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Cancel appointment\n  const cancelAppointment = async appointmentId => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/appointments/${appointmentId}/cancel`);\n\n      // Update appointment status in state\n      setAppointments(prev => prev.map(apt => apt.id === appointmentId ? {\n        ...apt,\n        status: 'cancelled'\n      } : apt));\n      toast.success('Appointment cancelled successfully');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to cancel appointment';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get queue position\n  const getQueuePosition = async () => {\n    try {\n      const response = await axios.get('/api/queues/my-position');\n      setQueuePosition(response.data.data);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to get queue position:', error);\n      return null;\n    }\n  };\n\n  // Join queue\n  const joinQueue = async serviceId => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/queues/join', {\n        service_id: serviceId\n      });\n      setQueuePosition(response.data.data);\n      toast.success('Joined queue successfully!');\n      return {\n        success: true,\n        position: response.data.data\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to join queue';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Leave queue\n  const leaveQueue = async () => {\n    try {\n      setLoading(true);\n      await axios.post('/api/queues/leave');\n      setQueuePosition(null);\n      toast.success('Left queue successfully');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      const message = ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Failed to leave queue';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Check service availability\n  const checkAvailability = async (serviceId, date) => {\n    try {\n      const response = await axios.get(`/api/services/${serviceId}/availability`, {\n        params: {\n          date\n        }\n      });\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to check availability:', error);\n      return null;\n    }\n  };\n\n  // Get dashboard stats\n  const getDashboardStats = async () => {\n    try {\n      const response = await axios.get('/api/reports/dashboard');\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to get dashboard stats:', error);\n      return null;\n    }\n  };\n\n  // ==================== CRUD OPERATIONS ====================\n\n  // Locations CRUD\n  const createLocation = async locationData => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/locations', locationData);\n      await fetchLocations(); // Refresh list\n      toast.success('Location created successfully');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data, _error$response8, _error$response8$data;\n      console.error('Failed to create location:', error);\n      toast.error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Failed to create location');\n      return {\n        success: false,\n        error: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateLocation = async (id, locationData) => {\n    try {\n      setLoading(true);\n      const response = await axios.put(`/api/locations/${id}`, locationData);\n      await fetchLocations(); // Refresh list\n      toast.success('Location updated successfully');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response9, _error$response9$data, _error$response10, _error$response10$dat;\n      console.error('Failed to update location:', error);\n      toast.error(((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.message) || 'Failed to update location');\n      return {\n        success: false,\n        error: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const deleteLocation = async id => {\n    try {\n      setLoading(true);\n      await axios.delete(`/api/locations/${id}`);\n      await fetchLocations(); // Refresh list\n      toast.success('Location deleted successfully');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response11, _error$response11$dat, _error$response12, _error$response12$dat;\n      console.error('Failed to delete location:', error);\n      toast.error(((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.message) || 'Failed to delete location');\n      return {\n        success: false,\n        error: (_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : (_error$response12$dat = _error$response12.data) === null || _error$response12$dat === void 0 ? void 0 : _error$response12$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Services CRUD\n  const createService = async serviceData => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/services', serviceData);\n      await fetchServices(); // Refresh list\n      toast.success('Service created successfully');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response13, _error$response13$dat, _error$response14, _error$response14$dat;\n      console.error('Failed to create service:', error);\n      toast.error(((_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : (_error$response13$dat = _error$response13.data) === null || _error$response13$dat === void 0 ? void 0 : _error$response13$dat.message) || 'Failed to create service');\n      return {\n        success: false,\n        error: (_error$response14 = error.response) === null || _error$response14 === void 0 ? void 0 : (_error$response14$dat = _error$response14.data) === null || _error$response14$dat === void 0 ? void 0 : _error$response14$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const updateService = async (id, serviceData) => {\n    try {\n      setLoading(true);\n      const response = await axios.put(`/api/services/${id}`, serviceData);\n      await fetchServices(); // Refresh list\n      toast.success('Service updated successfully');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response15, _error$response15$dat, _error$response16, _error$response16$dat;\n      console.error('Failed to update service:', error);\n      toast.error(((_error$response15 = error.response) === null || _error$response15 === void 0 ? void 0 : (_error$response15$dat = _error$response15.data) === null || _error$response15$dat === void 0 ? void 0 : _error$response15$dat.message) || 'Failed to update service');\n      return {\n        success: false,\n        error: (_error$response16 = error.response) === null || _error$response16 === void 0 ? void 0 : (_error$response16$dat = _error$response16.data) === null || _error$response16$dat === void 0 ? void 0 : _error$response16$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const deleteService = async id => {\n    try {\n      setLoading(true);\n      await axios.delete(`/api/services/${id}`);\n      await fetchServices(); // Refresh list\n      toast.success('Service deleted successfully');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response17, _error$response17$dat, _error$response18, _error$response18$dat;\n      console.error('Failed to delete service:', error);\n      toast.error(((_error$response17 = error.response) === null || _error$response17 === void 0 ? void 0 : (_error$response17$dat = _error$response17.data) === null || _error$response17$dat === void 0 ? void 0 : _error$response17$dat.message) || 'Failed to delete service');\n      return {\n        success: false,\n        error: (_error$response18 = error.response) === null || _error$response18 === void 0 ? void 0 : (_error$response18$dat = _error$response18.data) === null || _error$response18$dat === void 0 ? void 0 : _error$response18$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Appointments CRUD (update and delete)\n  const updateAppointment = async (id, appointmentData) => {\n    try {\n      setLoading(true);\n      const response = await axios.put(`/api/appointments/${id}`, appointmentData);\n      await fetchAppointments(); // Refresh list\n      toast.success('Appointment updated successfully');\n      return {\n        success: true,\n        data: response.data.data\n      };\n    } catch (error) {\n      var _error$response19, _error$response19$dat, _error$response20, _error$response20$dat;\n      console.error('Failed to update appointment:', error);\n      toast.error(((_error$response19 = error.response) === null || _error$response19 === void 0 ? void 0 : (_error$response19$dat = _error$response19.data) === null || _error$response19$dat === void 0 ? void 0 : _error$response19$dat.message) || 'Failed to update appointment');\n      return {\n        success: false,\n        error: (_error$response20 = error.response) === null || _error$response20 === void 0 ? void 0 : (_error$response20$dat = _error$response20.data) === null || _error$response20$dat === void 0 ? void 0 : _error$response20$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const deleteAppointment = async id => {\n    try {\n      setLoading(true);\n      await axios.delete(`/api/appointments/${id}`);\n      await fetchAppointments(); // Refresh list\n      toast.success('Appointment deleted successfully');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response21, _error$response21$dat, _error$response22, _error$response22$dat;\n      console.error('Failed to delete appointment:', error);\n      toast.error(((_error$response21 = error.response) === null || _error$response21 === void 0 ? void 0 : (_error$response21$dat = _error$response21.data) === null || _error$response21$dat === void 0 ? void 0 : _error$response21$dat.message) || 'Failed to delete appointment');\n      return {\n        success: false,\n        error: (_error$response22 = error.response) === null || _error$response22 === void 0 ? void 0 : (_error$response22$dat = _error$response22.data) === null || _error$response22$dat === void 0 ? void 0 : _error$response22$dat.message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize data on mount\n  useEffect(() => {\n    fetchLocations();\n  }, []);\n  const value = {\n    // State\n    locations,\n    services,\n    appointments,\n    queuePosition,\n    loading,\n    selectedLocation,\n    selectedService,\n    // Setters\n    setSelectedLocation,\n    setSelectedService,\n    // Actions\n    fetchLocations,\n    fetchServices,\n    fetchAppointments,\n    bookAppointment,\n    cancelAppointment,\n    getQueuePosition,\n    joinQueue,\n    leaveQueue,\n    checkAvailability,\n    getDashboardStats,\n    // CRUD operations\n    createLocation,\n    updateLocation,\n    deleteLocation,\n    createService,\n    updateService,\n    deleteService,\n    updateAppointment,\n    deleteAppointment,\n    // Computed values\n    hasActiveAppointments: appointments.some(apt => ['confirmed', 'in_progress'].includes(apt.status)),\n    upcomingAppointments: appointments.filter(apt => apt.status === 'confirmed' && new Date(apt.appointment_date) > new Date()),\n    pastAppointments: appointments.filter(apt => ['completed', 'cancelled'].includes(apt.status))\n  };\n  return /*#__PURE__*/_jsxDEV(AppContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 445,\n    columnNumber: 5\n  }, this);\n};\n_s2(AppProvider, \"l9+OB9mE4b57aAt8gX1P6PQRgG4=\");\n_c = AppProvider;\nvar _c;\n$RefreshReg$(_c, \"AppProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "AppContext", "useApp", "_s", "context", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s2", "locations", "setLocations", "services", "setServices", "appointments", "setAppointments", "queuePosition", "setQueuePosition", "loading", "setLoading", "selectedLocation", "setSelectedLocation", "selectedService", "setSelectedService", "fetchLocations", "response", "get", "data", "Array", "isArray", "console", "log", "error", "code", "fetchServices", "locationId", "url", "servicesData", "fetchAppointments", "token", "localStorage", "getItem", "_error$response", "_error$response2", "status", "bookAppointment", "appointmentData", "post", "prev", "appointment", "success", "_error$response3", "_error$response3$data", "message", "cancelAppointment", "appointmentId", "put", "map", "apt", "id", "_error$response4", "_error$response4$data", "getQueuePosition", "joinQueue", "serviceId", "service_id", "position", "_error$response5", "_error$response5$data", "leaveQueue", "_error$response6", "_error$response6$data", "checkAvailability", "date", "params", "getDashboardStats", "createLocation", "locationData", "_error$response7", "_error$response7$data", "_error$response8", "_error$response8$data", "updateLocation", "_error$response9", "_error$response9$data", "_error$response10", "_error$response10$dat", "deleteLocation", "delete", "_error$response11", "_error$response11$dat", "_error$response12", "_error$response12$dat", "createService", "serviceData", "_error$response13", "_error$response13$dat", "_error$response14", "_error$response14$dat", "updateService", "_error$response15", "_error$response15$dat", "_error$response16", "_error$response16$dat", "deleteService", "_error$response17", "_error$response17$dat", "_error$response18", "_error$response18$dat", "updateAppointment", "_error$response19", "_error$response19$dat", "_error$response20", "_error$response20$dat", "deleteAppointment", "_error$response21", "_error$response21$dat", "_error$response22", "_error$response22$dat", "value", "hasActiveAppointments", "some", "includes", "upcomingAppointments", "filter", "Date", "appointment_date", "pastAppointments", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/contexts/AppContext.js"], "sourcesContent": ["import { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\n\nconst AppContext = createContext();\n\nexport const useApp = () => {\n  const context = useContext(AppContext);\n  if (!context) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n};\n\nexport const AppProvider = ({ children }) => {\n  const [locations, setLocations] = useState([]);\n  const [services, setServices] = useState([]);\n  const [appointments, setAppointments] = useState([]);\n  const [queuePosition, setQueuePosition] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [selectedLocation, setSelectedLocation] = useState(null);\n  const [selectedService, setSelectedService] = useState(null);\n\n  // Fetch locations\n  const fetchLocations = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/locations');\n\n      // Handle different response structures\n      if (response.data && response.data.data && response.data.data.locations) {\n        setLocations(response.data.data.locations);\n      } else if (response.data && Array.isArray(response.data)) {\n        setLocations(response.data);\n      } else {\n        console.log('No locations found or unexpected response structure');\n        setLocations([]);\n      }\n    } catch (error) {\n      console.error('Failed to fetch locations:', error);\n\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        toast.error('Cannot connect to server. Please check if the backend is running.');\n      } else {\n        toast.error('Failed to load locations');\n      }\n      setLocations([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch services\n  const fetchServices = async (locationId = null) => {\n    try {\n      setLoading(true);\n      const url = locationId ? `/api/services?location_id=${locationId}` : '/api/services';\n      const response = await axios.get(url);\n\n      // Handle different response structures\n      let servicesData = [];\n      if (response.data && response.data.data && response.data.data.services) {\n        servicesData = response.data.data.services;\n      } else if (response.data && Array.isArray(response.data)) {\n        servicesData = response.data;\n      } else if (response.data && response.data.services) {\n        servicesData = response.data.services;\n      } else {\n        console.log('No services found or unexpected response structure');\n        servicesData = [];\n      }\n\n      setServices(servicesData);\n\n      // Return services for location-specific calls\n      if (locationId) {\n        return servicesData;\n      }\n    } catch (error) {\n      console.error('Failed to fetch services:', error);\n\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        toast.error('Cannot connect to server. Please check if the backend is running.');\n      } else {\n        toast.error('Failed to load services');\n      }\n      setServices([]);\n      return [];\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch user appointments\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n\n      // Check if user is authenticated\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('No token found, skipping appointments fetch');\n        setAppointments([]);\n        return;\n      }\n\n      const response = await axios.get('/api/appointments');\n\n      // Handle different response structures\n      if (response.data && response.data.data && response.data.data.appointments) {\n        setAppointments(response.data.data.appointments);\n      } else if (response.data && Array.isArray(response.data)) {\n        setAppointments(response.data);\n      } else {\n        console.log('No appointments found or unexpected response structure');\n        setAppointments([]);\n      }\n    } catch (error) {\n      console.error('Failed to fetch appointments:', error);\n\n      // Handle specific error cases\n      if (error.response?.status === 401) {\n        console.log('Authentication failed - user may need to log in again');\n        setAppointments([]);\n      } else if (error.response?.status === 404) {\n        console.log('Appointments endpoint not found');\n        setAppointments([]);\n      } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        toast.error('Cannot connect to server. Please check if the backend is running.');\n      } else {\n        toast.error('Failed to load appointments');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Book appointment\n  const bookAppointment = async (appointmentData) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/appointments', appointmentData);\n\n      // Add new appointment to state\n      setAppointments(prev => [response.data.data.appointment, ...prev]);\n\n      toast.success('Appointment booked successfully!');\n      return { success: true, appointment: response.data.data.appointment };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to book appointment';\n      toast.error(message);\n      return { success: false, error: message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Cancel appointment\n  const cancelAppointment = async (appointmentId) => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/appointments/${appointmentId}/cancel`);\n\n      // Update appointment status in state\n      setAppointments(prev =>\n        prev.map(apt =>\n          apt.id === appointmentId\n            ? { ...apt, status: 'cancelled' }\n            : apt\n        )\n      );\n\n      toast.success('Appointment cancelled successfully');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to cancel appointment';\n      toast.error(message);\n      return { success: false, error: message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get queue position\n  const getQueuePosition = async () => {\n    try {\n      const response = await axios.get('/api/queues/my-position');\n      setQueuePosition(response.data.data);\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to get queue position:', error);\n      return null;\n    }\n  };\n\n  // Join queue\n  const joinQueue = async (serviceId) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/queues/join', {\n        service_id: serviceId\n      });\n\n      setQueuePosition(response.data.data);\n      toast.success('Joined queue successfully!');\n      return { success: true, position: response.data.data };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to join queue';\n      toast.error(message);\n      return { success: false, error: message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Leave queue\n  const leaveQueue = async () => {\n    try {\n      setLoading(true);\n      await axios.post('/api/queues/leave');\n\n      setQueuePosition(null);\n      toast.success('Left queue successfully');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to leave queue';\n      toast.error(message);\n      return { success: false, error: message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Check service availability\n  const checkAvailability = async (serviceId, date) => {\n    try {\n      const response = await axios.get(`/api/services/${serviceId}/availability`, {\n        params: { date }\n      });\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to check availability:', error);\n      return null;\n    }\n  };\n\n  // Get dashboard stats\n  const getDashboardStats = async () => {\n    try {\n      const response = await axios.get('/api/reports/dashboard');\n      return response.data.data;\n    } catch (error) {\n      console.error('Failed to get dashboard stats:', error);\n      return null;\n    }\n  };\n\n  // ==================== CRUD OPERATIONS ====================\n\n  // Locations CRUD\n  const createLocation = async (locationData) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/locations', locationData);\n      await fetchLocations(); // Refresh list\n      toast.success('Location created successfully');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      console.error('Failed to create location:', error);\n      toast.error(error.response?.data?.message || 'Failed to create location');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateLocation = async (id, locationData) => {\n    try {\n      setLoading(true);\n      const response = await axios.put(`/api/locations/${id}`, locationData);\n      await fetchLocations(); // Refresh list\n      toast.success('Location updated successfully');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      console.error('Failed to update location:', error);\n      toast.error(error.response?.data?.message || 'Failed to update location');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const deleteLocation = async (id) => {\n    try {\n      setLoading(true);\n      await axios.delete(`/api/locations/${id}`);\n      await fetchLocations(); // Refresh list\n      toast.success('Location deleted successfully');\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to delete location:', error);\n      toast.error(error.response?.data?.message || 'Failed to delete location');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Services CRUD\n  const createService = async (serviceData) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/services', serviceData);\n      await fetchServices(); // Refresh list\n      toast.success('Service created successfully');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      console.error('Failed to create service:', error);\n      toast.error(error.response?.data?.message || 'Failed to create service');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateService = async (id, serviceData) => {\n    try {\n      setLoading(true);\n      const response = await axios.put(`/api/services/${id}`, serviceData);\n      await fetchServices(); // Refresh list\n      toast.success('Service updated successfully');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      console.error('Failed to update service:', error);\n      toast.error(error.response?.data?.message || 'Failed to update service');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const deleteService = async (id) => {\n    try {\n      setLoading(true);\n      await axios.delete(`/api/services/${id}`);\n      await fetchServices(); // Refresh list\n      toast.success('Service deleted successfully');\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to delete service:', error);\n      toast.error(error.response?.data?.message || 'Failed to delete service');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Appointments CRUD (update and delete)\n  const updateAppointment = async (id, appointmentData) => {\n    try {\n      setLoading(true);\n      const response = await axios.put(`/api/appointments/${id}`, appointmentData);\n      await fetchAppointments(); // Refresh list\n      toast.success('Appointment updated successfully');\n      return { success: true, data: response.data.data };\n    } catch (error) {\n      console.error('Failed to update appointment:', error);\n      toast.error(error.response?.data?.message || 'Failed to update appointment');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const deleteAppointment = async (id) => {\n    try {\n      setLoading(true);\n      await axios.delete(`/api/appointments/${id}`);\n      await fetchAppointments(); // Refresh list\n      toast.success('Appointment deleted successfully');\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to delete appointment:', error);\n      toast.error(error.response?.data?.message || 'Failed to delete appointment');\n      return { success: false, error: error.response?.data?.message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize data on mount\n  useEffect(() => {\n    fetchLocations();\n  }, []);\n\n  const value = {\n    // State\n    locations,\n    services,\n    appointments,\n    queuePosition,\n    loading,\n    selectedLocation,\n    selectedService,\n\n    // Setters\n    setSelectedLocation,\n    setSelectedService,\n\n    // Actions\n    fetchLocations,\n    fetchServices,\n    fetchAppointments,\n    bookAppointment,\n    cancelAppointment,\n    getQueuePosition,\n    joinQueue,\n    leaveQueue,\n    checkAvailability,\n    getDashboardStats,\n\n    // CRUD operations\n    createLocation,\n    updateLocation,\n    deleteLocation,\n    createService,\n    updateService,\n    deleteService,\n    updateAppointment,\n    deleteAppointment,\n\n    // Computed values\n    hasActiveAppointments: appointments.some(apt =>\n      ['confirmed', 'in_progress'].includes(apt.status)\n    ),\n    upcomingAppointments: appointments.filter(apt =>\n      apt.status === 'confirmed' && new Date(apt.appointment_date) > new Date()\n    ),\n    pastAppointments: appointments.filter(apt =>\n      ['completed', 'cancelled'].includes(apt.status)\n    )\n  };\n\n  return (\n    <AppContext.Provider value={value}>\n      {children}\n    </AppContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AACtE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,UAAU,gBAAGR,aAAa,CAAC,CAAC;AAElC,OAAO,MAAMS,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,OAAO,GAAGV,UAAU,CAACO,UAAU,CAAC;EACtC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,MAAM;AAQnB,OAAO,MAAMI,WAAW,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC3C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM4B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,gBAAgB,CAAC;;MAElD;MACA,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,SAAS,EAAE;QACvEC,YAAY,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACjB,SAAS,CAAC;MAC5C,CAAC,MAAM,IAAIe,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;QACxDhB,YAAY,CAACc,QAAQ,CAACE,IAAI,CAAC;MAC7B,CAAC,MAAM;QACLG,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QAClEpB,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAElD,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,IAAID,KAAK,CAACC,IAAI,KAAK,aAAa,EAAE;QACjElC,KAAK,CAACiC,KAAK,CAAC,mEAAmE,CAAC;MAClF,CAAC,MAAM;QACLjC,KAAK,CAACiC,KAAK,CAAC,0BAA0B,CAAC;MACzC;MACArB,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRQ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAG,MAAAA,CAAOC,UAAU,GAAG,IAAI,KAAK;IACjD,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,GAAG,GAAGD,UAAU,GAAG,6BAA6BA,UAAU,EAAE,GAAG,eAAe;MACpF,MAAMV,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAACU,GAAG,CAAC;;MAErC;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACf,QAAQ,EAAE;QACtEyB,YAAY,GAAGZ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACf,QAAQ;MAC5C,CAAC,MAAM,IAAIa,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;QACxDU,YAAY,GAAGZ,QAAQ,CAACE,IAAI;MAC9B,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACf,QAAQ,EAAE;QAClDyB,YAAY,GAAGZ,QAAQ,CAACE,IAAI,CAACf,QAAQ;MACvC,CAAC,MAAM;QACLkB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjEM,YAAY,GAAG,EAAE;MACnB;MAEAxB,WAAW,CAACwB,YAAY,CAAC;;MAEzB;MACA,IAAIF,UAAU,EAAE;QACd,OAAOE,YAAY;MACrB;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAEjD,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,IAAID,KAAK,CAACC,IAAI,KAAK,aAAa,EAAE;QACjElC,KAAK,CAACiC,KAAK,CAAC,mEAAmE,CAAC;MAClF,CAAC,MAAM;QACLjC,KAAK,CAACiC,KAAK,CAAC,yBAAyB,CAAC;MACxC;MACAnB,WAAW,CAAC,EAAE,CAAC;MACf,OAAO,EAAE;IACX,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMoB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVT,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DhB,eAAe,CAAC,EAAE,CAAC;QACnB;MACF;MAEA,MAAMU,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,mBAAmB,CAAC;;MAErD;MACA,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACb,YAAY,EAAE;QAC1EC,eAAe,CAACU,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACb,YAAY,CAAC;MAClD,CAAC,MAAM,IAAIW,QAAQ,CAACE,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACE,IAAI,CAAC,EAAE;QACxDZ,eAAe,CAACU,QAAQ,CAACE,IAAI,CAAC;MAChC,CAAC,MAAM;QACLG,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrEhB,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MAAA,IAAAU,eAAA,EAAAC,gBAAA;MACdb,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAI,EAAAU,eAAA,GAAAV,KAAK,CAACP,QAAQ,cAAAiB,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,EAAE;QAClCd,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpEhB,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,MAAM,IAAI,EAAA4B,gBAAA,GAAAX,KAAK,CAACP,QAAQ,cAAAkB,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QACzCd,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9ChB,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,MAAM,IAAIiB,KAAK,CAACC,IAAI,KAAK,cAAc,IAAID,KAAK,CAACC,IAAI,KAAK,aAAa,EAAE;QACxElC,KAAK,CAACiC,KAAK,CAAC,mEAAmE,CAAC;MAClF,CAAC,MAAM;QACLjC,KAAK,CAACiC,KAAK,CAAC,6BAA6B,CAAC;MAC5C;IACF,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,eAAe,GAAG,MAAOC,eAAe,IAAK;IACjD,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACiD,IAAI,CAAC,mBAAmB,EAAED,eAAe,CAAC;;MAEvE;MACA/B,eAAe,CAACiC,IAAI,IAAI,CAACvB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACsB,WAAW,EAAE,GAAGD,IAAI,CAAC,CAAC;MAElEjD,KAAK,CAACmD,OAAO,CAAC,kCAAkC,CAAC;MACjD,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAED,WAAW,EAAExB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACsB;MAAY,CAAC;IACvE,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,gBAAA,GAAAnB,KAAK,CAACP,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBC,OAAO,KAAI,4BAA4B;MAC7EtD,KAAK,CAACiC,KAAK,CAACqB,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,EAAEqB;MAAQ,CAAC;IAC3C,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAG,MAAOC,aAAa,IAAK;IACjD,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,KAAK,CAAC0D,GAAG,CAAC,qBAAqBD,aAAa,SAAS,CAAC;;MAE5D;MACAxC,eAAe,CAACiC,IAAI,IAClBA,IAAI,CAACS,GAAG,CAACC,GAAG,IACVA,GAAG,CAACC,EAAE,KAAKJ,aAAa,GACpB;QAAE,GAAGG,GAAG;QAAEd,MAAM,EAAE;MAAY,CAAC,GAC/Bc,GACN,CACF,CAAC;MAED3D,KAAK,CAACmD,OAAO,CAAC,oCAAoC,CAAC;MACnD,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACd,MAAMR,OAAO,GAAG,EAAAO,gBAAA,GAAA5B,KAAK,CAACP,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjC,IAAI,cAAAkC,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,8BAA8B;MAC/EtD,KAAK,CAACiC,KAAK,CAACqB,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,EAAEqB;MAAQ,CAAC;IAC3C,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,yBAAyB,CAAC;MAC3DT,gBAAgB,CAACQ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACpC,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAM+B,SAAS,GAAG,MAAOC,SAAS,IAAK;IACrC,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACiD,IAAI,CAAC,kBAAkB,EAAE;QACpDkB,UAAU,EAAED;MACd,CAAC,CAAC;MAEF/C,gBAAgB,CAACQ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACpC5B,KAAK,CAACmD,OAAO,CAAC,4BAA4B,CAAC;MAC3C,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEgB,QAAQ,EAAEzC,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACxD,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACd,MAAMf,OAAO,GAAG,EAAAc,gBAAA,GAAAnC,KAAK,CAACP,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAI,sBAAsB;MACvEtD,KAAK,CAACiC,KAAK,CAACqB,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,EAAEqB;MAAQ,CAAC;IAC3C,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,KAAK,CAACiD,IAAI,CAAC,mBAAmB,CAAC;MAErC9B,gBAAgB,CAAC,IAAI,CAAC;MACtBlB,KAAK,CAACmD,OAAO,CAAC,yBAAyB,CAAC;MACxC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACd,MAAMlB,OAAO,GAAG,EAAAiB,gBAAA,GAAAtC,KAAK,CAACP,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBlB,OAAO,KAAI,uBAAuB;MACxEtD,KAAK,CAACiC,KAAK,CAACqB,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,EAAEqB;MAAQ,CAAC;IAC3C,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqD,iBAAiB,GAAG,MAAAA,CAAOR,SAAS,EAAES,IAAI,KAAK;IACnD,IAAI;MACF,MAAMhD,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,iBAAiBsC,SAAS,eAAe,EAAE;QAC1EU,MAAM,EAAE;UAAED;QAAK;MACjB,CAAC,CAAC;MACF,OAAOhD,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAM2C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMlD,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,wBAAwB,CAAC;MAC1D,OAAOD,QAAQ,CAACE,IAAI,CAACA,IAAI;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,IAAI;IACb;EACF,CAAC;;EAED;;EAEA;EACA,MAAM4C,cAAc,GAAG,MAAOC,YAAY,IAAK;IAC7C,IAAI;MACF1D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACiD,IAAI,CAAC,gBAAgB,EAAE8B,YAAY,CAAC;MACjE,MAAMrD,cAAc,CAAC,CAAC,CAAC,CAAC;MACxBzB,KAAK,CAACmD,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAA8C,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdnD,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDjC,KAAK,CAACiC,KAAK,CAAC,EAAA8C,gBAAA,GAAA9C,KAAK,CAACP,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsB1B,OAAO,KAAI,2BAA2B,CAAC;MACzE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAAgD,gBAAA,GAAEhD,KAAK,CAACP,QAAQ,cAAAuD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrD,IAAI,cAAAsD,qBAAA,uBAApBA,qBAAA,CAAsB5B;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,cAAc,GAAG,MAAAA,CAAOvB,EAAE,EAAEkB,YAAY,KAAK;IACjD,IAAI;MACF1D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAAC0D,GAAG,CAAC,kBAAkBG,EAAE,EAAE,EAAEkB,YAAY,CAAC;MACtE,MAAMrD,cAAc,CAAC,CAAC,CAAC,CAAC;MACxBzB,KAAK,CAACmD,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACdxD,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDjC,KAAK,CAACiC,KAAK,CAAC,EAAAmD,gBAAA,GAAAnD,KAAK,CAACP,QAAQ,cAAA0D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxD,IAAI,cAAAyD,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAI,2BAA2B,CAAC;MACzE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAAqD,iBAAA,GAAErD,KAAK,CAACP,QAAQ,cAAA4D,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB1D,IAAI,cAAA2D,qBAAA,uBAApBA,qBAAA,CAAsBjC;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoE,cAAc,GAAG,MAAO5B,EAAE,IAAK;IACnC,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,KAAK,CAAC0F,MAAM,CAAC,kBAAkB7B,EAAE,EAAE,CAAC;MAC1C,MAAMnC,cAAc,CAAC,CAAC,CAAC,CAAC;MACxBzB,KAAK,CAACmD,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAyD,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACd9D,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDjC,KAAK,CAACiC,KAAK,CAAC,EAAAyD,iBAAA,GAAAzD,KAAK,CAACP,QAAQ,cAAAgE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB9D,IAAI,cAAA+D,qBAAA,uBAApBA,qBAAA,CAAsBrC,OAAO,KAAI,2BAA2B,CAAC;MACzE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAA2D,iBAAA,GAAE3D,KAAK,CAACP,QAAQ,cAAAkE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsBvC;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0E,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF3E,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACiD,IAAI,CAAC,eAAe,EAAE+C,WAAW,CAAC;MAC/D,MAAM5D,aAAa,CAAC,CAAC,CAAC,CAAC;MACvBnC,KAAK,CAACmD,OAAO,CAAC,8BAA8B,CAAC;MAC7C,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAA+D,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACdpE,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDjC,KAAK,CAACiC,KAAK,CAAC,EAAA+D,iBAAA,GAAA/D,KAAK,CAACP,QAAQ,cAAAsE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBpE,IAAI,cAAAqE,qBAAA,uBAApBA,qBAAA,CAAsB3C,OAAO,KAAI,0BAA0B,CAAC;MACxE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAAiE,iBAAA,GAAEjE,KAAK,CAACP,QAAQ,cAAAwE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBtE,IAAI,cAAAuE,qBAAA,uBAApBA,qBAAA,CAAsB7C;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgF,aAAa,GAAG,MAAAA,CAAOxC,EAAE,EAAEmC,WAAW,KAAK;IAC/C,IAAI;MACF3E,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAAC0D,GAAG,CAAC,iBAAiBG,EAAE,EAAE,EAAEmC,WAAW,CAAC;MACpE,MAAM5D,aAAa,CAAC,CAAC,CAAC,CAAC;MACvBnC,KAAK,CAACmD,OAAO,CAAC,8BAA8B,CAAC;MAC7C,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAAoE,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACdzE,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDjC,KAAK,CAACiC,KAAK,CAAC,EAAAoE,iBAAA,GAAApE,KAAK,CAACP,QAAQ,cAAA2E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzE,IAAI,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBhD,OAAO,KAAI,0BAA0B,CAAC;MACxE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAAsE,iBAAA,GAAEtE,KAAK,CAACP,QAAQ,cAAA6E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB3E,IAAI,cAAA4E,qBAAA,uBAApBA,qBAAA,CAAsBlD;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqF,aAAa,GAAG,MAAO7C,EAAE,IAAK;IAClC,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,KAAK,CAAC0F,MAAM,CAAC,iBAAiB7B,EAAE,EAAE,CAAC;MACzC,MAAMzB,aAAa,CAAC,CAAC,CAAC,CAAC;MACvBnC,KAAK,CAACmD,OAAO,CAAC,8BAA8B,CAAC;MAC7C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAyE,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACd9E,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDjC,KAAK,CAACiC,KAAK,CAAC,EAAAyE,iBAAA,GAAAzE,KAAK,CAACP,QAAQ,cAAAgF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB9E,IAAI,cAAA+E,qBAAA,uBAApBA,qBAAA,CAAsBrD,OAAO,KAAI,0BAA0B,CAAC;MACxE,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAA2E,iBAAA,GAAE3E,KAAK,CAACP,QAAQ,cAAAkF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBhF,IAAI,cAAAiF,qBAAA,uBAApBA,qBAAA,CAAsBvD;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0F,iBAAiB,GAAG,MAAAA,CAAOlD,EAAE,EAAEb,eAAe,KAAK;IACvD,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAAC0D,GAAG,CAAC,qBAAqBG,EAAE,EAAE,EAAEb,eAAe,CAAC;MAC5E,MAAMR,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC3BvC,KAAK,CAACmD,OAAO,CAAC,kCAAkC,CAAC;MACjD,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACA;MAAK,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAA,IAAA8E,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACdnF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDjC,KAAK,CAACiC,KAAK,CAAC,EAAA8E,iBAAA,GAAA9E,KAAK,CAACP,QAAQ,cAAAqF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBnF,IAAI,cAAAoF,qBAAA,uBAApBA,qBAAA,CAAsB1D,OAAO,KAAI,8BAA8B,CAAC;MAC5E,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAAgF,iBAAA,GAAEhF,KAAK,CAACP,QAAQ,cAAAuF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBrF,IAAI,cAAAsF,qBAAA,uBAApBA,qBAAA,CAAsB5D;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+F,iBAAiB,GAAG,MAAOvD,EAAE,IAAK;IACtC,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,KAAK,CAAC0F,MAAM,CAAC,qBAAqB7B,EAAE,EAAE,CAAC;MAC7C,MAAMrB,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC3BvC,KAAK,CAACmD,OAAO,CAAC,kCAAkC,CAAC;MACjD,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAmF,iBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;MACdxF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDjC,KAAK,CAACiC,KAAK,CAAC,EAAAmF,iBAAA,GAAAnF,KAAK,CAACP,QAAQ,cAAA0F,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBxF,IAAI,cAAAyF,qBAAA,uBAApBA,qBAAA,CAAsB/D,OAAO,KAAI,8BAA8B,CAAC;MAC5E,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAElB,KAAK,GAAAqF,iBAAA,GAAErF,KAAK,CAACP,QAAQ,cAAA4F,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB1F,IAAI,cAAA2F,qBAAA,uBAApBA,qBAAA,CAAsBjE;MAAQ,CAAC;IACjE,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAtB,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM+F,KAAK,GAAG;IACZ;IACA7G,SAAS;IACTE,QAAQ;IACRE,YAAY;IACZE,aAAa;IACbE,OAAO;IACPE,gBAAgB;IAChBE,eAAe;IAEf;IACAD,mBAAmB;IACnBE,kBAAkB;IAElB;IACAC,cAAc;IACdU,aAAa;IACbI,iBAAiB;IACjBO,eAAe;IACfS,iBAAiB;IACjBQ,gBAAgB;IAChBC,SAAS;IACTM,UAAU;IACVG,iBAAiB;IACjBG,iBAAiB;IAEjB;IACAC,cAAc;IACdM,cAAc;IACdK,cAAc;IACdM,aAAa;IACbM,aAAa;IACbK,aAAa;IACbK,iBAAiB;IACjBK,iBAAiB;IAEjB;IACAM,qBAAqB,EAAE1G,YAAY,CAAC2G,IAAI,CAAC/D,GAAG,IAC1C,CAAC,WAAW,EAAE,aAAa,CAAC,CAACgE,QAAQ,CAAChE,GAAG,CAACd,MAAM,CAClD,CAAC;IACD+E,oBAAoB,EAAE7G,YAAY,CAAC8G,MAAM,CAAClE,GAAG,IAC3CA,GAAG,CAACd,MAAM,KAAK,WAAW,IAAI,IAAIiF,IAAI,CAACnE,GAAG,CAACoE,gBAAgB,CAAC,GAAG,IAAID,IAAI,CAAC,CAC1E,CAAC;IACDE,gBAAgB,EAAEjH,YAAY,CAAC8G,MAAM,CAAClE,GAAG,IACvC,CAAC,WAAW,EAAE,WAAW,CAAC,CAACgE,QAAQ,CAAChE,GAAG,CAACd,MAAM,CAChD;EACF,CAAC;EAED,oBACE3C,OAAA,CAACC,UAAU,CAAC8H,QAAQ;IAACT,KAAK,EAAEA,KAAM;IAAA/G,QAAA,EAC/BA;EAAQ;IAAAyH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B,CAAC;AAAC3H,GAAA,CAlbWF,WAAW;AAAA8H,EAAA,GAAX9H,WAAW;AAAA,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}