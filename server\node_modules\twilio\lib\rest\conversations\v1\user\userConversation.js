"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserConversationPage = exports.UserConversationListInstance = exports.UserConversationInstance = exports.UserConversationContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class UserConversationContextImpl {
    constructor(_version, userSid, conversationSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(userSid)) {
            throw new Error("Parameter 'userSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(conversationSid)) {
            throw new Error("Parameter 'conversationSid' is not valid.");
        }
        this._solution = { userSid, conversationSid };
        this._uri = `/Users/<USER>/Conversations/${conversationSid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new UserConversationInstance(operationVersion, payload, instance._solution.userSid, instance._solution.conversationSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["notificationLevel"] !== undefined)
            data["NotificationLevel"] = params["notificationLevel"];
        if (params["lastReadTimestamp"] !== undefined)
            data["LastReadTimestamp"] = serialize.iso8601DateTime(params["lastReadTimestamp"]);
        if (params["lastReadMessageIndex"] !== undefined)
            data["LastReadMessageIndex"] = params["lastReadMessageIndex"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserConversationInstance(operationVersion, payload, instance._solution.userSid, instance._solution.conversationSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserConversationContextImpl = UserConversationContextImpl;
class UserConversationInstance {
    constructor(_version, payload, userSid, conversationSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.chatServiceSid = payload.chat_service_sid;
        this.conversationSid = payload.conversation_sid;
        this.unreadMessagesCount = deserialize.integer(payload.unread_messages_count);
        this.lastReadMessageIndex = deserialize.integer(payload.last_read_message_index);
        this.participantSid = payload.participant_sid;
        this.userSid = payload.user_sid;
        this.friendlyName = payload.friendly_name;
        this.conversationState = payload.conversation_state;
        this.timers = payload.timers;
        this.attributes = payload.attributes;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.createdBy = payload.created_by;
        this.notificationLevel = payload.notification_level;
        this.uniqueName = payload.unique_name;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = {
            userSid,
            conversationSid: conversationSid || this.conversationSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new UserConversationContextImpl(this._version, this._solution.userSid, this._solution.conversationSid);
        return this._context;
    }
    /**
     * Remove a UserConversationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a UserConversationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed UserConversationInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            chatServiceSid: this.chatServiceSid,
            conversationSid: this.conversationSid,
            unreadMessagesCount: this.unreadMessagesCount,
            lastReadMessageIndex: this.lastReadMessageIndex,
            participantSid: this.participantSid,
            userSid: this.userSid,
            friendlyName: this.friendlyName,
            conversationState: this.conversationState,
            timers: this.timers,
            attributes: this.attributes,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            createdBy: this.createdBy,
            notificationLevel: this.notificationLevel,
            uniqueName: this.uniqueName,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserConversationInstance = UserConversationInstance;
function UserConversationListInstance(version, userSid) {
    if (!(0, utility_1.isValidPathParam)(userSid)) {
        throw new Error("Parameter 'userSid' is not valid.");
    }
    const instance = ((conversationSid) => instance.get(conversationSid));
    instance.get = function get(conversationSid) {
        return new UserConversationContextImpl(version, userSid, conversationSid);
    };
    instance._version = version;
    instance._solution = { userSid };
    instance._uri = `/Users/<USER>/Conversations`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserConversationPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new UserConversationPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UserConversationListInstance = UserConversationListInstance;
class UserConversationPage extends Page_1.default {
    /**
     * Initialize the UserConversationPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of UserConversationInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new UserConversationInstance(this._version, payload, this._solution.userSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserConversationPage = UserConversationPage;
