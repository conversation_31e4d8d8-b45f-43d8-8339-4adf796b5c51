const sequelize = require('../config/database');

// Import all models
const User = require('./User');
const Location = require('./Location');
const Service = require('./Service');
const Appointment = require('./Appointment');
const Queue = require('./Queue');
const Payment = require('./Payment');

// Define associations

// User associations
User.hasMany(Appointment, { foreignKey: 'user_id', as: 'appointments' });
User.hasMany(Queue, { foreignKey: 'user_id', as: 'queues' });
User.hasMany(Payment, { foreignKey: 'user_id', as: 'payments' });
User.hasMany(Location, { foreignKey: 'admin_user_id', as: 'managed_locations' });

// Location associations
Location.belongsTo(User, { foreignKey: 'admin_user_id', as: 'admin' });
Location.hasMany(Service, { foreignKey: 'location_id', as: 'services' });
Location.hasMany(Appointment, { foreignKey: 'location_id', as: 'appointments' });
Location.hasMany(Queue, { foreignKey: 'location_id', as: 'queues' });

// Service associations
Service.belongsTo(Location, { foreignKey: 'location_id', as: 'location' });
Service.hasMany(Appointment, { foreignKey: 'service_id', as: 'appointments' });
Service.hasMany(Queue, { foreignKey: 'service_id', as: 'queues' });

// Appointment associations
Appointment.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Appointment.belongsTo(Service, { foreignKey: 'service_id', as: 'service' });
Appointment.belongsTo(Location, { foreignKey: 'location_id', as: 'location' });
Appointment.belongsTo(User, { foreignKey: 'cancelled_by', as: 'cancelled_by_user' });
Appointment.belongsTo(Appointment, { foreignKey: 'rescheduled_from', as: 'original_appointment' });
Appointment.hasOne(Queue, { foreignKey: 'appointment_id', as: 'queue' });
Appointment.hasMany(Payment, { foreignKey: 'appointment_id', as: 'payments' });

// Queue associations
Queue.belongsTo(Appointment, { foreignKey: 'appointment_id', as: 'appointment' });
Queue.belongsTo(Location, { foreignKey: 'location_id', as: 'location' });
Queue.belongsTo(Service, { foreignKey: 'service_id', as: 'service' });
Queue.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Queue.belongsTo(User, { foreignKey: 'served_by', as: 'server' });

// Payment associations
Payment.belongsTo(Appointment, { foreignKey: 'appointment_id', as: 'appointment' });
Payment.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// Export models and sequelize instance
module.exports = {
  sequelize,
  User,
  Location,
  Service,
  Appointment,
  Queue,
  Payment
};
