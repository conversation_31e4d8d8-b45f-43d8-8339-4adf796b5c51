"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Microvisor
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const accountConfig_1 = require("./v1/accountConfig");
const accountSecret_1 = require("./v1/accountSecret");
const app_1 = require("./v1/app");
const device_1 = require("./v1/device");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Microvisor
     *
     * @param domain - The Twilio (Twilio.Microvisor) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for accountConfigs resource */
    get accountConfigs() {
        this._accountConfigs =
            this._accountConfigs || (0, accountConfig_1.AccountConfigListInstance)(this);
        return this._accountConfigs;
    }
    /** Getter for accountSecrets resource */
    get accountSecrets() {
        this._accountSecrets =
            this._accountSecrets || (0, accountSecret_1.AccountSecretListInstance)(this);
        return this._accountSecrets;
    }
    /** Getter for apps resource */
    get apps() {
        this._apps = this._apps || (0, app_1.AppListInstance)(this);
        return this._apps;
    }
    /** Getter for devices resource */
    get devices() {
        this._devices = this._devices || (0, device_1.DeviceListInstance)(this);
        return this._devices;
    }
}
exports.default = V1;
