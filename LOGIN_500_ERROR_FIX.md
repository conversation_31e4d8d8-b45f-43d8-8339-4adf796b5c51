# 🔧 LOGIN 500 ERROR - COMPLETE FIX

## 🎯 **The Issue**
You're getting a "Server error (500)" when trying to login. This is likely because:
1. **Database not seeded** - No test users exist
2. **Database connection issue** - SQLite database not accessible
3. **Missing environment variables** - JWT secret not set

## ✅ **STEP-BY-STEP FIX**

### **Step 1: Stop All Servers**
```bash
# Press Ctrl+C in all terminals to stop servers
```

### **Step 2: Check Environment Variables**
Make sure `server/.env` has these values:
```env
# Database
DATABASE_URL=sqlite:./umurongo_dev.db

# JWT Configuration
JWT_SECRET=umurongo_super_secret_jwt_key_2024_development
JWT_EXPIRES_IN=7d

# Server Configuration
PORT=5002
NODE_ENV=development
```

### **Step 3: Create Sample Users Manually**
Create this file: `server/create-users.js`
```javascript
require('dotenv').config();
const { User } = require('./models');
const { sequelize } = require('./models');

async function createUsers() {
  try {
    await sequelize.sync({ force: false });
    console.log('Database connected');

    // Create customer
    const customer = await User.findOrCreate({
      where: { phone: '+250788000003' },
      defaults: {
        name: 'Alice Uwimana',
        phone: '+250788000003',
        email: '<EMAIL>',
        password: 'customer123',
        role: 'customer',
        is_verified: true
      }
    });

    console.log('Customer created:', customer[0].name);
    console.log('Test credentials: +250788000003 / customer123');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

createUsers();
```

### **Step 4: Run User Creation**
```bash
cd server
node create-users.js
```

### **Step 5: Start Backend**
```bash
cd server
node server.js
```

**Expected Output:**
```
✅ Database connection established successfully.
✅ Database tables ready.
🚀 Umurongo API server running on port 5002
```

### **Step 6: Test Login API Directly**
Open PowerShell and test:
```powershell
$headers = @{ "Content-Type" = "application/json" }
$body = @{
    phone = "+250788000003"
    password = "customer123"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/auth/login" -Method POST -Body $body -Headers $headers
    Write-Host "✅ Login Success!" -ForegroundColor Green
    Write-Host "User: $($response.data.user.name)"
} catch {
    Write-Host "❌ Login Failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
```

### **Step 7: Start Frontend and Test**
```bash
cd client
npm start
```

Then try login with: `+250788000003` / `customer123`

## 🐛 **Debugging the 500 Error**

### **Check Backend Logs**
When you try to login, check the backend terminal for detailed logs:
```
=== LOGIN ATTEMPT ===
Phone: +250788000003
Password provided: true
Searching for user with phone: +250788000003
```

### **Common 500 Error Causes:**

#### **1. Database Connection Error**
**Symptoms:** Backend shows database connection errors
**Fix:**
```bash
cd server
rm umurongo_dev.db  # Delete corrupted database
node server.js      # Restart to recreate
```

#### **2. Missing JWT Secret**
**Symptoms:** "JWT_SECRET is not defined" error
**Fix:** Add to `server/.env`:
```env
JWT_SECRET=umurongo_super_secret_jwt_key_2024_development
```

#### **3. No Users in Database**
**Symptoms:** "User not found" in logs
**Fix:** Run the user creation script above

#### **4. Password Hashing Error**
**Symptoms:** Error during password comparison
**Fix:** Check bcrypt installation:
```bash
cd server
npm install bcryptjs
```

#### **5. Validation Error**
**Symptoms:** Phone number validation fails
**Fix:** Use exact format: `+250788000003`

## 🔍 **Manual Testing Steps**

### **Test 1: Health Check**
```bash
curl http://localhost:5002/health
```
**Expected:** `{"status":"OK","message":"Umurongo API is running"}`

### **Test 2: Database Check**
Create `server/test-db.js`:
```javascript
require('dotenv').config();
const { User } = require('./models');

async function testDB() {
  try {
    const users = await User.findAll();
    console.log('Users in database:', users.length);
    users.forEach(user => {
      console.log(`- ${user.name} (${user.phone})`);
    });
  } catch (error) {
    console.error('Database error:', error.message);
  }
}

testDB();
```

Run: `node test-db.js`

### **Test 3: Login API**
```bash
curl -X POST http://localhost:5002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+250788000003","password":"customer123"}'
```

## 🚨 **Emergency Fix - Simple Backend**

If the main backend still fails, create `server/simple-backend.js`:
```javascript
const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
app.use(cors());
app.use(express.json());

// Test user with pre-hashed password
const testUser = {
  id: 1,
  name: 'Alice Uwimana',
  phone: '+250788000003',
  password: '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', // customer123
  role: 'customer',
  is_verified: true
};

app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Simple backend running' });
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { phone, password } = req.body;
    
    if (phone !== '+250788000003') {
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    const isValid = await bcrypt.compare(password, testUser.password);
    if (!isValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    const token = jwt.sign({ userId: testUser.id }, 'secret', { expiresIn: '7d' });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: testUser.id,
          name: testUser.name,
          phone: testUser.phone,
          role: testUser.role,
          is_verified: testUser.is_verified
        },
        token
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

app.listen(5002, () => {
  console.log('🚀 Simple backend running on port 5002');
  console.log('Test credentials: +250788000003 / customer123');
});
```

Run: `node simple-backend.js`

## ✅ **Success Checklist**

- [ ] Backend starts without errors
- [ ] Health check returns 200 OK
- [ ] Database has test users
- [ ] Login API returns 200 with token
- [ ] Frontend connects without CORS errors
- [ ] Login form works with test credentials

## 📞 **If Still Not Working**

1. **Check backend terminal** for detailed error logs
2. **Check browser console** (F12) for frontend errors
3. **Try the simple backend** as emergency fallback
4. **Verify credentials exactly**: `+250788000003` / `customer123`

The enhanced logging will show exactly where the 500 error is occurring! 🔍
