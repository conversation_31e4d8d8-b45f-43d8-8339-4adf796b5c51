const express = require('express');
const { Op, sequelize } = require('sequelize');
const { Service, Location, Appointment } = require('../models');
const { authenticateToken, requireLocationAdmin, optionalAuth } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

const router = express.Router();

// Get all services (public with optional auth)
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      location_id,
      category,
      search,
      is_active = true
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Only show active services to non-admin users
    if (req.user?.role !== 'system_admin') {
      whereClause.is_active = true;
    } else if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true';
    }

    if (location_id) {
      whereClause.location_id = location_id;
    }

    if (category) {
      whereClause.category = category;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: services } = await Service.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name', 'address', 'district', 'type']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: {
        services,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch services'
    });
  }
});

// Get service by ID (public)
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const whereClause = { id };

    // Only show active services to non-admin users
    if (req.user?.role !== 'system_admin') {
      whereClause.is_active = true;
    }

    const service = await Service.findOne({
      where: whereClause,
      include: [
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name', 'address', 'district', 'type', 'operating_hours', 'contact_phone']
        }
      ]
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    res.json({
      success: true,
      data: { service }
    });
  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service'
    });
  }
});

// Create new service (location admin)
router.post('/',
  authenticateToken,
  validate(schemas.createService),
  async (req, res) => {
    try {
      const { location_id } = req.body;

      // Check if user can manage this location
      const location = await Location.findOne({
        where: {
          id: location_id,
          [Op.or]: [
            { admin_user_id: req.user.id },
            ...(req.user.role === 'system_admin' ? [{}] : [])
          ]
        }
      });

      if (!location) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to add services to this location'
        });
      }

      const service = await Service.create(req.body);

      // Include location details in response
      const serviceWithLocation = await Service.findByPk(service.id, {
        include: [
          {
            model: Location,
            as: 'location',
            attributes: ['id', 'name', 'address', 'district', 'type']
          }
        ]
      });

      res.status(201).json({
        success: true,
        message: 'Service created successfully',
        data: { service: serviceWithLocation }
      });
    } catch (error) {
      console.error('Create service error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create service',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
);

// Update service
router.put('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Get service with location
      const service = await Service.findByPk(id, {
        include: [
          {
            model: Location,
            as: 'location'
          }
        ]
      });

      if (!service) {
        return res.status(404).json({
          success: false,
          message: 'Service not found'
        });
      }

      // Check if user can manage this service's location
      if (req.user.role !== 'system_admin' && service.location.admin_user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to update this service'
        });
      }

      // Remove fields that shouldn't be updated directly
      delete updateData.id;
      delete updateData.location_id;

      await service.update(updateData);

      // Return updated service with location details
      const updatedService = await Service.findByPk(id, {
        include: [
          {
            model: Location,
            as: 'location',
            attributes: ['id', 'name', 'address', 'district', 'type']
          }
        ]
      });

      res.json({
        success: true,
        message: 'Service updated successfully',
        data: { service: updatedService }
      });
    } catch (error) {
      console.error('Update service error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update service'
      });
    }
  }
);

// Get service availability for a specific date
router.get('/:id/availability', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { date } = req.query;

    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date parameter is required'
      });
    }

    const service = await Service.findByPk(id, {
      include: [
        {
          model: Location,
          as: 'location',
          attributes: ['operating_hours', 'appointment_duration']
        }
      ]
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Check if date is within advance booking limit
    if (!service.isAvailableForDate(date)) {
      return res.status(400).json({
        success: false,
        message: 'Date is outside the advance booking window'
      });
    }

    // Get existing appointments for this date
    const existingAppointments = await Appointment.findAll({
      where: {
        service_id: id,
        appointment_date: date,
        status: ['scheduled', 'confirmed', 'in_progress']
      },
      attributes: ['appointment_time']
    });

    // Generate available time slots
    const targetDate = new Date(date);
    const dayOfWeek = targetDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const operatingHours = service.location.operating_hours[dayOfWeek];

    if (!operatingHours || operatingHours.closed) {
      return res.json({
        success: true,
        data: {
          available_slots: [],
          message: 'Location is closed on this day'
        }
      });
    }

    // Generate time slots (simplified version)
    const slots = [];
    const duration = service.duration;
    const startTime = operatingHours.open;
    const endTime = operatingHours.close;

    // This is a simplified slot generation - in production, you'd want more sophisticated logic
    const bookedTimes = existingAppointments.map(apt => apt.appointment_time);

    res.json({
      success: true,
      data: {
        available_slots: slots,
        booked_slots: bookedTimes,
        operating_hours: operatingHours,
        service_duration: duration
      }
    });
  } catch (error) {
    console.error('Get service availability error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service availability'
    });
  }
});

// Get service statistics
router.get('/:id/stats', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { start_date, end_date } = req.query;

    const service = await Service.findByPk(id, {
      include: [
        {
          model: Location,
          as: 'location'
        }
      ]
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Check if user can view this service's stats
    if (req.user.role !== 'system_admin' && service.location.admin_user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view this service statistics'
      });
    }

    const dateFilter = {};
    if (start_date) dateFilter[Op.gte] = new Date(start_date);
    if (end_date) dateFilter[Op.lte] = new Date(end_date);

    const whereClause = { service_id: id };
    if (Object.keys(dateFilter).length > 0) {
      whereClause.appointment_date = dateFilter;
    }

    const totalAppointments = await Appointment.count({
      where: whereClause
    });

    const appointmentsByStatus = await Appointment.findAll({
      where: whereClause,
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    res.json({
      success: true,
      data: {
        service_name: service.name,
        total_appointments: totalAppointments,
        appointments_by_status: appointmentsByStatus
      }
    });
  } catch (error) {
    console.error('Get service stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service statistics'
    });
  }
});

// Deactivate service
router.put('/:id/deactivate', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const service = await Service.findByPk(id, {
      include: [
        {
          model: Location,
          as: 'location'
        }
      ]
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Check authorization
    if (req.user.role !== 'system_admin' && service.location.admin_user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to deactivate this service'
      });
    }

    await service.update({ is_active: false });

    res.json({
      success: true,
      message: 'Service deactivated successfully'
    });
  } catch (error) {
    console.error('Deactivate service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to deactivate service'
    });
  }
});

// Activate service
router.put('/:id/activate', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const service = await Service.findByPk(id, {
      include: [
        {
          model: Location,
          as: 'location'
        }
      ]
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Check authorization
    if (req.user.role !== 'system_admin' && service.location.admin_user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to activate this service'
      });
    }

    await service.update({ is_active: true });

    res.json({
      success: true,
      message: 'Service activated successfully'
    });
  } catch (error) {
    console.error('Activate service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to activate service'
    });
  }
});

// Delete service
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const service = await Service.findByPk(id, {
      include: [
        {
          model: Location,
          as: 'location'
        }
      ]
    });

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'Service not found'
      });
    }

    // Check authorization
    if (req.user.role !== 'system_admin' && service.location.admin_user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to delete this service'
      });
    }

    // Check for active appointments
    const activeAppointments = await Appointment.count({
      where: {
        service_id: id,
        status: ['scheduled', 'confirmed', 'in_progress']
      }
    });

    if (activeAppointments > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete service with active appointments'
      });
    }

    await service.destroy();

    res.json({
      success: true,
      message: 'Service deleted successfully'
    });
  } catch (error) {
    console.error('Delete service error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete service'
    });
  }
});

module.exports = router;
