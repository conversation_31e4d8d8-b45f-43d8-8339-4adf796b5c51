require('dotenv').config();
const { User } = require('./models');
const bcrypt = require('bcryptjs');

async function seedAndTest() {
  try {
    console.log('🌱 Creating test users...');

    // Create customer user
    const customerExists = await User.findOne({ where: { phone: '+250788000003' } });
    
    if (!customerExists) {
      const customer = await User.create({
        name: 'Alice Uwimana',
        phone: '+250788000003',
        email: '<EMAIL>',
        password: 'customer123',
        role: 'customer',
        is_verified: true,
        language: 'rw'
      });
      console.log('✅ Customer created:', customer.name);
    } else {
      console.log('✅ Customer already exists:', customerExists.name);
    }

    // Create admin user
    const adminExists = await User.findOne({ where: { phone: '+250788000001' } });
    
    if (!adminExists) {
      const admin = await User.create({
        name: 'System Administrator',
        phone: '+250788000001',
        email: '<EMAIL>',
        password: 'admin123456',
        role: 'system_admin',
        is_verified: true,
        language: 'en'
      });
      console.log('✅ Admin created:', admin.name);
    } else {
      console.log('✅ Admin already exists:', adminExists.name);
    }

    console.log('\n🧪 Testing password comparison...');
    
    // Test customer login
    const customer = await User.findOne({ where: { phone: '+250788000003' } });
    if (customer) {
      const isValid = await customer.comparePassword('customer123');
      console.log('Customer password test:', isValid ? '✅ PASS' : '❌ FAIL');
      
      // Test wrong password
      const isInvalid = await customer.comparePassword('wrongpassword');
      console.log('Wrong password test:', !isInvalid ? '✅ PASS' : '❌ FAIL');
    }

    console.log('\n📋 Test Credentials:');
    console.log('Customer: +250788000003 / customer123');
    console.log('Admin: +250788000001 / admin123456');
    
    console.log('\n✅ Database seeding and testing completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

seedAndTest();
