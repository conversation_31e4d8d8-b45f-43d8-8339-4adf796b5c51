{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport startOfUT<PERSON><PERSON>Week from \"../startOfUTCISOWeek/index.js\";\nimport startOfUTCISOWeekYear from \"../startOfUTCISOWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\nexport default function getUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime();\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "map": {"version": 3, "names": ["toDate", "startOfUTCISOWeek", "startOfUTCISOWeekYear", "requiredArgs", "MILLISECONDS_IN_WEEK", "getUTCISOWeek", "dirtyDate", "arguments", "date", "diff", "getTime", "Math", "round"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/_lib/getUTCISOWeek/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport startOfUT<PERSON><PERSON>Week from \"../startOfUTCISOWeek/index.js\";\nimport startOfUTCISOWeekYear from \"../startOfUTCISOWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\nexport default function getUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime();\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,YAAY,MAAM,0BAA0B;AACnD,IAAIC,oBAAoB,GAAG,SAAS;AACpC,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC/CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,MAAM,CAACM,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGR,iBAAiB,CAACO,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGR,qBAAqB,CAACM,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC;;EAEpF;EACA;EACA;EACA,OAAOC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGL,oBAAoB,CAAC,GAAG,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}