"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const account_1 = require("./v2010/account");
class V2010 extends Version_1.default {
    /**
     * Initialize the V2010 version of Api
     *
     * @param domain - The Twilio (Twilio.Api) domain
     */
    constructor(domain) {
        super(domain, "2010-04-01");
    }
    /** Getter for accounts resource */
    get accounts() {
        this._accounts = this._accounts || (0, account_1.AccountListInstance)(this);
        return this._accounts;
    }
    /** Getter for account resource */
    get account() {
        this._account =
            this._account || (0, account_1.AccountListInstance)(this)(this.domain.twilio.accountSid);
        return this._account;
    }
}
exports.default = V2010;
