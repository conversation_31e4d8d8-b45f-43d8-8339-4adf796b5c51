{"ast": null, "code": "import addQuarters from \"../addQuarters/index.js\";\nimport startOfQuarter from \"../startOfQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport default function eachQuarterOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var startDateQuarter = startOfQuarter(startDate);\n  var endDateQuarter = startOfQuarter(endDate);\n  endTime = endDateQuarter.getTime();\n  var quarters = [];\n  var currentQuarter = startDateQuarter;\n  while (currentQuarter.getTime() <= endTime) {\n    quarters.push(toDate(currentQuarter));\n    currentQuarter = addQuarters(currentQuarter, 1);\n  }\n  return quarters;\n}", "map": {"version": 3, "names": ["addQuarters", "startOfQuarter", "toDate", "requiredArgs", "eachQuarterOfInterval", "dirtyInterval", "arguments", "interval", "startDate", "start", "endDate", "end", "endTime", "getTime", "RangeError", "startDateQuarter", "endDateQuarter", "quarters", "currentQuarter", "push"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/eachQuarterOfInterval/index.js"], "sourcesContent": ["import addQuarters from \"../addQuarters/index.js\";\nimport startOfQuarter from \"../startOfQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport default function eachQuarterOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var startDateQuarter = startOfQuarter(startDate);\n  var endDateQuarter = startOfQuarter(endDate);\n  endTime = endDateQuarter.getTime();\n  var quarters = [];\n  var currentQuarter = startDateQuarter;\n  while (currentQuarter.getTime() <= endTime) {\n    quarters.push(toDate(currentQuarter));\n    currentQuarter = addQuarters(currentQuarter, 1);\n  }\n  return quarters;\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,aAAa,EAAE;EAC3DF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGF,aAAa,IAAI,CAAC,CAAC;EAClC,IAAIG,SAAS,GAAGN,MAAM,CAACK,QAAQ,CAACE,KAAK,CAAC;EACtC,IAAIC,OAAO,GAAGR,MAAM,CAACK,QAAQ,CAACI,GAAG,CAAC;EAClC,IAAIC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAAC,CAAC;;EAE/B;EACA,IAAI,EAAEL,SAAS,CAACK,OAAO,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;IACrC,MAAM,IAAIE,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,gBAAgB,GAAGd,cAAc,CAACO,SAAS,CAAC;EAChD,IAAIQ,cAAc,GAAGf,cAAc,CAACS,OAAO,CAAC;EAC5CE,OAAO,GAAGI,cAAc,CAACH,OAAO,CAAC,CAAC;EAClC,IAAII,QAAQ,GAAG,EAAE;EACjB,IAAIC,cAAc,GAAGH,gBAAgB;EACrC,OAAOG,cAAc,CAACL,OAAO,CAAC,CAAC,IAAID,OAAO,EAAE;IAC1CK,QAAQ,CAACE,IAAI,CAACjB,MAAM,CAACgB,cAAc,CAAC,CAAC;IACrCA,cAAc,GAAGlB,WAAW,CAACkB,cAAc,EAAE,CAAC,CAAC;EACjD;EACA,OAAOD,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}