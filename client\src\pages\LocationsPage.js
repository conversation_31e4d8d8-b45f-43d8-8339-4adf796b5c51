import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useApp } from '../contexts/AppContext';
import {
  MapPinIcon,
  ClockIcon,
  PhoneIcon,
  BuildingOfficeIcon,
  MagnifyingGlassIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';

const LocationsPage = () => {
  const { locations, fetchLocations, fetchServices, loading } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [locationServices, setLocationServices] = useState([]);

  useEffect(() => {
    fetchLocations();
  }, []);

  const filteredLocations = locations.filter(location =>
    location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    location.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLocationClick = async (location) => {
    setSelectedLocation(location);
    const services = await fetchServices(location.id);
    setLocationServices(services || []);
  };

  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<StarSolidIcon key={i} className="w-4 h-4 text-yellow-400" />);
    }

    if (hasHalfStar) {
      stars.push(<StarIcon key="half" className="w-4 h-4 text-yellow-400" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<StarIcon key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }

    return stars;
  };

  if (loading && locations.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-3xl font-bold text-gray-900">Service Locations</h1>
        <p className="text-gray-600 mt-2">
          Discover and explore service locations near you. Find the perfect place for your needs.
        </p>
      </motion.div>

      {/* Search Bar */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        className="relative"
      >
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search locations by name or address..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
      </motion.div>

      {/* Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <div className="bg-white rounded-xl shadow-soft p-6">
          <div className="flex items-center">
            <div className="bg-primary-100 p-3 rounded-lg">
              <BuildingOfficeIcon className="w-6 h-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Locations</p>
              <p className="text-2xl font-bold text-gray-900">{locations.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-soft p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <ClockIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Open Now</p>
              <p className="text-2xl font-bold text-gray-900">
                {locations.filter(loc => loc.is_active).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-soft p-6">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-lg">
              <StarIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-gray-900">4.8</p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Locations Grid */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Locations List */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-4"
        >
          <h2 className="text-xl font-bold text-gray-900">
            Available Locations ({filteredLocations.length})
          </h2>

          {filteredLocations.length === 0 ? (
            <div className="bg-white rounded-xl shadow-soft p-8 text-center">
              <MapPinIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No locations found matching your search.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredLocations.map((location, index) => (
                <motion.div
                  key={location.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  onClick={() => handleLocationClick(location)}
                  className={`bg-white rounded-xl shadow-soft p-6 cursor-pointer transition-all duration-200 hover:shadow-medium ${
                    selectedLocation?.id === location.id ? 'ring-2 ring-primary-500 bg-primary-50' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {location.name}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          location.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {location.is_active ? 'Open' : 'Closed'}
                        </span>
                      </div>

                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPinIcon className="w-4 h-4 mr-2" />
                        <span className="text-sm">{location.address}</span>
                      </div>

                      {location.phone && (
                        <div className="flex items-center text-gray-600 mb-2">
                          <PhoneIcon className="w-4 h-4 mr-2" />
                          <span className="text-sm">{location.phone}</span>
                        </div>
                      )}

                      <div className="flex items-center text-gray-600">
                        <ClockIcon className="w-4 h-4 mr-2" />
                        <span className="text-sm">
                          {location.operating_hours || 'Mon-Fri: 8:00 AM - 6:00 PM'}
                        </span>
                      </div>

                      {/* Rating */}
                      <div className="flex items-center mt-3">
                        <div className="flex items-center">
                          {renderRating(4.5)}
                        </div>
                        <span className="ml-2 text-sm text-gray-600">4.5 (120 reviews)</span>
                      </div>
                    </div>

                    <div className="ml-4">
                      <div className="bg-primary-100 p-2 rounded-lg">
                        <BuildingOfficeIcon className="w-6 h-6 text-primary-600" />
                      </div>
                    </div>
                  </div>

                  {location.description && (
                    <p className="text-gray-600 text-sm mt-3 line-clamp-2">
                      {location.description}
                    </p>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Location Details */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white rounded-xl shadow-soft p-6"
        >
          {selectedLocation ? (
            <div>
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                {selectedLocation.name}
              </h2>

              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Location Details</h3>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <MapPinIcon className="w-4 h-4 mr-2" />
                      {selectedLocation.address}
                    </div>
                    {selectedLocation.phone && (
                      <div className="flex items-center">
                        <PhoneIcon className="w-4 h-4 mr-2" />
                        {selectedLocation.phone}
                      </div>
                    )}
                    <div className="flex items-center">
                      <ClockIcon className="w-4 h-4 mr-2" />
                      {selectedLocation.operating_hours || 'Mon-Fri: 8:00 AM - 6:00 PM'}
                    </div>
                  </div>
                </div>

                {selectedLocation.description && (
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">About</h3>
                    <p className="text-sm text-gray-600">{selectedLocation.description}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">Available Services</h3>
                  {locationServices.length > 0 ? (
                    <div className="space-y-2">
                      {locationServices.map((service) => (
                        <div key={service.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{service.name}</p>
                            <p className="text-sm text-gray-600">
                              Duration: {service.duration} min
                            </p>
                          </div>
                          {service.price && (
                            <span className="text-primary-600 font-semibold">
                              {service.price} RWF
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">Loading services...</p>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <MapPinIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Select a Location
              </h3>
              <p className="text-gray-600">
                Click on a location from the list to view details and available services.
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default LocationsPage;
