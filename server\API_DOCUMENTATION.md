# Umurongo API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
Most endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Response Format
All responses follow this format:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": { ... },
  "errors": [ ... ] // Only for validation errors
}
```

## Authentication Endpoints

### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "name": "<PERSON>",
  "phone": "+************",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "customer",
  "language": "en"
}
```

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "phone": "+************",
  "password": "password123"
}
```

### Verify Phone
```http
POST /auth/verify-phone
Content-Type: application/json

{
  "phone": "+************",
  "code": "123456"
}
```

### Get Profile
```http
GET /auth/profile
Authorization: Bearer <token>
```

## Location Endpoints

### Get All Locations
```http
GET /locations?page=1&limit=10&type=health_center&district=Gasabo
```

### Get Location by ID
```http
GET /locations/:id
```

### Create Location (Admin)
```http
POST /locations
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "name": "Remera Health Center",
  "description": "Primary healthcare facility",
  "address": "KG 11 Ave, Remera",
  "district": "Gasabo",
  "sector": "Remera",
  "contact_phone": "+************",
  "type": "health_center",
  "max_daily_appointments": 150
}
```

## Service Endpoints

### Get All Services
```http
GET /services?location_id=uuid&category=consultation
```

### Get Service Availability
```http
GET /services/:id/availability?date=2024-01-15
```

### Create Service (Admin)
```http
POST /services
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "name": "General Consultation",
  "description": "Medical consultation",
  "category": "consultation",
  "duration": 30,
  "location_id": "uuid",
  "max_daily_slots": 40
}
```

## Appointment Endpoints

### Get User Appointments
```http
GET /appointments?status=scheduled&page=1&limit=10
Authorization: Bearer <token>
```

### Book Appointment
```http
POST /appointments
Authorization: Bearer <token>
Content-Type: application/json

{
  "service_id": "uuid",
  "appointment_date": "2024-01-15",
  "appointment_time": "09:30",
  "notes": "First visit"
}
```

### Cancel Appointment
```http
PUT /appointments/:id/cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "Schedule conflict"
}
```

### Update Appointment Status (Admin)
```http
PUT /appointments/:id/status
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "status": "completed",
  "notes": "Patient seen successfully",
  "room_number": "Room 2"
}
```

## Queue Endpoints

### Get Location Queue
```http
GET /queues/location/:locationId?date=2024-01-15&status=waiting
Authorization: Bearer <admin-token>
```

### Get My Queue Position
```http
GET /queues/my-position
Authorization: Bearer <token>
```

### Call Next in Queue (Admin)
```http
POST /queues/location/:locationId/call-next
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "service_id": "uuid",
  "room_number": "Room 1"
}
```

### Update Queue Status (Admin)
```http
PUT /queues/:id/status
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "status": "in_service",
  "room_number": "Room 2",
  "notes": "Patient called"
}
```

## Report Endpoints (Admin Only)

### Dashboard Statistics
```http
GET /reports/dashboard?location_id=uuid&start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer <admin-token>
```

### Appointment Report
```http
GET /reports/appointments?location_id=uuid&status=completed&page=1&limit=50
Authorization: Bearer <admin-token>
```

### Queue Performance
```http
GET /reports/queue-performance?location_id=uuid&start_date=2024-01-01
Authorization: Bearer <admin-token>
```

### Feedback Summary
```http
GET /reports/feedback?location_id=uuid&service_id=uuid
Authorization: Bearer <admin-token>
```

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting
- 100 requests per 15 minutes per IP address
- Higher limits for authenticated users

## Phone Number Format
All phone numbers should be in Rwanda format:
- `+************` (international)
- `************` (with country code)
- `0788123456` (local format, will be converted)

## Date/Time Format
- Dates: `YYYY-MM-DD` (e.g., `2024-01-15`)
- Times: `HH:MM` (24-hour format, e.g., `14:30`)
- Timestamps: ISO 8601 format

## Pagination
List endpoints support pagination:
```
?page=1&limit=10
```

Response includes pagination info:
```json
{
  "data": {
    "items": [...],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10
    }
  }
}
```

## Testing with cURL

### Register and Login
```bash
# Register
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","phone":"+************","password":"test123456"}'

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+************","password":"test123456"}'
```

### Book Appointment
```bash
curl -X POST http://localhost:5000/api/appointments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"service_id":"SERVICE_UUID","appointment_date":"2024-01-15","appointment_time":"10:00"}'
```
