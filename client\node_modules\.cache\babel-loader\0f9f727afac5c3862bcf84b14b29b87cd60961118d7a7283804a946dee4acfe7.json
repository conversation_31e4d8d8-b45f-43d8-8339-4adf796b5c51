{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousThursday\n * @category Weekday Helpers\n * @summary When is the previous Thursday?\n *\n * @description\n * When is the previous Thursday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Thursday before Jun, 18, 2021?\n * const result = previousThursday(new Date(2021, 5, 18))\n * //=> Thu June 17 2021 00:00:00\n */\nexport default function previousThursday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 4);\n}", "map": {"version": 3, "names": ["requiredArgs", "previousDay", "previousThursday", "date", "arguments"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/previousThursday/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousThursday\n * @category Weekday Helpers\n * @summary When is the previous Thursday?\n *\n * @description\n * When is the previous Thursday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Thursday before Jun, 18, 2021?\n * const result = previousThursday(new Date(2021, 5, 18))\n * //=> Thu June 17 2021 00:00:00\n */\nexport default function previousThursday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 4);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,WAAW,MAAM,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC7CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOH,WAAW,CAACE,IAAI,EAAE,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}