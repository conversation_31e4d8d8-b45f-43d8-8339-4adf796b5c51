{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfDecade\n * @category Decade Helpers\n * @summary Return the last day of a decade for the given date.\n *\n * @description\n * Return the last day of a decade for the given date.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a decade for 21 December 2012 21:12:00:\n * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))\n * //=> Wed Dec 31 2019 00:00:00\n */\nexport default function lastDayOfDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = 9 + Math.floor(year / 10) * 10;\n  date.setFullYear(decade + 1, 0, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "lastDayOfDecade", "dirtyDate", "arguments", "date", "year", "getFullYear", "decade", "Math", "floor", "setFullYear", "setHours"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/lastDayOfDecade/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfDecade\n * @category Decade Helpers\n * @summary Return the last day of a decade for the given date.\n *\n * @description\n * Return the last day of a decade for the given date.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a decade for 21 December 2012 21:12:00:\n * const result = lastDayOfDecade(new Date(2012, 11, 21, 21, 12, 00))\n * //=> Wed Dec 31 2019 00:00:00\n */\nexport default function lastDayOfDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = 9 + Math.floor(year / 10) * 10;\n  date.setFullYear(decade + 1, 0, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAE;EACjDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;EAC7B,IAAIC,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE;EAC3CD,IAAI,CAACM,WAAW,CAACH,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClCH,IAAI,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOP,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}