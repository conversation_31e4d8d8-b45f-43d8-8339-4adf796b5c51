const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Service = sequelize.define('Service', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category: {
    type: DataTypes.ENUM(
      'consultation',
      'vaccination',
      'checkup',
      'emergency',
      'dental',
      'eye_care',
      'maternity',
      'pediatric',
      'surgery',
      'laboratory',
      'pharmacy',
      'haircut',
      'styling',
      'manicure',
      'pedicure',
      'massage',
      'government_service',
      'banking',
      'other'
    ),
    allowNull: false
  },
  duration: {
    type: DataTypes.INTEGER, // in minutes
    defaultValue: 30,
    validate: {
      min: 5,
      max: 480
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  requires_payment: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  max_daily_slots: {
    type: DataTypes.INTEGER,
    defaultValue: 50,
    validate: {
      min: 1,
      max: 500
    }
  },
  advance_booking_days: {
    type: DataTypes.INTEGER,
    defaultValue: 7,
    validate: {
      min: 0,
      max: 365
    }
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  requires_documents: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  preparation_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  age_restrictions: {
    type: DataTypes.JSON,
    defaultValue: {
      min_age: null,
      max_age: null
    }
  },
  gender_restrictions: {
    type: DataTypes.ENUM('male', 'female', 'any'),
    defaultValue: 'any'
  },
  priority_level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    validate: {
      min: 1,
      max: 5
    }
  },
  location_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'locations',
      key: 'id'
    }
  }
}, {
  tableName: 'services',
  indexes: [
    {
      fields: ['location_id']
    },
    {
      fields: ['category']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['requires_payment']
    }
  ]
});

// Instance methods
Service.prototype.isAvailableForDate = function(date) {
  const targetDate = new Date(date);
  const today = new Date();
  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays >= 0 && diffDays <= this.advance_booking_days;
};

Service.prototype.calculateEstimatedWaitTime = function(queuePosition) {
  return queuePosition * this.duration;
};

module.exports = Service;
