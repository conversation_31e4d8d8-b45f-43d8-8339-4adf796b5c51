# 🎨 Umurongo Frontend - React.js Application

## 🚀 Overview

The Umurongo frontend is a modern, responsive React.js application built with cutting-edge technologies to provide an exceptional user experience for queue management and appointment booking.

## ✨ Features

### 🎯 Core Features
- **Beautiful Landing Page** with animated elements and modern design
- **User Authentication** (Login/Register) with form validation
- **Dashboard** with real-time stats and quick actions
- **Appointment Management** - Book, view, and manage appointments
- **Queue Management** - Join queues and track position in real-time
- **Location Discovery** - Find and explore service locations
- **Profile Management** - Update user settings and preferences
- **Admin Dashboard** - Manage locations, services, and analytics

### 🎨 Design Features
- **Modern UI/UX** with Tailwind CSS
- **Responsive Design** - Works perfectly on all devices
- **Smooth Animations** with Framer Motion
- **Glass Morphism** effects and modern gradients
- **Dark/Light Theme** support (coming soon)
- **Accessibility** compliant design

### 🔧 Technical Features
- **React 19** with modern hooks and patterns
- **React Router** for client-side routing
- **Context API** for state management
- **Axios** for API communication
- **React Hook Form** for form handling
- **Hot Toast** for notifications
- **Heroicons** for beautiful icons

## 📁 Project Structure

```
client/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── Layout.js      # Main layout with navigation
│   │   ├── LoadingSpinner.js
│   │   └── ProtectedRoute.js
│   ├── contexts/          # React Context providers
│   │   ├── AuthContext.js # Authentication state
│   │   └── AppContext.js  # Application state
│   ├── pages/             # Page components
│   │   ├── LandingPage.js # Beautiful landing page
│   │   ├── LoginPage.js   # User login
│   │   ├── RegisterPage.js # User registration
│   │   ├── DashboardPage.js # Main dashboard
│   │   ├── LocationsPage.js
│   │   ├── ServicesPage.js
│   │   ├── AppointmentsPage.js
│   │   ├── QueuePage.js
│   │   ├── ProfilePage.js
│   │   └── AdminDashboard.js
│   ├── utils/             # Utility functions
│   │   ├── api.js         # Axios configuration
│   │   └── cn.js          # Tailwind class merger
│   ├── App.js             # Main app component
│   ├── index.js           # App entry point
│   └── index.css          # Global styles
├── tailwind.config.js     # Tailwind configuration
└── package.json           # Dependencies
```

## 🎨 Design System

### Colors
- **Primary**: Blue shades for main actions and branding
- **Secondary**: Gray shades for text and backgrounds
- **Success**: Green for positive actions
- **Warning**: Yellow for alerts
- **Danger**: Red for errors

### Typography
- **Font**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700, 800

### Components
- **Buttons**: Multiple variants with hover effects
- **Forms**: Beautiful input fields with validation
- **Cards**: Glass morphism effects with shadows
- **Navigation**: Responsive sidebar and top navigation

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ installed
- Backend server running on port 5001

### Installation

1. **Navigate to client directory**
   ```bash
   cd client
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm start
   ```

4. **Open browser**
   Navigate to `http://localhost:3000`

### Environment Setup

The app automatically connects to:
- **Development**: `http://localhost:5001` (backend)
- **Production**: Configure in `src/utils/api.js`

## 📱 Pages Overview

### 🏠 Landing Page (`/`)
- Hero section with animated typewriter effect
- Feature showcase with icons and descriptions
- Statistics section
- Call-to-action sections
- Responsive footer

### 🔐 Authentication Pages
- **Login** (`/login`) - Phone number and password
- **Register** (`/register`) - Full registration form
- Form validation with React Hook Form
- Demo credentials provided

### 📊 Dashboard (`/dashboard`)
- Welcome header with user greeting
- Quick stats cards (appointments, queue position, etc.)
- Queue status alerts
- Upcoming appointments list
- Quick action buttons

### 🏢 Locations Page (`/locations`)
- Browse available service locations
- Search and filter functionality
- Location details and services

### 🛠️ Services Page (`/services`)
- Browse available services
- Service details and booking
- Availability checking

### 📅 Appointments Page (`/appointments`)
- View all appointments (upcoming, past, cancelled)
- Book new appointments
- Cancel or reschedule existing appointments

### ⏰ Queue Page (`/queue`)
- Real-time queue position tracking
- Join/leave queue functionality
- Estimated wait times

### 👤 Profile Page (`/profile`)
- Update personal information
- Change password
- Notification preferences

### 🔧 Admin Dashboard (`/admin`)
- System analytics and reports
- Manage locations and services
- User management
- Queue monitoring

## 🎯 Key Features Implementation

### Authentication Flow
1. User enters credentials
2. Frontend validates form
3. API call to backend
4. Token stored in localStorage
5. User redirected to dashboard
6. Protected routes check authentication

### State Management
- **AuthContext**: User authentication state
- **AppContext**: Application data (appointments, locations, etc.)
- Local state for component-specific data

### API Integration
- Axios instance with interceptors
- Automatic token attachment
- Error handling and redirects
- Loading states

### Responsive Design
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Flexible grid layouts
- Touch-friendly interactions

## 🎨 Styling Guide

### Tailwind Classes
```css
/* Primary button */
bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg

/* Card */
bg-white rounded-xl shadow-soft p-6

/* Input field */
border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500
```

### Custom Animations
- `animate-pulse-slow`: Slow pulse effect
- `animate-fade-in`: Fade in animation
- `animate-slide-up`: Slide up animation

## 🔧 Development Tips

### Adding New Pages
1. Create component in `src/pages/`
2. Add route in `App.js`
3. Add navigation link in `Layout.js`
4. Implement page content

### State Management
- Use `useAuth()` for authentication state
- Use `useApp()` for application data
- Create local state for component-specific data

### API Calls
```javascript
import { useApp } from '../contexts/AppContext';

const { fetchAppointments, bookAppointment } = useApp();
```

### Form Handling
```javascript
import { useForm } from 'react-hook-form';

const { register, handleSubmit, formState: { errors } } = useForm();
```

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Netlify/Vercel
1. Connect GitHub repository
2. Set build command: `npm run build`
3. Set publish directory: `build`
4. Configure environment variables

## 🎯 Next Steps

### Planned Features
- [ ] Real-time notifications with WebSocket
- [ ] PWA (Progressive Web App) support
- [ ] Dark mode toggle
- [ ] Multi-language support (Kinyarwanda, English, French)
- [ ] Advanced search and filtering
- [ ] Calendar integration
- [ ] Payment integration
- [ ] Offline support

### Performance Optimizations
- [ ] Code splitting with React.lazy
- [ ] Image optimization
- [ ] Bundle size optimization
- [ ] Caching strategies

## 🎨 Design Credits

- **Icons**: Heroicons by Tailwind Labs
- **Fonts**: Inter by Google Fonts
- **Colors**: Custom palette inspired by Rwanda's flag
- **Animations**: Framer Motion
- **UI Framework**: Tailwind CSS

## 📞 Support

For frontend-related issues:
1. Check browser console for errors
2. Verify backend connection
3. Check network requests in DevTools
4. Review component props and state

---

**Built with ❤️ for Rwanda's digital transformation**
