const axios = require('axios');

async function testLogin() {
  console.log('🧪 Testing Umurongo Login API...\n');

  const baseURL = 'http://localhost:5002';
  
  try {
    // Test 1: Health Check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${baseURL}/health`);
    console.log('✅ Health check passed:', healthResponse.data.message);
    console.log('');

    // Test 2: Login with customer credentials
    console.log('2. Testing login with customer credentials...');
    const loginData = {
      phone: '+250788000003',
      password: 'customer123'
    };

    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, loginData);
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful!');
      console.log('User:', loginResponse.data.data.user.name);
      console.log('Role:', loginResponse.data.data.user.role);
      console.log('Token length:', loginResponse.data.data.token.length);
      
      // Test 3: Profile with token
      console.log('');
      console.log('3. Testing profile endpoint with token...');
      const token = loginResponse.data.data.token;
      
      const profileResponse = await axios.get(`${baseURL}/api/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (profileResponse.data.success) {
        console.log('✅ Profile fetch successful!');
        console.log('Profile user:', profileResponse.data.data.user.name);
      } else {
        console.log('❌ Profile fetch failed');
      }
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }

  } catch (error) {
    console.log('❌ Test failed:');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   Backend server is not running on port 5002');
      console.log('   Solution: Run "cd server && node working-server.js"');
    } else if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Message:', error.response.data?.message || error.response.statusText);
    } else {
      console.log('   Error:', error.message);
    }
  }

  console.log('\n🎯 To start the backend:');
  console.log('cd server && node working-server.js');
  console.log('\n🎯 To start the frontend:');
  console.log('cd client && npm start');
  console.log('\n📱 Test credentials:');
  console.log('Phone: +250788000003');
  console.log('Password: customer123');
}

testLogin();
