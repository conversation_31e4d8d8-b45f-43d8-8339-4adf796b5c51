{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/auth/profile');\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const login = async (phone, password) => {\n    try {\n      var _response$data$data, _response$data$data2;\n      console.log('🔐 Frontend: Attempting login with:', {\n        phone,\n        password: '***'\n      });\n\n      // Validate inputs\n      if (!phone || !password) {\n        const message = 'Please enter both phone number and password';\n        toast.error(message);\n        return {\n          success: false,\n          error: message\n        };\n      }\n\n      // Make API call\n      const response = await axios.post('/api/auth/login', {\n        phone: phone.trim(),\n        password: password.trim()\n      }, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      });\n      console.log('🔐 Frontend: Login response received:', {\n        status: response.status,\n        success: response.data.success,\n        hasUser: !!((_response$data$data = response.data.data) !== null && _response$data$data !== void 0 && _response$data$data.user),\n        hasToken: !!((_response$data$data2 = response.data.data) !== null && _response$data$data2 !== void 0 && _response$data$data2.token)\n      });\n      if (!response.data.success) {\n        const message = response.data.message || 'Login failed';\n        toast.error(message);\n        return {\n          success: false,\n          error: message\n        };\n      }\n      const {\n        user,\n        token\n      } = response.data.data;\n      if (!user || !token) {\n        const message = 'Invalid response from server';\n        toast.error(message);\n        return {\n          success: false,\n          error: message\n        };\n      }\n      console.log('🔐 Frontend: Setting user and token');\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      toast.success(`Welcome back, ${user.name}!`);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      console.error('🔐 Frontend: Login error:', error);\n      let message = 'Login failed';\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        message = 'Cannot connect to server. Please make sure the backend is running on port 5002.';\n      } else if (error.code === 'ECONNABORTED') {\n        message = 'Request timeout. Please try again.';\n      } else if (error.response) {\n        var _error$response$data2;\n        // Server responded with error\n        console.error('🔐 Frontend: Server error response:', {\n          status: error.response.status,\n          data: error.response.data\n        });\n        if (error.response.status === 401) {\n          message = 'Invalid phone number or password. Please check your credentials.';\n        } else if (error.response.status === 400) {\n          var _error$response$data;\n          message = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Invalid request. Please check your input.';\n        } else if ((_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && _error$response$data2.message) {\n          message = error.response.data.message;\n        } else {\n          message = `Server error (${error.response.status}). Please try again.`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        message = 'No response from server. Please check your internet connection.';\n      }\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('/api/auth/register', userData);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      toast.success('Registration successful! Please verify your phone number.');\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response, _error$response$data3;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data3 = _error$response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || 'Registration failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const verifyPhone = async (phone, code) => {\n    try {\n      const response = await axios.post('/api/auth/verify-phone', {\n        phone,\n        code\n      });\n\n      // Update user verification status\n      setUser(prev => ({\n        ...prev,\n        is_verified: true\n      }));\n      toast.success('Phone number verified successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Verification failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const resendVerification = async phone => {\n    try {\n      await axios.post('/api/auth/resend-verification', {\n        phone\n      });\n      toast.success('Verification code sent!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to send verification code';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await axios.put('/api/users/profile', profileData);\n      setUser(response.data.data.user);\n      toast.success('Profile updated successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Profile update failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('/api/users/change-password', {\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n      toast.success('Password changed successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Password change failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const value = {\n    user,\n    loading,\n    token,\n    login,\n    register,\n    verifyPhone,\n    resendVerification,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user,\n    isVerified: (user === null || user === void 0 ? void 0 : user.is_verified) || false,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'system_admin' || (user === null || user === void 0 ? void 0 : user.role) === 'business_admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"V8bE6DZSV5/nB2UMC4Uofie15PA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "console", "logout", "login", "phone", "password", "_response$data$data", "_response$data$data2", "log", "message", "success", "post", "trim", "timeout", "status", "<PERSON><PERSON>ser", "hasToken", "setItem", "name", "code", "_error$response$data2", "_error$response$data", "request", "register", "userData", "_error$response", "_error$response$data3", "verifyPhone", "prev", "is_verified", "_error$response2", "_error$response2$data", "resendVerification", "_error$response3", "_error$response3$data", "removeItem", "updateProfile", "profileData", "put", "_error$response4", "_error$response4$data", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "_error$response5", "_error$response5$data", "value", "isAuthenticated", "isVerified", "isAdmin", "role", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/auth/profile');\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (phone, password) => {\n    try {\n      console.log('🔐 Frontend: Attempting login with:', { phone, password: '***' });\n\n      // Validate inputs\n      if (!phone || !password) {\n        const message = 'Please enter both phone number and password';\n        toast.error(message);\n        return { success: false, error: message };\n      }\n\n      // Make API call\n      const response = await axios.post('/api/auth/login', {\n        phone: phone.trim(),\n        password: password.trim()\n      }, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      });\n\n      console.log('🔐 Frontend: Login response received:', {\n        status: response.status,\n        success: response.data.success,\n        hasUser: !!response.data.data?.user,\n        hasToken: !!response.data.data?.token\n      });\n\n      if (!response.data.success) {\n        const message = response.data.message || 'Login failed';\n        toast.error(message);\n        return { success: false, error: message };\n      }\n\n      const { user, token } = response.data.data;\n\n      if (!user || !token) {\n        const message = 'Invalid response from server';\n        toast.error(message);\n        return { success: false, error: message };\n      }\n\n      console.log('🔐 Frontend: Setting user and token');\n\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n\n      toast.success(`Welcome back, ${user.name}!`);\n      return { success: true, user };\n    } catch (error) {\n      console.error('🔐 Frontend: Login error:', error);\n\n      let message = 'Login failed';\n\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        message = 'Cannot connect to server. Please make sure the backend is running on port 5002.';\n      } else if (error.code === 'ECONNABORTED') {\n        message = 'Request timeout. Please try again.';\n      } else if (error.response) {\n        // Server responded with error\n        console.error('🔐 Frontend: Server error response:', {\n          status: error.response.status,\n          data: error.response.data\n        });\n\n        if (error.response.status === 401) {\n          message = 'Invalid phone number or password. Please check your credentials.';\n        } else if (error.response.status === 400) {\n          message = error.response.data?.message || 'Invalid request. Please check your input.';\n        } else if (error.response.data?.message) {\n          message = error.response.data.message;\n        } else {\n          message = `Server error (${error.response.status}). Please try again.`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        message = 'No response from server. Please check your internet connection.';\n      }\n\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('/api/auth/register', userData);\n\n      const { user, token } = response.data.data;\n\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n\n      toast.success('Registration successful! Please verify your phone number.');\n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const verifyPhone = async (phone, code) => {\n    try {\n      const response = await axios.post('/api/auth/verify-phone', {\n        phone,\n        code\n      });\n\n      // Update user verification status\n      setUser(prev => ({ ...prev, is_verified: true }));\n\n      toast.success('Phone number verified successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Verification failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const resendVerification = async (phone) => {\n    try {\n      await axios.post('/api/auth/resend-verification', { phone });\n      toast.success('Verification code sent!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to send verification code';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await axios.put('/api/users/profile', profileData);\n      setUser(response.data.data.user);\n      toast.success('Profile updated successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Profile update failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('/api/users/change-password', {\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n      toast.success('Password changed successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Password change failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    token,\n    login,\n    register,\n    verifyPhone,\n    resendVerification,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user,\n    isVerified: user?.is_verified || false,\n    isAdmin: user?.role === 'system_admin' || user?.role === 'business_admin'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAACoB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;EAEjE;EACApB,SAAS,CAAC,MAAM;IACd,IAAIiB,KAAK,EAAE;MACThB,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOhB,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;;EAEX;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMwB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIP,KAAK,EAAE;QACT,IAAI;UACF,MAAMQ,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,mBAAmB,CAAC;UACrDZ,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,IAAI,CAAC;QAClC,CAAC,CAAC,OAAOe,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CE,MAAM,CAAC,CAAC;QACV;MACF;MACAd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EAEX,MAAMc,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MAAA,IAAAC,mBAAA,EAAAC,oBAAA;MACFN,OAAO,CAACO,GAAG,CAAC,qCAAqC,EAAE;QAAEJ,KAAK;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC;;MAE9E;MACA,IAAI,CAACD,KAAK,IAAI,CAACC,QAAQ,EAAE;QACvB,MAAMI,OAAO,GAAG,6CAA6C;QAC7DnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;QACpB,OAAO;UAAEC,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAES;QAAQ,CAAC;MAC3C;;MAEA;MACA,MAAMZ,QAAQ,GAAG,MAAMxB,KAAK,CAACsC,IAAI,CAAC,iBAAiB,EAAE;QACnDP,KAAK,EAAEA,KAAK,CAACQ,IAAI,CAAC,CAAC;QACnBP,QAAQ,EAAEA,QAAQ,CAACO,IAAI,CAAC;MAC1B,CAAC,EAAE;QACDlB,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,OAAO,EAAE;MACX,CAAC,CAAC;MAEFZ,OAAO,CAACO,GAAG,CAAC,uCAAuC,EAAE;QACnDM,MAAM,EAAEjB,QAAQ,CAACiB,MAAM;QACvBJ,OAAO,EAAEb,QAAQ,CAACE,IAAI,CAACW,OAAO;QAC9BK,OAAO,EAAE,CAAC,GAAAT,mBAAA,GAACT,QAAQ,CAACE,IAAI,CAACA,IAAI,cAAAO,mBAAA,eAAlBA,mBAAA,CAAoBrB,IAAI;QACnC+B,QAAQ,EAAE,CAAC,GAAAT,oBAAA,GAACV,QAAQ,CAACE,IAAI,CAACA,IAAI,cAAAQ,oBAAA,eAAlBA,oBAAA,CAAoBlB,KAAK;MACvC,CAAC,CAAC;MAEF,IAAI,CAACQ,QAAQ,CAACE,IAAI,CAACW,OAAO,EAAE;QAC1B,MAAMD,OAAO,GAAGZ,QAAQ,CAACE,IAAI,CAACU,OAAO,IAAI,cAAc;QACvDnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;QACpB,OAAO;UAAEC,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAES;QAAQ,CAAC;MAC3C;MAEA,MAAM;QAAExB,IAAI;QAAEI;MAAM,CAAC,GAAGQ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1C,IAAI,CAACd,IAAI,IAAI,CAACI,KAAK,EAAE;QACnB,MAAMoB,OAAO,GAAG,8BAA8B;QAC9CnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;QACpB,OAAO;UAAEC,OAAO,EAAE,KAAK;UAAEV,KAAK,EAAES;QAAQ,CAAC;MAC3C;MAEAR,OAAO,CAACO,GAAG,CAAC,qCAAqC,CAAC;MAElDtB,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAAC0B,OAAO,CAAC,OAAO,EAAE5B,KAAK,CAAC;MAEpCf,KAAK,CAACoC,OAAO,CAAC,iBAAiBzB,IAAI,CAACiC,IAAI,GAAG,CAAC;MAC5C,OAAO;QAAER,OAAO,EAAE,IAAI;QAAEzB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MAEjD,IAAIS,OAAO,GAAG,cAAc;MAE5B,IAAIT,KAAK,CAACmB,IAAI,KAAK,cAAc,IAAInB,KAAK,CAACmB,IAAI,KAAK,aAAa,EAAE;QACjEV,OAAO,GAAG,iFAAiF;MAC7F,CAAC,MAAM,IAAIT,KAAK,CAACmB,IAAI,KAAK,cAAc,EAAE;QACxCV,OAAO,GAAG,oCAAoC;MAChD,CAAC,MAAM,IAAIT,KAAK,CAACH,QAAQ,EAAE;QAAA,IAAAuB,qBAAA;QACzB;QACAnB,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAE;UACnDc,MAAM,EAAEd,KAAK,CAACH,QAAQ,CAACiB,MAAM;UAC7Bf,IAAI,EAAEC,KAAK,CAACH,QAAQ,CAACE;QACvB,CAAC,CAAC;QAEF,IAAIC,KAAK,CAACH,QAAQ,CAACiB,MAAM,KAAK,GAAG,EAAE;UACjCL,OAAO,GAAG,kEAAkE;QAC9E,CAAC,MAAM,IAAIT,KAAK,CAACH,QAAQ,CAACiB,MAAM,KAAK,GAAG,EAAE;UAAA,IAAAO,oBAAA;UACxCZ,OAAO,GAAG,EAAAY,oBAAA,GAAArB,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAsB,oBAAA,uBAAnBA,oBAAA,CAAqBZ,OAAO,KAAI,2CAA2C;QACvF,CAAC,MAAM,KAAAW,qBAAA,GAAIpB,KAAK,CAACH,QAAQ,CAACE,IAAI,cAAAqB,qBAAA,eAAnBA,qBAAA,CAAqBX,OAAO,EAAE;UACvCA,OAAO,GAAGT,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACU,OAAO;QACvC,CAAC,MAAM;UACLA,OAAO,GAAG,iBAAiBT,KAAK,CAACH,QAAQ,CAACiB,MAAM,sBAAsB;QACxE;MACF,CAAC,MAAM,IAAId,KAAK,CAACsB,OAAO,EAAE;QACxB;QACAb,OAAO,GAAG,iEAAiE;MAC7E;MAEAnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;MACpB,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAES;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMc,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAMxB,KAAK,CAACsC,IAAI,CAAC,oBAAoB,EAAEa,QAAQ,CAAC;MAEjE,MAAM;QAAEvC,IAAI;QAAEI;MAAM,CAAC,GAAGQ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1Cb,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAAC0B,OAAO,CAAC,OAAO,EAAE5B,KAAK,CAAC;MAEpCf,KAAK,CAACoC,OAAO,CAAC,2DAA2D,CAAC;MAC1E,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEzB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAyB,eAAA,EAAAC,qBAAA;MACd,MAAMjB,OAAO,GAAG,EAAAgB,eAAA,GAAAzB,KAAK,CAACH,QAAQ,cAAA4B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBjB,OAAO,KAAI,qBAAqB;MACtEnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;MACpB,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAES;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMkB,WAAW,GAAG,MAAAA,CAAOvB,KAAK,EAAEe,IAAI,KAAK;IACzC,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMxB,KAAK,CAACsC,IAAI,CAAC,wBAAwB,EAAE;QAC1DP,KAAK;QACLe;MACF,CAAC,CAAC;;MAEF;MACAjC,OAAO,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEC,WAAW,EAAE;MAAK,CAAC,CAAC,CAAC;MAEjDvD,KAAK,CAACoC,OAAO,CAAC,qCAAqC,CAAC;MACpD,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACd,MAAMtB,OAAO,GAAG,EAAAqB,gBAAA,GAAA9B,KAAK,CAACH,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsBtB,OAAO,KAAI,qBAAqB;MACtEnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;MACpB,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAES;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMuB,kBAAkB,GAAG,MAAO5B,KAAK,IAAK;IAC1C,IAAI;MACF,MAAM/B,KAAK,CAACsC,IAAI,CAAC,+BAA+B,EAAE;QAAEP;MAAM,CAAC,CAAC;MAC5D9B,KAAK,CAACoC,OAAO,CAAC,yBAAyB,CAAC;MACxC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACd,MAAMzB,OAAO,GAAG,EAAAwB,gBAAA,GAAAjC,KAAK,CAACH,QAAQ,cAAAoC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,IAAI,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,kCAAkC;MACnFnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;MACpB,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAES;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMP,MAAM,GAAGA,CAAA,KAAM;IACnBhB,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAAC4C,UAAU,CAAC,OAAO,CAAC;IAChC,OAAO9D,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDrB,KAAK,CAACoC,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;EAED,MAAM0B,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMxC,QAAQ,GAAG,MAAMxB,KAAK,CAACiE,GAAG,CAAC,oBAAoB,EAAED,WAAW,CAAC;MACnEnD,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,IAAI,CAAC;MAChCX,KAAK,CAACoC,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAuC,gBAAA,EAAAC,qBAAA;MACd,MAAM/B,OAAO,GAAG,EAAA8B,gBAAA,GAAAvC,KAAK,CAACH,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxC,IAAI,cAAAyC,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAI,uBAAuB;MACxEnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;MACpB,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAES;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMgC,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAMtE,KAAK,CAACiE,GAAG,CAAC,4BAA4B,EAAE;QAC5CM,gBAAgB,EAAEF,eAAe;QACjCG,YAAY,EAAEF;MAChB,CAAC,CAAC;MACFrE,KAAK,CAACoC,OAAO,CAAC,gCAAgC,CAAC;MAC/C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAA8C,gBAAA,EAAAC,qBAAA;MACd,MAAMtC,OAAO,GAAG,EAAAqC,gBAAA,GAAA9C,KAAK,CAACH,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/C,IAAI,cAAAgD,qBAAA,uBAApBA,qBAAA,CAAsBtC,OAAO,KAAI,wBAAwB;MACzEnC,KAAK,CAAC0B,KAAK,CAACS,OAAO,CAAC;MACpB,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEV,KAAK,EAAES;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMuC,KAAK,GAAG;IACZ/D,IAAI;IACJE,OAAO;IACPE,KAAK;IACLc,KAAK;IACLoB,QAAQ;IACRI,WAAW;IACXK,kBAAkB;IAClB9B,MAAM;IACNkC,aAAa;IACbK,cAAc;IACdQ,eAAe,EAAE,CAAC,CAAChE,IAAI;IACvBiE,UAAU,EAAE,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,WAAW,KAAI,KAAK;IACtCsB,OAAO,EAAE,CAAAlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,MAAK,cAAc,IAAI,CAAAnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,MAAK;EAC3D,CAAC;EAED,oBACE5E,OAAA,CAACC,WAAW,CAAC4E,QAAQ;IAACL,KAAK,EAAEA,KAAM;IAAAjE,QAAA,EAChCA;EAAQ;IAAAuE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzE,GAAA,CAjOWF,YAAY;AAAA4E,EAAA,GAAZ5E,YAAY;AAAA,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}