{"ast": null, "code": "import { transformPropOrder } from './keys-transform.mjs';\nconst positionalKeys = new Set([\"width\", \"height\", \"top\", \"left\", \"right\", \"bottom\", ...transformPropOrder]);\nexport { positionalKeys };", "map": {"version": 3, "names": ["transformPropOrder", "positional<PERSON>eys", "Set"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/motion-dom/dist/es/render/utils/keys-position.mjs"], "sourcesContent": ["import { transformPropOrder } from './keys-transform.mjs';\n\nconst positionalKeys = new Set([\n    \"width\",\n    \"height\",\n    \"top\",\n    \"left\",\n    \"right\",\n    \"bottom\",\n    ...transformPropOrder,\n]);\n\nexport { positionalKeys };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AAEzD,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC3B,OAAO,EACP,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,GAAGF,kBAAkB,CACxB,CAAC;AAEF,SAASC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}