const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Location = sequelize.define('Location', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  address: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  district: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  sector: {
    type: DataTypes.STRING,
    allowNull: true
  },
  cell: {
    type: DataTypes.STRING,
    allowNull: true
  },
  contact_phone: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      is: /^(\+250|250)?[0-9]{9}$/
    }
  },
  contact_email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  type: {
    type: DataTypes.ENUM(
      'health_center', 
      'hospital', 
      'clinic', 
      'salon', 
      'barber_shop', 
      'government_office', 
      'bank', 
      'other'
    ),
    allowNull: false
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: true
  },
  longitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: true
  },
  operating_hours: {
    type: DataTypes.JSON,
    defaultValue: {
      monday: { open: '08:00', close: '17:00', closed: false },
      tuesday: { open: '08:00', close: '17:00', closed: false },
      wednesday: { open: '08:00', close: '17:00', closed: false },
      thursday: { open: '08:00', close: '17:00', closed: false },
      friday: { open: '08:00', close: '17:00', closed: false },
      saturday: { open: '08:00', close: '12:00', closed: false },
      sunday: { open: '08:00', close: '12:00', closed: true }
    }
  },
  max_daily_appointments: {
    type: DataTypes.INTEGER,
    defaultValue: 100,
    validate: {
      min: 1,
      max: 1000
    }
  },
  appointment_duration: {
    type: DataTypes.INTEGER, // in minutes
    defaultValue: 30,
    validate: {
      min: 5,
      max: 480
    }
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  requires_payment: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  payment_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  image_url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  website: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  social_media: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  features: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  admin_user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'locations',
  indexes: [
    {
      fields: ['type']
    },
    {
      fields: ['district']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['admin_user_id']
    }
  ]
});

// Instance methods
Location.prototype.isOpenNow = function() {
  const now = new Date();
  const day = now.toLocaleLowerCase().substring(0, 3); // mon, tue, etc.
  const currentTime = now.toTimeString().substring(0, 5); // HH:MM
  
  const daySchedule = this.operating_hours[day];
  if (!daySchedule || daySchedule.closed) {
    return false;
  }
  
  return currentTime >= daySchedule.open && currentTime <= daySchedule.close;
};

Location.prototype.getAvailableTimeSlots = function(date) {
  // This would calculate available time slots based on existing appointments
  // Implementation would depend on appointment scheduling logic
  return [];
};

module.exports = Location;
