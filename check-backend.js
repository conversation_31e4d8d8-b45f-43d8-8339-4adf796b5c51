const axios = require('axios');

async function checkBackend() {
  console.log('🔍 Checking Umurongo Backend Status...\n');

  const tests = [
    {
      name: 'Health Check',
      url: 'http://localhost:5001/health',
      method: 'GET'
    },
    {
      name: 'Locations API',
      url: 'http://localhost:5001/api/locations',
      method: 'GET'
    },
    {
      name: 'Services API',
      url: 'http://localhost:5001/api/services',
      method: 'GET'
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing ${test.name}...`);
      const response = await axios({
        method: test.method,
        url: test.url,
        timeout: 5000
      });
      
      console.log(`✅ ${test.name}: SUCCESS (${response.status})`);
      if (test.name === 'Health Check') {
        console.log(`   Message: ${response.data.message}`);
      }
      console.log('');
    } catch (error) {
      console.log(`❌ ${test.name}: FAILED`);
      if (error.code === 'ECONNREFUSED') {
        console.log('   Error: Backend server is not running');
        console.log('   Solution: Run "cd server && node server.js"');
      } else if (error.response) {
        console.log(`   Error: ${error.response.status} - ${error.response.statusText}`);
      } else {
        console.log(`   Error: ${error.message}`);
      }
      console.log('');
    }
  }

  console.log('🎯 Quick Start Commands:');
  console.log('Backend: cd server && node server.js');
  console.log('Frontend: cd client && npm start');
  console.log('');
  console.log('📱 Demo Credentials:');
  console.log('Customer: +250788000003 / customer123');
  console.log('Admin: +250788000001 / admin123456');
}

checkBackend().catch(console.error);
