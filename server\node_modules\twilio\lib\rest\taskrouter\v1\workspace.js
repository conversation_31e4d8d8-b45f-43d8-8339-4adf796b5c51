"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspacePage = exports.WorkspaceListInstance = exports.WorkspaceInstance = exports.WorkspaceContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const activity_1 = require("./workspace/activity");
const event_1 = require("./workspace/event");
const task_1 = require("./workspace/task");
const taskChannel_1 = require("./workspace/taskChannel");
const taskQueue_1 = require("./workspace/taskQueue");
const worker_1 = require("./workspace/worker");
const workflow_1 = require("./workspace/workflow");
const workspaceCumulativeStatistics_1 = require("./workspace/workspaceCumulativeStatistics");
const workspaceRealTimeStatistics_1 = require("./workspace/workspaceRealTimeStatistics");
const workspaceStatistics_1 = require("./workspace/workspaceStatistics");
class WorkspaceContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Workspaces/${sid}`;
    }
    get activities() {
        this._activities =
            this._activities ||
                (0, activity_1.ActivityListInstance)(this._version, this._solution.sid);
        return this._activities;
    }
    get events() {
        this._events =
            this._events || (0, event_1.EventListInstance)(this._version, this._solution.sid);
        return this._events;
    }
    get tasks() {
        this._tasks =
            this._tasks || (0, task_1.TaskListInstance)(this._version, this._solution.sid);
        return this._tasks;
    }
    get taskChannels() {
        this._taskChannels =
            this._taskChannels ||
                (0, taskChannel_1.TaskChannelListInstance)(this._version, this._solution.sid);
        return this._taskChannels;
    }
    get taskQueues() {
        this._taskQueues =
            this._taskQueues ||
                (0, taskQueue_1.TaskQueueListInstance)(this._version, this._solution.sid);
        return this._taskQueues;
    }
    get workers() {
        this._workers =
            this._workers || (0, worker_1.WorkerListInstance)(this._version, this._solution.sid);
        return this._workers;
    }
    get workflows() {
        this._workflows =
            this._workflows ||
                (0, workflow_1.WorkflowListInstance)(this._version, this._solution.sid);
        return this._workflows;
    }
    get cumulativeStatistics() {
        this._cumulativeStatistics =
            this._cumulativeStatistics ||
                (0, workspaceCumulativeStatistics_1.WorkspaceCumulativeStatisticsListInstance)(this._version, this._solution.sid);
        return this._cumulativeStatistics;
    }
    get realTimeStatistics() {
        this._realTimeStatistics =
            this._realTimeStatistics ||
                (0, workspaceRealTimeStatistics_1.WorkspaceRealTimeStatisticsListInstance)(this._version, this._solution.sid);
        return this._realTimeStatistics;
    }
    get statistics() {
        this._statistics =
            this._statistics ||
                (0, workspaceStatistics_1.WorkspaceStatisticsListInstance)(this._version, this._solution.sid);
        return this._statistics;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new WorkspaceInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["defaultActivitySid"] !== undefined)
            data["DefaultActivitySid"] = params["defaultActivitySid"];
        if (params["eventCallbackUrl"] !== undefined)
            data["EventCallbackUrl"] = params["eventCallbackUrl"];
        if (params["eventsFilter"] !== undefined)
            data["EventsFilter"] = params["eventsFilter"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["multiTaskEnabled"] !== undefined)
            data["MultiTaskEnabled"] = serialize.bool(params["multiTaskEnabled"]);
        if (params["timeoutActivitySid"] !== undefined)
            data["TimeoutActivitySid"] = params["timeoutActivitySid"];
        if (params["prioritizeQueueOrder"] !== undefined)
            data["PrioritizeQueueOrder"] = params["prioritizeQueueOrder"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkspaceInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkspaceContextImpl = WorkspaceContextImpl;
class WorkspaceInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.defaultActivityName = payload.default_activity_name;
        this.defaultActivitySid = payload.default_activity_sid;
        this.eventCallbackUrl = payload.event_callback_url;
        this.eventsFilter = payload.events_filter;
        this.friendlyName = payload.friendly_name;
        this.multiTaskEnabled = payload.multi_task_enabled;
        this.sid = payload.sid;
        this.timeoutActivityName = payload.timeout_activity_name;
        this.timeoutActivitySid = payload.timeout_activity_sid;
        this.prioritizeQueueOrder = payload.prioritize_queue_order;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new WorkspaceContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a WorkspaceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a WorkspaceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed WorkspaceInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the activities.
     */
    activities() {
        return this._proxy.activities;
    }
    /**
     * Access the events.
     */
    events() {
        return this._proxy.events;
    }
    /**
     * Access the tasks.
     */
    tasks() {
        return this._proxy.tasks;
    }
    /**
     * Access the taskChannels.
     */
    taskChannels() {
        return this._proxy.taskChannels;
    }
    /**
     * Access the taskQueues.
     */
    taskQueues() {
        return this._proxy.taskQueues;
    }
    /**
     * Access the workers.
     */
    workers() {
        return this._proxy.workers;
    }
    /**
     * Access the workflows.
     */
    workflows() {
        return this._proxy.workflows;
    }
    /**
     * Access the cumulativeStatistics.
     */
    cumulativeStatistics() {
        return this._proxy.cumulativeStatistics;
    }
    /**
     * Access the realTimeStatistics.
     */
    realTimeStatistics() {
        return this._proxy.realTimeStatistics;
    }
    /**
     * Access the statistics.
     */
    statistics() {
        return this._proxy.statistics;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            defaultActivityName: this.defaultActivityName,
            defaultActivitySid: this.defaultActivitySid,
            eventCallbackUrl: this.eventCallbackUrl,
            eventsFilter: this.eventsFilter,
            friendlyName: this.friendlyName,
            multiTaskEnabled: this.multiTaskEnabled,
            sid: this.sid,
            timeoutActivityName: this.timeoutActivityName,
            timeoutActivitySid: this.timeoutActivitySid,
            prioritizeQueueOrder: this.prioritizeQueueOrder,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkspaceInstance = WorkspaceInstance;
function WorkspaceListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new WorkspaceContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Workspaces`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["friendlyName"] === null ||
            params["friendlyName"] === undefined) {
            throw new Error("Required parameter \"params['friendlyName']\" missing.");
        }
        let data = {};
        data["FriendlyName"] = params["friendlyName"];
        if (params["eventCallbackUrl"] !== undefined)
            data["EventCallbackUrl"] = params["eventCallbackUrl"];
        if (params["eventsFilter"] !== undefined)
            data["EventsFilter"] = params["eventsFilter"];
        if (params["multiTaskEnabled"] !== undefined)
            data["MultiTaskEnabled"] = serialize.bool(params["multiTaskEnabled"]);
        if (params["template"] !== undefined)
            data["Template"] = params["template"];
        if (params["prioritizeQueueOrder"] !== undefined)
            data["PrioritizeQueueOrder"] = params["prioritizeQueueOrder"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkspaceInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WorkspacePage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new WorkspacePage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.WorkspaceListInstance = WorkspaceListInstance;
class WorkspacePage extends Page_1.default {
    /**
     * Initialize the WorkspacePage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of WorkspaceInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new WorkspaceInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WorkspacePage = WorkspacePage;
