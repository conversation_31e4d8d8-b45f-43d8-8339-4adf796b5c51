# 🔧 Umurongo System Fix Guide

## 🚨 **COMPLETE SYSTEM STARTUP SOLUTION**

### **🎯 One-Command Solution**

Run this single command to start everything:

```bash
node start-system.js
```

This will:
1. ✅ Start backend server
2. ✅ Wait for backend to be ready
3. ✅ Run system health check
4. ✅ Start frontend server
5. ✅ Verify all components are working

### **📋 Manual Step-by-Step Solution**

If the automated script doesn't work, follow these steps:

#### **Step 1: Check System Health**
```bash
node system-check.js
```

#### **Step 2: Start Backend**
```bash
cd server
node server.js
```

**Expected Output:**
```
🗄️  Using SQLite database for development
📱 SMS service running in mock mode
✅ Database connection established successfully.
✅ Database connection ready (sync skipped).
🚀 Umurongo API server running on port 5001
```

#### **Step 3: Verify Backend**
Open browser: `http://localhost:5001/health`

**Expected Response:**
```json
{
  "status": "OK",
  "message": "Umurongo API is running",
  "timestamp": "2024-01-XX...",
  "environment": "development"
}
```

#### **Step 4: Start Frontend**
```bash
cd client
npm start
```

**Expected Output:**
```
webpack compiled successfully
Local: http://localhost:3000
```

#### **Step 5: Test Login**
1. Go to `http://localhost:3000`
2. Click "Sign In"
3. Use demo credentials:
   - Phone: `+250788000003`
   - Password: `customer123`

## 🔍 **Common Issues & Solutions**

### **Issue 1: "Failed to load appointments/locations/services"**

**Cause:** Backend not running or API endpoints not working

**Solution:**
```bash
# Check if backend is running
node system-check.js

# If not running, start backend
cd server
node server.js
```

### **Issue 2: "Cannot connect to server"**

**Cause:** Backend server not started or wrong port

**Solution:**
```bash
# Check what's running on port 5001
netstat -an | findstr :5001

# Kill any process on port 5001 if needed
# Then start backend
cd server
node server.js
```

### **Issue 3: "Authentication failed"**

**Cause:** Token issues or backend auth not working

**Solution:**
```bash
# Clear browser storage
# In browser console:
localStorage.clear();
sessionStorage.clear();
location.reload();

# Then login again with demo credentials
```

### **Issue 4: Frontend won't start**

**Cause:** Dependencies not installed or port conflict

**Solution:**
```bash
cd client
npm install
npm start

# If port 3000 is busy, it will ask to use another port
```

### **Issue 5: Database errors**

**Cause:** SQLite database corruption or missing

**Solution:**
```bash
cd server
# Remove database file
rm umurongo_dev.db
# Restart server (will recreate database)
node server.js
```

## 🛠️ **Advanced Troubleshooting**

### **Complete System Reset**

If nothing works, do a complete reset:

```bash
# 1. Stop all processes (Ctrl+C in terminals)

# 2. Clear all data
cd server
rm umurongo_dev.db

cd ../client
rm -rf node_modules
npm install

# 3. Restart everything
node ../start-system.js
```

### **Check Dependencies**

```bash
# Backend dependencies
cd server
npm list

# Frontend dependencies  
cd client
npm list
```

### **Port Conflicts**

```bash
# Check what's using ports
netstat -an | findstr :3000
netstat -an | findstr :5001

# Kill processes if needed (Windows)
taskkill /F /PID <process_id>
```

## 🐛 **Debug Panel Usage**

1. Login to the app
2. Look for 🐛 button (bottom-right corner)
3. Click "Run Tests"
4. Check results:
   - 🟢 Green = Working
   - 🔴 Red = Problem

## 📊 **System Health Indicators**

### **Backend Healthy:**
- ✅ Port 5001 responding
- ✅ Health endpoint returns 200
- ✅ Database connected
- ✅ API endpoints working

### **Frontend Healthy:**
- ✅ Port 3000 responding
- ✅ No console errors
- ✅ Can login with demo credentials
- ✅ Pages load data

### **Integration Healthy:**
- ✅ Login works
- ✅ Dashboard shows data
- ✅ Locations page shows locations
- ✅ Services page shows services
- ✅ Can book appointments

## 🎯 **Quick Verification Tests**

### **Test 1: Backend API**
```bash
curl http://localhost:5001/health
curl http://localhost:5001/api/locations
```

### **Test 2: Authentication**
```bash
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"+250788000003","password":"customer123"}'
```

### **Test 3: Frontend Pages**
1. Visit `http://localhost:3000`
2. Login with demo credentials
3. Check each page:
   - Dashboard ✅
   - Locations ✅
   - Services ✅
   - Appointments ✅

## 🚀 **Success Checklist**

- [ ] Backend running on port 5001
- [ ] Frontend running on port 3000
- [ ] Health check passes
- [ ] Can login with demo credentials
- [ ] Dashboard shows welcome message
- [ ] Locations page shows 2 locations
- [ ] Services page shows services
- [ ] Can book an appointment
- [ ] Debug panel shows all green

## 📞 **Getting Help**

If you're still having issues:

1. **Run system check:**
   ```bash
   node system-check.js
   ```

2. **Copy the output** and provide it when asking for help

3. **Check browser console** for any error messages

4. **Check terminal outputs** for both backend and frontend

## 🎉 **Expected Final State**

When everything is working:

- **Backend Terminal:** Shows "🚀 Umurongo API server running on port 5001"
- **Frontend Terminal:** Shows "webpack compiled successfully"
- **Browser:** Shows Umurongo app with working login
- **Debug Panel:** All tests show green dots ✅

**Your Umurongo system should now be fully operational! 🎊**
