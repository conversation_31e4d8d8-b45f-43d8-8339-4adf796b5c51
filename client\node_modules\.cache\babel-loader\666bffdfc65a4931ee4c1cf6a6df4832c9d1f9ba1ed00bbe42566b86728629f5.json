{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport addYears from \"../addYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport default function subYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addYears(dirtyDate, -amount);\n}", "map": {"version": 3, "names": ["toInteger", "addYears", "requiredArgs", "subYears", "dirtyDate", "dirtyAmount", "arguments", "amount"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/subYears/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport addYears from \"../addYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport default function subYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addYears(dirtyDate, -amount);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,SAAS,EAAEC,WAAW,EAAE;EACvDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAGP,SAAS,CAACK,WAAW,CAAC;EACnC,OAAOJ,QAAQ,CAACG,SAAS,EAAE,CAACG,MAAM,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}