const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 5002;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  if (req.body && Object.keys(req.body).length > 0) {
    const logBody = { ...req.body };
    if (logBody.password) logBody.password = '***';
    console.log('Request body:', logBody);
  }
  next();
});

// Pre-hashed passwords for testing
const hashPassword = async (password) => {
  return await bcrypt.hash(password, 10);
};

// Test users with pre-hashed passwords
const users = [
  {
    id: 1,
    name: 'System Administrator',
    phone: '+250788000001',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123456
    role: 'system_admin',
    is_verified: true,
    is_active: true
  },
  {
    id: 2,
    name: 'Dr. Jean Baptiste',
    phone: '+250788000002',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123456
    role: 'business_admin',
    is_verified: true,
    is_active: true
  },
  {
    id: 3,
    name: 'Alice Uwimana',
    phone: '+250788000003',
    email: '<EMAIL>',
    password: '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', // customer123
    role: 'customer',
    is_verified: true,
    is_active: true
  }
];

// JWT secret
const JWT_SECRET = 'umurongo_super_secret_jwt_key_2024_development';

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' });
};

// Verify JWT token middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    
    const user = users.find(u => u.id === decoded.userId);
    if (!user) {
      return res.status(403).json({
        success: false,
        message: 'User not found'
      });
    }
    
    req.user = user;
    next();
  });
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Umurongo API is running',
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: 'development'
  });
});

// API health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Umurongo API endpoints are working',
    timestamp: new Date().toISOString()
  });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('=== LOGIN ATTEMPT ===');
    const { phone, password } = req.body;

    // Validate input
    if (!phone || !password) {
      console.log('Missing phone or password');
      return res.status(400).json({
        success: false,
        message: 'Phone number and password are required'
      });
    }

    console.log('Looking for user with phone:', phone);

    // Find user by phone
    const user = users.find(u => u.phone === phone);

    if (!user) {
      console.log('User not found for phone:', phone);
      console.log('Available phones:', users.map(u => u.phone));
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    console.log('User found:', user.name);

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log('Password valid:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('Invalid password for user:', phone);
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    // Check if account is active
    if (!user.is_active) {
      console.log('Account is deactivated for user:', phone);
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Generate token
    const token = generateToken(user.id);

    console.log('Login successful for:', phone);
    console.log('Generated token length:', token.length);

    const response = {
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          email: user.email,
          role: user.role,
          is_verified: user.is_verified
        },
        token
      }
    };

    console.log('Sending response:', { ...response, data: { ...response.data, token: 'TOKEN_HIDDEN' } });

    res.json(response);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// Register endpoint
app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('=== REGISTER ATTEMPT ===');
    const { name, phone, email, password } = req.body;

    // Validate input
    if (!name || !phone || !password) {
      return res.status(400).json({
        success: false,
        message: 'Name, phone number and password are required'
      });
    }

    // Check if user already exists
    const existingUser = users.find(u => u.phone === phone);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this phone number already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: users.length + 1,
      name,
      phone,
      email,
      password: hashedPassword,
      role: 'customer',
      is_verified: true, // Auto-verify for testing
      is_active: true
    };

    users.push(newUser);

    // Generate token
    const token = generateToken(newUser.id);

    console.log('Registration successful for:', phone);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: newUser.id,
          name: newUser.name,
          phone: newUser.phone,
          email: newUser.email,
          role: newUser.role,
          is_verified: newUser.is_verified
        },
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
});

// Profile endpoint
app.get('/api/auth/profile', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: {
      user: {
        id: req.user.id,
        name: req.user.name,
        phone: req.user.phone,
        email: req.user.email,
        role: req.user.role,
        is_verified: req.user.is_verified
      }
    }
  });
});

// Locations endpoint
app.get('/api/locations', (req, res) => {
  res.json({
    success: true,
    data: {
      locations: [
        {
          id: 1,
          name: 'Remera Health Center',
          description: 'Primary healthcare facility serving Remera community',
          address: 'KG 11 Ave, Remera, Gasabo',
          type: 'health_center'
        },
        {
          id: 2,
          name: 'Kigali Beauty Salon',
          description: 'Professional beauty and hair care services',
          address: 'KN 3 Rd, Nyarugenge',
          type: 'salon'
        }
      ]
    }
  });
});

// Appointments endpoint
app.get('/api/appointments', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: {
      appointments: []
    }
  });
});

// Services endpoint
app.get('/api/services', (req, res) => {
  res.json({
    success: true,
    data: {
      services: [
        {
          id: 1,
          name: 'General Consultation',
          description: 'General medical consultation and checkup',
          location_id: 1,
          duration: 30
        },
        {
          id: 2,
          name: 'Hair Cut & Styling',
          description: 'Professional hair cutting and styling',
          location_id: 2,
          duration: 60
        }
      ]
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Umurongo Working API server running on port', PORT);
  console.log('📱 Environment: development');
  console.log('🔗 Health check: http://localhost:' + PORT + '/health');
  console.log('🔗 API health: http://localhost:' + PORT + '/api/health');
  console.log('');
  console.log('📋 Test Credentials:');
  console.log('Customer: +250788000003 / customer123');
  console.log('Admin: +250788000001 / admin123456');
  console.log('Business Admin: +250788000002 / admin123456');
  console.log('');
  console.log('✅ Server ready for login attempts!');
});

module.exports = app;
