{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @param {Date | Number} dateToCompare - the date to compare with\n * @param {Array<Date> | Array<number>} datesArray - the array to search\n * @returns {Date | undefined} the date from the array closest to the given date or undefined if no valid value is given\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport default function closestTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(Number(dateToCompare))) return new Date(NaN);\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray;\n  // `dirtyDatesArray` is undefined or null\n  if (dirtyDatesArray == null) {\n    datesArray = [];\n\n    // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n\n    // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n    if (isNaN(Number(currentDate))) {\n      result = new Date(NaN);\n      minDistance = NaN;\n      return;\n    }\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < Number(minDistance)) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "closestTo", "dirtyDateToCompare", "dirtyDatesArray", "arguments", "dateToCompare", "isNaN", "Number", "Date", "NaN", "timeToCompare", "getTime", "datesArray", "for<PERSON>ach", "Array", "prototype", "slice", "call", "result", "minDistance", "dirtyDate", "currentDate", "distance", "Math", "abs"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/closestTo/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @param {Date | Number} dateToCompare - the date to compare with\n * @param {Array<Date> | Array<number>} datesArray - the array to search\n * @returns {Date | undefined} the date from the array closest to the given date or undefined if no valid value is given\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport default function closestTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(Number(dateToCompare))) return new Date(NaN);\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray;\n  // `dirtyDatesArray` is undefined or null\n  if (dirtyDatesArray == null) {\n    datesArray = [];\n\n    // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n\n    // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n    if (isNaN(Number(currentDate))) {\n      result = new Date(NaN);\n      minDistance = NaN;\n      return;\n    }\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < Number(minDistance)) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,kBAAkB,EAAEC,eAAe,EAAE;EACrEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,aAAa,GAAGN,MAAM,CAACG,kBAAkB,CAAC;EAC9C,IAAII,KAAK,CAACC,MAAM,CAACF,aAAa,CAAC,CAAC,EAAE,OAAO,IAAIG,IAAI,CAACC,GAAG,CAAC;EACtD,IAAIC,aAAa,GAAGL,aAAa,CAACM,OAAO,CAAC,CAAC;EAC3C,IAAIC,UAAU;EACd;EACA,IAAIT,eAAe,IAAI,IAAI,EAAE;IAC3BS,UAAU,GAAG,EAAE;;IAEf;EACF,CAAC,MAAM,IAAI,OAAOT,eAAe,CAACU,OAAO,KAAK,UAAU,EAAE;IACxDD,UAAU,GAAGT,eAAe;;IAE5B;EACF,CAAC,MAAM;IACLS,UAAU,GAAGE,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACd,eAAe,CAAC;EAC1D;EACA,IAAIe,MAAM;EACV,IAAIC,WAAW;EACfP,UAAU,CAACC,OAAO,CAAC,UAAUO,SAAS,EAAE;IACtC,IAAIC,WAAW,GAAGtB,MAAM,CAACqB,SAAS,CAAC;IACnC,IAAId,KAAK,CAACC,MAAM,CAACc,WAAW,CAAC,CAAC,EAAE;MAC9BH,MAAM,GAAG,IAAIV,IAAI,CAACC,GAAG,CAAC;MACtBU,WAAW,GAAGV,GAAG;MACjB;IACF;IACA,IAAIa,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACd,aAAa,GAAGW,WAAW,CAACV,OAAO,CAAC,CAAC,CAAC;IAC9D,IAAIO,MAAM,IAAI,IAAI,IAAII,QAAQ,GAAGf,MAAM,CAACY,WAAW,CAAC,EAAE;MACpDD,MAAM,GAAGG,WAAW;MACpBF,WAAW,GAAGG,QAAQ;IACxB;EACF,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}