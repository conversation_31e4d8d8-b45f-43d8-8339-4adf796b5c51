import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  BuildingOfficeIcon,
  WrenchScrewdriverIcon,
  CalendarDaysIcon,
  UsersIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('locations');
  const [data, setData] = useState({
    locations: [],
    services: [],
    appointments: [],
    users: [],
    queues: []
  });
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedItem, setSelectedItem] = useState(null);

  const tabs = [
    { id: 'locations', name: 'Locations', icon: BuildingOfficeIcon, color: 'blue' },
    { id: 'services', name: 'Services', icon: WrenchScrewdriverIcon, color: 'green' },
    { id: 'appointments', name: 'Appointments', icon: CalendarDaysIcon, color: 'purple' },
    { id: 'users', name: 'Users', icon: UsersIcon, color: 'indigo' },
    { id: 'queues', name: 'Queues', icon: ClockIcon, color: 'orange' }
  ];

  useEffect(() => {
    fetchData(activeTab);
  }, [activeTab]);

  const fetchData = async (type) => {
    setLoading(true);
    try {
      const response = await axios.get(`/api/${type}`);
      setData(prev => ({
        ...prev,
        [type]: response.data.data[type] || response.data.data
      }));
    } catch (error) {
      console.error(`Failed to fetch ${type}:`, error);
      toast.error(`Failed to load ${type}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setModalMode('create');
    setSelectedItem(null);
    setShowModal(true);
  };

  const handleEdit = (item) => {
    setModalMode('edit');
    setSelectedItem(item);
    setShowModal(true);
  };

  const handleView = (item) => {
    setModalMode('view');
    setSelectedItem(item);
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this item?')) return;

    try {
      await axios.delete(`/api/${activeTab}/${id}`);
      toast.success('Item deleted successfully');
      fetchData(activeTab);
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to delete item');
    }
  };

  const handleSave = async (formData) => {
    try {
      if (modalMode === 'create') {
        await axios.post(`/api/${activeTab}`, formData);
        toast.success('Item created successfully');
      } else if (modalMode === 'edit') {
        await axios.put(`/api/${activeTab}/${selectedItem.id}`, formData);
        toast.success('Item updated successfully');
      }
      setShowModal(false);
      fetchData(activeTab);
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to save item');
    }
  };

  const filteredData = data[activeTab]?.filter(item => {
    const searchFields = getSearchFields(activeTab, item);
    return searchFields.some(field =>
      field?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }) || [];

  const getSearchFields = (type, item) => {
    switch (type) {
      case 'locations':
        return [item.name, item.address, item.phone, item.email];
      case 'services':
        return [item.name, item.description, item.category];
      case 'appointments':
        return [item.service_name, item.location_name, item.notes];
      case 'users':
        return [item.first_name, item.last_name, item.email, item.phone];
      case 'queues':
        return [item.service_name, item.location_name];
      default:
        return [];
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage all system components with full CRUD operations</p>
        </div>
        <button
          onClick={handleCreate}
          className="mt-4 sm:mt-0 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          Add New {activeTab.slice(0, -1)}
        </button>
      </motion.div>

      {/* Tabs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="border-b border-gray-200"
      >
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </motion.div>

      {/* Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="relative"
      >
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder={`Search ${activeTab}...`}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
      </motion.div>

      {/* Simple CRUD Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white shadow-soft rounded-xl p-6"
      >
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
          </div>
        ) : (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 capitalize">
              {activeTab} Management ({filteredData.length} items)
            </h3>

            {filteredData.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No {activeTab} found</p>
                <button
                  onClick={handleCreate}
                  className="mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  Create First {activeTab.slice(0, -1)}
                </button>
              </div>
            ) : (
              <div className="grid gap-4">
                {filteredData.map((item) => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">
                          {item.name || `${item.first_name} ${item.last_name}` || item.service_name || `${activeTab.slice(0, -1)} #${item.id}`}
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {activeTab === 'locations' && `${item.address} • ${item.phone}`}
                          {activeTab === 'services' && `${item.category} • ${item.duration}min • ${item.price} RWF`}
                          {activeTab === 'appointments' && `${item.appointment_date} ${item.appointment_time} • ${item.status}`}
                          {activeTab === 'users' && `${item.phone} • ${item.role}`}
                          {activeTab === 'queues' && `Position #${item.position} • ${item.status}`}
                        </p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => handleView(item)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="View"
                        >
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEdit(item)}
                          className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                          title="Edit"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(item.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </motion.div>

      {/* Simple Modal for CRUD operations */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {modalMode === 'create' ? 'Create' : modalMode === 'edit' ? 'Edit' : 'View'} {activeTab.slice(0, -1)}
            </h3>

            <div className="space-y-4">
              {selectedItem && (
                <div className="text-sm text-gray-600">
                  <pre className="whitespace-pre-wrap bg-gray-50 p-3 rounded-lg">
                    {JSON.stringify(selectedItem, null, 2)}
                  </pre>
                </div>
              )}

              {modalMode !== 'view' && (
                <div className="text-center py-4">
                  <p className="text-gray-500">
                    Full form interface will be implemented here for {modalMode} operations.
                  </p>
                  <p className="text-sm text-gray-400 mt-2">
                    For now, you can test the API endpoints directly.
                  </p>
                </div>
              )}
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
              {modalMode !== 'view' && (
                <button
                  onClick={() => {
                    // For demo, just close modal
                    setShowModal(false);
                    toast.success(`${modalMode} operation would be performed here`);
                  }}
                  className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  {modalMode === 'create' ? 'Create' : 'Update'}
                </button>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
