{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseAnyDigitsSigned } from \"../utils.js\";\nexport var TimestampSecondsParser = /*#__PURE__*/function (_Parser) {\n  _inherits(TimestampSecondsParser, _Parser);\n  var _super = _createSuper(TimestampSecondsParser);\n  function TimestampSecondsParser() {\n    var _this;\n    _classCallCheck(this, TimestampSecondsParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 40);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", '*');\n    return _this;\n  }\n  _createClass(TimestampSecondsParser, [{\n    key: \"parse\",\n    value: function parse(dateString) {\n      return parseAnyDigitsSigned(dateString);\n    }\n  }, {\n    key: \"set\",\n    value: function set(_date, _flags, value) {\n      return [new Date(value * 1000), {\n        timestampIsSet: true\n      }];\n    }\n  }]);\n  return TimestampSecondsParser;\n}(Parser);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "<PERSON><PERSON><PERSON>", "parseAnyDigitsSigned", "TimestampSecondsParser", "_<PERSON><PERSON>r", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "key", "value", "parse", "dateString", "set", "_date", "_flags", "Date", "timestampIsSet"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/parse/_lib/parsers/TimestampSecondsParser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseAnyDigitsSigned } from \"../utils.js\";\nexport var TimestampSecondsParser = /*#__PURE__*/function (_Parser) {\n  _inherits(TimestampSecondsParser, _Parser);\n  var _super = _createSuper(TimestampSecondsParser);\n  function TimestampSecondsParser() {\n    var _this;\n    _classCallCheck(this, TimestampSecondsParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 40);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", '*');\n    return _this;\n  }\n  _createClass(TimestampSecondsParser, [{\n    key: \"parse\",\n    value: function parse(dateString) {\n      return parseAnyDigitsSigned(dateString);\n    }\n  }, {\n    key: \"set\",\n    value: function set(_date, _flags, value) {\n      return [new Date(value * 1000), {\n        timestampIsSet: true\n      }];\n    }\n  }]);\n  return TimestampSecondsParser;\n}(Parser);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,oBAAoB,QAAQ,aAAa;AAClD,OAAO,IAAIC,sBAAsB,GAAG,aAAa,UAAUC,OAAO,EAAE;EAClEN,SAAS,CAACK,sBAAsB,EAAEC,OAAO,CAAC;EAC1C,IAAIC,MAAM,GAAGN,YAAY,CAACI,sBAAsB,CAAC;EACjD,SAASA,sBAAsBA,CAAA,EAAG;IAChC,IAAIG,KAAK;IACTX,eAAe,CAAC,IAAI,EAAEQ,sBAAsB,CAAC;IAC7C,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDV,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,UAAU,EAAE,EAAE,CAAC;IAC9DN,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,oBAAoB,EAAE,GAAG,CAAC;IACzE,OAAOA,KAAK;EACd;EACAV,YAAY,CAACO,sBAAsB,EAAE,CAAC;IACpCa,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,UAAU,EAAE;MAChC,OAAOjB,oBAAoB,CAACiB,UAAU,CAAC;IACzC;EACF,CAAC,EAAE;IACDH,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASG,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEL,KAAK,EAAE;MACxC,OAAO,CAAC,IAAIM,IAAI,CAACN,KAAK,GAAG,IAAI,CAAC,EAAE;QAC9BO,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;EACH,OAAOrB,sBAAsB;AAC/B,CAAC,CAACF,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}