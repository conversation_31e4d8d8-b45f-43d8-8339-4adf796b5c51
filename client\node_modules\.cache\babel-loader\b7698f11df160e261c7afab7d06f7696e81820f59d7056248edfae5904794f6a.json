{"ast": null, "code": "import { supportsScrollTimeline } from 'motion-dom';\nimport { scrollInfo } from '../track.mjs';\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n  const currentTime = {\n    value: 0\n  };\n  const cancel = scrollInfo(info => {\n    currentTime.value = info[options.axis].progress * 100;\n  }, options);\n  return {\n    currentTime,\n    cancel\n  };\n}\nfunction getTimeline({\n  source,\n  container,\n  ...options\n}) {\n  const {\n    axis\n  } = options;\n  if (source) container = source;\n  const containerCache = timelineCache.get(container) ?? new Map();\n  timelineCache.set(container, containerCache);\n  const targetKey = options.target ?? \"self\";\n  const targetCache = containerCache.get(targetKey) ?? {};\n  const axisKey = axis + (options.offset ?? []).join(\",\");\n  if (!targetCache[axisKey]) {\n    targetCache[axisKey] = !options.target && supportsScrollTimeline() ? new ScrollTimeline({\n      source: container,\n      axis\n    }) : scrollTimelineFallback({\n      container,\n      ...options\n    });\n  }\n  return targetCache[axisKey];\n}\nexport { getTimeline };", "map": {"version": 3, "names": ["supportsScrollTimeline", "scrollInfo", "timelineCache", "Map", "scrollTimelineFallback", "options", "currentTime", "value", "cancel", "info", "axis", "progress", "getTimeline", "source", "container", "containerCache", "get", "set", "<PERSON><PERSON><PERSON>", "target", "targetCache", "axisKey", "offset", "join", "ScrollTimeline"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs"], "sourcesContent": ["import { supportsScrollTimeline } from 'motion-dom';\nimport { scrollInfo } from '../track.mjs';\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = scrollInfo((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && supportsScrollTimeline()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\nexport { getTimeline };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,YAAY;AACnD,SAASC,UAAU,QAAQ,cAAc;AAEzC,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EACrC,MAAMC,WAAW,GAAG;IAAEC,KAAK,EAAE;EAAE,CAAC;EAChC,MAAMC,MAAM,GAAGP,UAAU,CAAEQ,IAAI,IAAK;IAChCH,WAAW,CAACC,KAAK,GAAGE,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,CAACC,QAAQ,GAAG,GAAG;EACzD,CAAC,EAAEN,OAAO,CAAC;EACX,OAAO;IAAEC,WAAW;IAAEE;EAAO,CAAC;AAClC;AACA,SAASI,WAAWA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAE,GAAGT;AAAQ,CAAC,EAAE;EACpD,MAAM;IAAEK;EAAK,CAAC,GAAGL,OAAO;EACxB,IAAIQ,MAAM,EACNC,SAAS,GAAGD,MAAM;EACtB,MAAME,cAAc,GAAGb,aAAa,CAACc,GAAG,CAACF,SAAS,CAAC,IAAI,IAAIX,GAAG,CAAC,CAAC;EAChED,aAAa,CAACe,GAAG,CAACH,SAAS,EAAEC,cAAc,CAAC;EAC5C,MAAMG,SAAS,GAAGb,OAAO,CAACc,MAAM,IAAI,MAAM;EAC1C,MAAMC,WAAW,GAAGL,cAAc,CAACC,GAAG,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;EACvD,MAAMG,OAAO,GAAGX,IAAI,GAAG,CAACL,OAAO,CAACiB,MAAM,IAAI,EAAE,EAAEC,IAAI,CAAC,GAAG,CAAC;EACvD,IAAI,CAACH,WAAW,CAACC,OAAO,CAAC,EAAE;IACvBD,WAAW,CAACC,OAAO,CAAC,GAChB,CAAChB,OAAO,CAACc,MAAM,IAAInB,sBAAsB,CAAC,CAAC,GACrC,IAAIwB,cAAc,CAAC;MAAEX,MAAM,EAAEC,SAAS;MAAEJ;IAAK,CAAC,CAAC,GAC/CN,sBAAsB,CAAC;MAAEU,SAAS;MAAE,GAAGT;IAAQ,CAAC,CAAC;EAC/D;EACA,OAAOe,WAAW,CAACC,OAAO,CAAC;AAC/B;AAEA,SAAST,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}