{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachDayOfInterval\n * @category Interval Helpers\n * @summary Return the array of dates within the specified time interval.\n *\n * @description\n * Return the array of dates within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The value should be more than 1.\n * @returns {Date[]} the array with starts of days from the day of the interval start to the day of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.step` must be a number greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each day between 6 October 2014 and 10 October 2014:\n * const result = eachDayOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 9, 10)\n * })\n * //=> [\n * //   Mon Oct 06 2014 00:00:00,\n * //   Tue Oct 07 2014 00:00:00,\n * //   Wed Oct 08 2014 00:00:00,\n * //   Thu Oct 09 2014 00:00:00,\n * //   Fri Oct 10 2014 00:00:00\n * // ]\n */\nexport default function eachDayOfInterval(dirtyInterval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setDate(currentDate.getDate() + step);\n    currentDate.setHours(0, 0, 0, 0);\n  }\n  return dates;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "eachDayOfInterval", "dirtyInterval", "options", "_options$step", "arguments", "interval", "startDate", "start", "endDate", "end", "endTime", "getTime", "RangeError", "dates", "currentDate", "setHours", "step", "Number", "isNaN", "push", "setDate", "getDate"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/eachDayOfInterval/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachDayOfInterval\n * @category Interval Helpers\n * @summary Return the array of dates within the specified time interval.\n *\n * @description\n * Return the array of dates within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The value should be more than 1.\n * @returns {Date[]} the array with starts of days from the day of the interval start to the day of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.step` must be a number greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each day between 6 October 2014 and 10 October 2014:\n * const result = eachDayOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 9, 10)\n * })\n * //=> [\n * //   Mon Oct 06 2014 00:00:00,\n * //   Tue Oct 07 2014 00:00:00,\n * //   Wed Oct 08 2014 00:00:00,\n * //   Thu Oct 09 2014 00:00:00,\n * //   Fri Oct 10 2014 00:00:00\n * // ]\n */\nexport default function eachDayOfInterval(dirtyInterval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setDate(currentDate.getDate() + step);\n    currentDate.setHours(0, 0, 0, 0);\n  }\n  return dates;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAChE,IAAIC,aAAa;EACjBJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGJ,aAAa,IAAI,CAAC,CAAC;EAClC,IAAIK,SAAS,GAAGR,MAAM,CAACO,QAAQ,CAACE,KAAK,CAAC;EACtC,IAAIC,OAAO,GAAGV,MAAM,CAACO,QAAQ,CAACI,GAAG,CAAC;EAClC,IAAIC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAAC,CAAC;;EAE/B;EACA,IAAI,EAAEL,SAAS,CAACK,OAAO,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;IACrC,MAAM,IAAIE,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,WAAW,GAAGR,SAAS;EAC3BQ,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChC,IAAIC,IAAI,GAAGC,MAAM,CAAC,CAACd,aAAa,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,IAAI,MAAM,IAAI,IAAIb,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC;EAC5J,IAAIa,IAAI,GAAG,CAAC,IAAIE,KAAK,CAACF,IAAI,CAAC,EAAE,MAAM,IAAIJ,UAAU,CAAC,gDAAgD,CAAC;EACnG,OAAOE,WAAW,CAACH,OAAO,CAAC,CAAC,IAAID,OAAO,EAAE;IACvCG,KAAK,CAACM,IAAI,CAACrB,MAAM,CAACgB,WAAW,CAAC,CAAC;IAC/BA,WAAW,CAACM,OAAO,CAACN,WAAW,CAACO,OAAO,CAAC,CAAC,GAAGL,IAAI,CAAC;IACjDF,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC;EACA,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}