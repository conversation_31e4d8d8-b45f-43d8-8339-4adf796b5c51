{"name": "umurongo", "version": "1.0.0", "description": "Queue Management System for Rwanda", "main": "index.js", "scripts": {"start": "node start-system.js", "check": "node system-check.js", "backend": "cd server && node server.js", "frontend": "cd client && npm start", "install-all": "cd server && npm install && cd ../client && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["queue", "management", "rwanda", "appointments"], "author": "Umurongo Team", "license": "ISC", "devDependencies": {"axios": "^1.6.2"}}