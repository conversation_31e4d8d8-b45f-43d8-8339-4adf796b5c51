const express = require('express');
const { Op, sequelize } = require('sequelize');
const { Location, Service, User, Appointment } = require('../models');
const { authenticateToken, requireRole, requireLocationAdmin, optionalAuth } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

const router = express.Router();

// Get all locations (public endpoint with optional auth)
router.get('/', optionalAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      district,
      search,
      is_active = true
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Only show active locations to non-admin users
    if (req.user?.role !== 'system_admin') {
      whereClause.is_active = true;
    } else if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true';
    }

    if (type) {
      whereClause.type = type;
    }

    if (district) {
      whereClause.district = district;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { address: { [Op.like]: `%${search}%` } },
        { district: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: locations } = await Location.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Service,
          as: 'services',
          where: { is_active: true },
          required: false
        },
        {
          model: User,
          as: 'admin',
          attributes: ['id', 'name', 'phone']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['name', 'ASC']]
    });

    res.json({
      success: true,
      data: {
        locations,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get locations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch locations'
    });
  }
});

// Get location by ID (public)
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const whereClause = { id };

    // Only show active locations to non-admin users
    if (req.user?.role !== 'system_admin') {
      whereClause.is_active = true;
    }

    const location = await Location.findOne({
      where: whereClause,
      include: [
        {
          model: Service,
          as: 'services',
          where: req.user?.role === 'system_admin' ? {} : { is_active: true },
          required: false
        },
        {
          model: User,
          as: 'admin',
          attributes: ['id', 'name', 'phone', 'email']
        }
      ]
    });

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found'
      });
    }

    res.json({
      success: true,
      data: { location }
    });
  } catch (error) {
    console.error('Get location error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch location'
    });
  }
});

// Create new location (business admin or system admin)
router.post('/',
  authenticateToken,
  requireRole('business_admin', 'system_admin'),
  validate(schemas.createLocation),
  async (req, res) => {
    try {
      const locationData = {
        ...req.body,
        admin_user_id: req.user.role === 'system_admin' ? req.body.admin_user_id || req.user.id : req.user.id
      };

      const location = await Location.create(locationData);

      // Include admin details in response
      const locationWithAdmin = await Location.findByPk(location.id, {
        include: [
          {
            model: User,
            as: 'admin',
            attributes: ['id', 'name', 'phone', 'email']
          }
        ]
      });

      res.status(201).json({
        success: true,
        message: 'Location created successfully',
        data: { location: locationWithAdmin }
      });
    } catch (error) {
      console.error('Create location error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create location',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
);

// Update location
router.put('/:id',
  authenticateToken,
  requireLocationAdmin,
  async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Remove fields that shouldn't be updated directly
      delete updateData.id;
      delete updateData.admin_user_id;

      const location = await Location.findByPk(id);
      if (!location) {
        return res.status(404).json({
          success: false,
          message: 'Location not found'
        });
      }

      await location.update(updateData);

      // Return updated location with admin details
      const updatedLocation = await Location.findByPk(id, {
        include: [
          {
            model: User,
            as: 'admin',
            attributes: ['id', 'name', 'phone', 'email']
          },
          {
            model: Service,
            as: 'services'
          }
        ]
      });

      res.json({
        success: true,
        message: 'Location updated successfully',
        data: { location: updatedLocation }
      });
    } catch (error) {
      console.error('Update location error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update location'
      });
    }
  }
);

// Get location statistics
router.get('/:id/stats', authenticateToken, requireLocationAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { start_date, end_date } = req.query;

    const dateFilter = {};
    if (start_date) dateFilter[Op.gte] = new Date(start_date);
    if (end_date) dateFilter[Op.lte] = new Date(end_date);

    const whereClause = { location_id: id };
    if (Object.keys(dateFilter).length > 0) {
      whereClause.appointment_date = dateFilter;
    }

    // Get appointment statistics
    const totalAppointments = await Appointment.count({
      where: whereClause
    });

    const appointmentsByStatus = await Appointment.findAll({
      where: whereClause,
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    const serviceStats = await Appointment.findAll({
      where: whereClause,
      include: [
        {
          model: Service,
          as: 'service',
          attributes: ['name']
        }
      ],
      attributes: [
        'service_id',
        [sequelize.fn('COUNT', sequelize.col('Appointment.id')), 'count']
      ],
      group: ['service_id', 'service.name'],
      raw: true
    });

    res.json({
      success: true,
      data: {
        total_appointments: totalAppointments,
        appointments_by_status: appointmentsByStatus,
        service_statistics: serviceStats
      }
    });
  } catch (error) {
    console.error('Get location stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch location statistics'
    });
  }
});

// Deactivate location (admin only)
router.put('/:id/deactivate',
  authenticateToken,
  requireLocationAdmin,
  async (req, res) => {
    try {
      const { id } = req.params;

      const location = await Location.findByPk(id);
      if (!location) {
        return res.status(404).json({
          success: false,
          message: 'Location not found'
        });
      }

      await location.update({ is_active: false });

      res.json({
        success: true,
        message: 'Location deactivated successfully'
      });
    } catch (error) {
      console.error('Deactivate location error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to deactivate location'
      });
    }
  }
);

// Activate location (admin only)
router.put('/:id/activate',
  authenticateToken,
  requireLocationAdmin,
  async (req, res) => {
    try {
      const { id } = req.params;

      const location = await Location.findByPk(id);
      if (!location) {
        return res.status(404).json({
          success: false,
          message: 'Location not found'
        });
      }

      await location.update({ is_active: true });

      res.json({
        success: true,
        message: 'Location activated successfully'
      });
    } catch (error) {
      console.error('Activate location error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to activate location'
      });
    }
  }
);

// Delete location (system admin only)
router.delete('/:id',
  authenticateToken,
  requireRole('system_admin'),
  async (req, res) => {
    try {
      const { id } = req.params;

      const location = await Location.findByPk(id);
      if (!location) {
        return res.status(404).json({
          success: false,
          message: 'Location not found'
        });
      }

      // Check for active appointments
      const activeAppointments = await Appointment.count({
        where: {
          location_id: id,
          status: ['scheduled', 'confirmed', 'in_progress']
        }
      });

      if (activeAppointments > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete location with active appointments'
        });
      }

      await location.destroy();

      res.json({
        success: true,
        message: 'Location deleted successfully'
      });
    } catch (error) {
      console.error('Delete location error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete location'
      });
    }
  }
);

module.exports = router;
