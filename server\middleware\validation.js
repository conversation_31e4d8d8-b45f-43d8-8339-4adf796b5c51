const Joi = require('joi');

// Validation middleware factory
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }
    
    next();
  };
};

// Common validation schemas
const schemas = {
  // User registration
  register: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    phone: Joi.string().pattern(/^(\+250|250)?[0-9]{9}$/).required(),
    email: Joi.string().email().optional(),
    password: Joi.string().min(6).max(255).required(),
    role: Joi.string().valid('customer', 'business_admin').default('customer'),
    language: Joi.string().valid('en', 'rw', 'fr').default('en')
  }),

  // User login
  login: Joi.object({
    phone: Joi.string().pattern(/^(\+250|250)?[0-9]{9}$/).required(),
    password: Joi.string().required()
  }),

  // Phone verification
  verifyPhone: Joi.object({
    phone: Joi.string().pattern(/^(\+250|250)?[0-9]{9}$/).required(),
    code: Joi.string().length(6).required()
  }),

  // Location creation
  createLocation: Joi.object({
    name: Joi.string().min(2).max(200).required(),
    description: Joi.string().optional(),
    address: Joi.string().required(),
    district: Joi.string().required(),
    sector: Joi.string().optional(),
    cell: Joi.string().optional(),
    contact_phone: Joi.string().pattern(/^(\+250|250)?[0-9]{9}$/).required(),
    contact_email: Joi.string().email().optional(),
    type: Joi.string().valid(
      'health_center', 'hospital', 'clinic', 'salon', 
      'barber_shop', 'government_office', 'bank', 'other'
    ).required(),
    latitude: Joi.number().min(-90).max(90).optional(),
    longitude: Joi.number().min(-180).max(180).optional(),
    max_daily_appointments: Joi.number().min(1).max(1000).default(100),
    appointment_duration: Joi.number().min(5).max(480).default(30),
    requires_payment: Joi.boolean().default(false),
    payment_amount: Joi.number().min(0).optional(),
    website: Joi.string().uri().optional()
  }),

  // Service creation
  createService: Joi.object({
    name: Joi.string().min(2).max(200).required(),
    description: Joi.string().optional(),
    category: Joi.string().valid(
      'consultation', 'vaccination', 'checkup', 'emergency', 'dental',
      'eye_care', 'maternity', 'pediatric', 'surgery', 'laboratory',
      'pharmacy', 'haircut', 'styling', 'manicure', 'pedicure',
      'massage', 'government_service', 'banking', 'other'
    ).required(),
    duration: Joi.number().min(5).max(480).default(30),
    price: Joi.number().min(0).optional(),
    requires_payment: Joi.boolean().default(false),
    max_daily_slots: Joi.number().min(1).max(500).default(50),
    advance_booking_days: Joi.number().min(0).max(365).default(7),
    location_id: Joi.string().uuid().required()
  }),

  // Appointment booking
  createAppointment: Joi.object({
    service_id: Joi.string().uuid().required(),
    appointment_date: Joi.date().min('now').required(),
    appointment_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
    notes: Joi.string().max(500).optional(),
    patient_notes: Joi.string().max(500).optional()
  }),

  // Update appointment status
  updateAppointmentStatus: Joi.object({
    status: Joi.string().valid(
      'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show', 'rescheduled'
    ).required(),
    notes: Joi.string().max(500).optional(),
    room_number: Joi.string().max(20).optional()
  }),

  // Queue management
  updateQueueStatus: Joi.object({
    status: Joi.string().valid(
      'called', 'in_service', 'completed', 'skipped', 'no_show'
    ).required(),
    room_number: Joi.string().max(20).optional(),
    notes: Joi.string().max(500).optional()
  }),

  // Payment
  createPayment: Joi.object({
    appointment_id: Joi.string().uuid().required(),
    amount: Joi.number().min(0).required(),
    payment_method: Joi.string().valid('mobile_money', 'card', 'cash', 'bank_transfer').required(),
    phone_number: Joi.string().pattern(/^(\+250|250)?[0-9]{9}$/).when('payment_method', {
      is: 'mobile_money',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
  }),

  // Feedback
  submitFeedback: Joi.object({
    appointment_id: Joi.string().uuid().required(),
    rating: Joi.number().min(1).max(5).required(),
    comment: Joi.string().max(1000).optional()
  })
};

module.exports = {
  validate,
  schemas
};
