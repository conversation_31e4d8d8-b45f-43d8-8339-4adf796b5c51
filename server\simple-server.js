const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Sample data
const locations = [
  {
    id: 1,
    name: "Remera Health Center",
    address: "KN 3 Rd, Kigali",
    phone: "+250788123456",
    email: "<EMAIL>",
    operating_hours: "08:00-17:00",
    rating: 4.5,
    status: "active"
  },
  {
    id: 2,
    name: "Kigali Beauty Salon",
    address: "KG 11 Ave, Kigali",
    phone: "+250788654321",
    email: "<EMAIL>",
    operating_hours: "09:00-18:00",
    rating: 4.2,
    status: "active"
  }
];

const services = [
  {
    id: 1,
    name: "General Consultation",
    description: "Basic health checkup and consultation",
    duration: 30,
    price: 5000,
    location_id: 1,
    category: "Healthcare",
    status: "active"
  },
  {
    id: 2,
    name: "Hair Cut & Styling",
    description: "Professional hair cutting and styling service",
    duration: 60,
    price: 8000,
    location_id: 2,
    category: "Beauty",
    status: "active"
  },
  {
    id: 3,
    name: "Dental Checkup",
    description: "Comprehensive dental examination",
    duration: 45,
    price: 7500,
    location_id: 1,
    category: "Healthcare",
    status: "active"
  }
];

const users = [
  {
    id: 1,
    phone: "+250788000003",
    password: "customer123", // In real app, this would be hashed
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    role: "customer"
  }
];

let appointments = [
  {
    id: 1,
    user_id: 1,
    service_id: 1,
    location_id: 1,
    appointment_date: "2024-01-15",
    appointment_time: "10:00",
    status: "confirmed",
    notes: "Regular checkup",
    service_name: "General Consultation",
    location_name: "Remera Health Center",
    created_at: "2024-01-10T08:00:00Z"
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Umurongo API is running',
    timestamp: new Date().toISOString(),
    environment: 'development'
  });
});

app.get('/api/locations', (req, res) => {
  res.json({
    success: true,
    data: { locations }
  });
});

app.get('/api/services', (req, res) => {
  let filteredServices = services;
  
  if (req.query.location_id) {
    filteredServices = services.filter(s => s.location_id == req.query.location_id);
  }
  
  res.json({
    success: true,
    data: { services: filteredServices }
  });
});

app.post('/api/auth/login', (req, res) => {
  const { phone, password } = req.body;
  
  const user = users.find(u => u.phone === phone && u.password === password);
  
  if (user) {
    res.json({
      success: true,
      data: {
        token: 'fake-jwt-token-for-demo',
        user: {
          id: user.id,
          phone: user.phone,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          role: user.role
        }
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

app.get('/api/appointments', (req, res) => {
  // In a real app, filter by authenticated user
  res.json({
    success: true,
    data: { appointments }
  });
});

app.post('/api/appointments', (req, res) => {
  const { service_id, appointment_date, appointment_time, notes } = req.body;
  
  const service = services.find(s => s.id == service_id);
  const location = locations.find(l => l.id == service.location_id);
  
  const newAppointment = {
    id: appointments.length + 1,
    user_id: 1, // In real app, get from auth token
    service_id: parseInt(service_id),
    location_id: service.location_id,
    appointment_date,
    appointment_time,
    status: 'confirmed',
    notes: notes || '',
    service_name: service.name,
    location_name: location.name,
    created_at: new Date().toISOString()
  };
  
  appointments.push(newAppointment);
  
  res.json({
    success: true,
    data: { appointment: newAppointment }
  });
});

app.put('/api/appointments/:id/cancel', (req, res) => {
  const appointmentId = parseInt(req.params.id);
  const appointmentIndex = appointments.findIndex(a => a.id === appointmentId);
  
  if (appointmentIndex !== -1) {
    appointments[appointmentIndex].status = 'cancelled';
    res.json({
      success: true,
      data: { appointment: appointments[appointmentIndex] }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'Appointment not found'
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  });
});

const PORT = 5001;
app.listen(PORT, () => {
  console.log('🗄️  Using SQLite database for development');
  console.log('📱 SMS service running in mock mode');
  console.log('✅ Database connection established successfully.');
  console.log('✅ Database connection ready (sync skipped).');
  console.log(`🚀 Umurongo API server running on port ${PORT}`);
  console.log(`📱 Environment: development`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
