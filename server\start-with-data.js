const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 5002;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory users for testing (simulating database)
const users = [
  {
    id: 1,
    name: 'System Administrator',
    phone: '+250788000001',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123456
    role: 'system_admin',
    is_verified: true,
    is_active: true
  },
  {
    id: 2,
    name: 'Dr. <PERSON>',
    phone: '+250788000002',
    email: '<EMAIL>',
    password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // admin123456
    role: 'business_admin',
    is_verified: true,
    is_active: true
  },
  {
    id: 3,
    name: '<PERSON>',
    phone: '+250788000003',
    email: '<EMAIL>',
    password: '$2a$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', // customer123
    role: 'customer',
    is_verified: true,
    is_active: true
  }
];

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    'umurongo_super_secret_jwt_key_2024_development',
    { expiresIn: '7d' }
  );
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Umurongo API is running',
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: 'development'
  });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { phone, password } = req.body;

    console.log('Login attempt:', { phone, password: '***' });

    // Find user by phone
    const user = users.find(u => u.phone === phone);

    if (!user) {
      console.log('User not found for phone:', phone);
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('Invalid password for user:', phone);
      return res.status(401).json({
        success: false,
        message: 'Invalid phone number or password'
      });
    }

    // Check if account is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated'
      });
    }

    // Generate token
    const token = generateToken(user.id);

    console.log('Login successful for:', phone);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          email: user.email,
          role: user.role,
          is_verified: user.is_verified
        },
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: error.message
    });
  }
});

// Register endpoint
app.post('/api/auth/register', async (req, res) => {
  try {
    const { name, phone, email, password } = req.body;

    // Check if user already exists
    const existingUser = users.find(u => u.phone === phone);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this phone number already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = {
      id: users.length + 1,
      name,
      phone,
      email,
      password: hashedPassword,
      role: 'customer',
      is_verified: true, // Auto-verify for testing
      is_active: true
    };

    users.push(newUser);

    // Generate token
    const token = generateToken(newUser.id);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: newUser.id,
          name: newUser.name,
          phone: newUser.phone,
          email: newUser.email,
          role: newUser.role,
          is_verified: newUser.is_verified
        },
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
});

// Profile endpoint
app.get('/api/auth/profile', (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }

  const token = authHeader.split(' ')[1];
  try {
    const decoded = jwt.verify(token, 'umurongo_super_secret_jwt_key_2024_development');
    const user = users.find(u => u.id === decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          email: user.email,
          role: user.role,
          is_verified: user.is_verified
        }
      }
    });
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
});

// Locations endpoint
app.get('/api/locations', (req, res) => {
  res.json({
    success: true,
    data: {
      locations: [
        {
          id: 1,
          name: 'Remera Health Center',
          description: 'Primary healthcare facility serving Remera community',
          address: 'KG 11 Ave, Remera, Gasabo',
          type: 'health_center'
        },
        {
          id: 2,
          name: 'Kigali Beauty Salon',
          description: 'Professional beauty and hair care services',
          address: 'KN 3 Rd, Nyarugenge',
          type: 'salon'
        }
      ]
    }
  });
});

// Appointments endpoint
app.get('/api/appointments', (req, res) => {
  res.json({
    success: true,
    data: {
      appointments: []
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Umurongo Test API server running on port', PORT);
  console.log('📱 Environment: development');
  console.log('🔗 Health check: http://localhost:' + PORT + '/health');
  console.log('');
  console.log('📋 Test Credentials:');
  console.log('Customer: +250788000003 / customer123');
  console.log('Admin: +250788000001 / admin123456');
  console.log('Business Admin: +250788000002 / admin123456');
});

module.exports = app;
