{"name": "client", "version": "0.1.0", "proxy": "http://localhost:5002", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "framer-motion": "^12.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.28.0", "react-scripts": "5.0.1", "typewriter-effect": "^2.22.0", "web-vitals": "^2.1.4", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}