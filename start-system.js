const { spawn } = require('child_process');
const axios = require('axios');
const path = require('path');

console.log('🚀 Starting Umurongo System...\n');

let backendProcess = null;
let frontendProcess = null;

// Function to wait for backend to be ready
async function waitForBackend(maxAttempts = 30) {
  console.log('⏳ Waiting for backend to start...');
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await axios.get('http://localhost:5001/health', { timeout: 2000 });
      if (response.status === 200) {
        console.log('✅ Backend is ready!');
        return true;
      }
    } catch (error) {
      // Backend not ready yet, wait and try again
      await new Promise(resolve => setTimeout(resolve, 1000));
      process.stdout.write('.');
    }
  }
  
  console.log('\n❌ Backend failed to start within timeout');
  return false;
}

// Function to start backend
function startBackend() {
  return new Promise((resolve, reject) => {
    console.log('🔧 Starting backend server...');
    
    const backend = spawn('node', ['server.js'], {
      cwd: path.join(__dirname, 'server'),
      stdio: ['pipe', 'pipe', 'pipe']
    });

    backend.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[Backend] ${output.trim()}`);
      
      // Check if backend is ready
      if (output.includes('Umurongo API server running on port 5001')) {
        resolve(backend);
      }
    });

    backend.stderr.on('data', (data) => {
      console.error(`[Backend Error] ${data.toString().trim()}`);
    });

    backend.on('error', (error) => {
      console.error('❌ Failed to start backend:', error.message);
      reject(error);
    });

    backend.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!backend.killed) {
        console.log('⏰ Backend startup timeout, but continuing...');
        resolve(backend);
      }
    }, 30000);
  });
}

// Function to start frontend
function startFrontend() {
  return new Promise((resolve, reject) => {
    console.log('🎨 Starting frontend server...');
    
    const frontend = spawn('npm', ['start'], {
      cwd: path.join(__dirname, 'client'),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    frontend.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[Frontend] ${output.trim()}`);
      
      // Check if frontend is ready
      if (output.includes('webpack compiled') || output.includes('Local:')) {
        resolve(frontend);
      }
    });

    frontend.stderr.on('data', (data) => {
      const output = data.toString();
      // Don't log webpack warnings as errors
      if (!output.includes('WARNING') && !output.includes('webpack')) {
        console.error(`[Frontend Error] ${output.trim()}`);
      }
    });

    frontend.on('error', (error) => {
      console.error('❌ Failed to start frontend:', error.message);
      reject(error);
    });

    frontend.on('close', (code) => {
      console.log(`Frontend process exited with code ${code}`);
    });

    // Timeout after 60 seconds
    setTimeout(() => {
      if (!frontend.killed) {
        console.log('⏰ Frontend startup timeout, but continuing...');
        resolve(frontend);
      }
    }, 60000);
  });
}

// Function to run system check
async function runSystemCheck() {
  console.log('\n🔍 Running system health check...');
  
  try {
    const { spawn } = require('child_process');
    const checker = spawn('node', ['system-check.js'], {
      stdio: 'inherit'
    });
    
    return new Promise((resolve) => {
      checker.on('close', (code) => {
        resolve(code === 0);
      });
    });
  } catch (error) {
    console.error('❌ System check failed:', error.message);
    return false;
  }
}

// Cleanup function
function cleanup() {
  console.log('\n🛑 Shutting down system...');
  
  if (backendProcess && !backendProcess.killed) {
    console.log('Stopping backend...');
    backendProcess.kill('SIGTERM');
  }
  
  if (frontendProcess && !frontendProcess.killed) {
    console.log('Stopping frontend...');
    frontendProcess.kill('SIGTERM');
  }
  
  process.exit(0);
}

// Handle cleanup on exit
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('exit', cleanup);

// Main startup sequence
async function startSystem() {
  try {
    // 1. Start backend
    backendProcess = await startBackend();
    
    // 2. Wait for backend to be ready
    const backendReady = await waitForBackend();
    
    if (!backendReady) {
      console.log('❌ Backend failed to start properly');
      return;
    }
    
    // 3. Run system check
    await runSystemCheck();
    
    // 4. Start frontend
    frontendProcess = await startFrontend();
    
    console.log('\n🎉 UMURONGO SYSTEM STARTED SUCCESSFULLY!');
    console.log('=' .repeat(50));
    console.log('🔧 Backend: http://localhost:5001');
    console.log('💻 Frontend: http://localhost:3000');
    console.log('🐛 Debug Panel: Available in frontend (bottom-right)');
    console.log('=' .repeat(50));
    console.log('\n📱 Demo Credentials:');
    console.log('Customer: +250788000003 / customer123');
    console.log('Admin: +250788000001 / admin123456');
    console.log('\n💡 Press Ctrl+C to stop all services');
    
    // Keep the process running
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ Failed to start system:', error.message);
    cleanup();
  }
}

// Start the system
startSystem();
