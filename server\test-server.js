console.log('🔍 Testing server startup...');

try {
  console.log('1. Loading dependencies...');
  const express = require('express');
  console.log('✅ Express loaded');
  
  const cors = require('cors');
  console.log('✅ CORS loaded');
  
  require('dotenv').config();
  console.log('✅ Dotenv loaded');
  
  console.log('2. Testing database connection...');
  const db = require('./config/database');
  console.log('✅ Database config loaded');
  
  console.log('3. Testing database authentication...');
  db.authenticate()
    .then(() => {
      console.log('✅ Database connection successful');
      
      console.log('4. Starting simple server...');
      const app = express();
      
      app.use(cors());
      app.use(express.json());
      
      app.get('/health', (req, res) => {
        res.json({ status: 'OK', message: 'Test server running' });
      });
      
      const PORT = 5001;
      app.listen(PORT, () => {
        console.log(`🚀 Test server running on port ${PORT}`);
        console.log(`🔗 Test: http://localhost:${PORT}/health`);
      });
    })
    .catch(error => {
      console.error('❌ Database connection failed:', error);
      process.exit(1);
    });
    
} catch (error) {
  console.error('❌ Error during startup:', error);
  process.exit(1);
}
