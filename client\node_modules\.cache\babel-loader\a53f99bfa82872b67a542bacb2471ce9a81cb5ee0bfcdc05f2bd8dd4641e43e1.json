{"ast": null, "code": "var _jsxFileName = \"E:\\\\CBS Pro\\\\umurongo\\\\client\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/auth/profile');\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const login = async (phone, password) => {\n    try {\n      console.log('Attempting login with:', {\n        phone,\n        password: '***'\n      });\n      const response = await axios.post('/api/auth/login', {\n        phone,\n        password\n      });\n      console.log('Login response:', response.data);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      toast.success(`Welcome back, ${user.name}!`);\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('Login error:', error);\n      let message = 'Login failed';\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        message = 'Cannot connect to server. Please check if the backend is running on port 5002.';\n      } else if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        message = 'Invalid phone number or password. Please check your credentials.';\n      } else if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n        message = error.response.data.message;\n      }\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await axios.post('/api/auth/register', userData);\n      const {\n        user,\n        token\n      } = response.data.data;\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n      toast.success('Registration successful! Please verify your phone number.');\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const message = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const verifyPhone = async (phone, code) => {\n    try {\n      const response = await axios.post('/api/auth/verify-phone', {\n        phone,\n        code\n      });\n\n      // Update user verification status\n      setUser(prev => ({\n        ...prev,\n        is_verified: true\n      }));\n      toast.success('Phone number verified successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const message = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Verification failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const resendVerification = async phone => {\n    try {\n      await axios.post('/api/auth/resend-verification', {\n        phone\n      });\n      toast.success('Verification code sent!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const message = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to send verification code';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await axios.put('/api/users/profile', profileData);\n      setUser(response.data.data.user);\n      toast.success('Profile updated successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      const message = ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Profile update failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('/api/users/change-password', {\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n      toast.success('Password changed successfully!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      const message = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Password change failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const value = {\n    user,\n    loading,\n    token,\n    login,\n    register,\n    verifyPhone,\n    resendVerification,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user,\n    isVerified: (user === null || user === void 0 ? void 0 : user.is_verified) || false,\n    isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'system_admin' || (user === null || user === void 0 ? void 0 : user.role) === 'business_admin'\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"V8bE6DZSV5/nB2UMC4Uofie15PA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "console", "logout", "login", "phone", "password", "log", "post", "setItem", "success", "name", "_error$response", "_error$response2", "_error$response2$data", "message", "code", "status", "register", "userData", "_error$response3", "_error$response3$data", "verifyPhone", "prev", "is_verified", "_error$response4", "_error$response4$data", "resendVerification", "_error$response5", "_error$response5$data", "removeItem", "updateProfile", "profileData", "put", "_error$response6", "_error$response6$data", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "_error$response7", "_error$response7$data", "value", "isAuthenticated", "isVerified", "isAdmin", "role", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/CBS Pro/umurongo/client/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/auth/profile');\n          setUser(response.data.data.user);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const login = async (phone, password) => {\n    try {\n      console.log('Attempting login with:', { phone, password: '***' });\n\n      const response = await axios.post('/api/auth/login', {\n        phone,\n        password\n      });\n\n      console.log('Login response:', response.data);\n\n      const { user, token } = response.data.data;\n\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n\n      toast.success(`Welcome back, ${user.name}!`);\n      return { success: true, user };\n    } catch (error) {\n      console.error('Login error:', error);\n\n      let message = 'Login failed';\n\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        message = 'Cannot connect to server. Please check if the backend is running on port 5002.';\n      } else if (error.response?.status === 401) {\n        message = 'Invalid phone number or password. Please check your credentials.';\n      } else if (error.response?.data?.message) {\n        message = error.response.data.message;\n      }\n\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      const response = await axios.post('/api/auth/register', userData);\n\n      const { user, token } = response.data.data;\n\n      setUser(user);\n      setToken(token);\n      localStorage.setItem('token', token);\n\n      toast.success('Registration successful! Please verify your phone number.');\n      return { success: true, user };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const verifyPhone = async (phone, code) => {\n    try {\n      const response = await axios.post('/api/auth/verify-phone', {\n        phone,\n        code\n      });\n\n      // Update user verification status\n      setUser(prev => ({ ...prev, is_verified: true }));\n\n      toast.success('Phone number verified successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Verification failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const resendVerification = async (phone) => {\n    try {\n      await axios.post('/api/auth/resend-verification', { phone });\n      toast.success('Verification code sent!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Failed to send verification code';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n\n  const updateProfile = async (profileData) => {\n    try {\n      const response = await axios.put('/api/users/profile', profileData);\n      setUser(response.data.data.user);\n      toast.success('Profile updated successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Profile update failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const changePassword = async (currentPassword, newPassword) => {\n    try {\n      await axios.put('/api/users/change-password', {\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n      toast.success('Password changed successfully!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Password change failed';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    token,\n    login,\n    register,\n    verifyPhone,\n    resendVerification,\n    logout,\n    updateProfile,\n    changePassword,\n    isAuthenticated: !!user,\n    isVerified: user?.is_verified || false,\n    isAdmin: user?.role === 'system_admin' || user?.role === 'business_admin'\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAACoB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;;EAEjE;EACApB,SAAS,CAAC,MAAM;IACd,IAAIiB,KAAK,EAAE;MACThB,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUN,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOhB,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;;EAEX;EACAjB,SAAS,CAAC,MAAM;IACd,MAAMwB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIP,KAAK,EAAE;QACT,IAAI;UACF,MAAMQ,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,mBAAmB,CAAC;UACrDZ,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,IAAI,CAAC;QAClC,CAAC,CAAC,OAAOe,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CE,MAAM,CAAC,CAAC;QACV;MACF;MACAd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,KAAK,CAAC,CAAC;EAEX,MAAMc,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACFJ,OAAO,CAACK,GAAG,CAAC,wBAAwB,EAAE;QAAEF,KAAK;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC;MAEjE,MAAMR,QAAQ,GAAG,MAAMxB,KAAK,CAACkC,IAAI,CAAC,iBAAiB,EAAE;QACnDH,KAAK;QACLC;MACF,CAAC,CAAC;MAEFJ,OAAO,CAACK,GAAG,CAAC,iBAAiB,EAAET,QAAQ,CAACE,IAAI,CAAC;MAE7C,MAAM;QAAEd,IAAI;QAAEI;MAAM,CAAC,GAAGQ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1Cb,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEnB,KAAK,CAAC;MAEpCf,KAAK,CAACmC,OAAO,CAAC,iBAAiBxB,IAAI,CAACyB,IAAI,GAAG,CAAC;MAC5C,OAAO;QAAED,OAAO,EAAE,IAAI;QAAExB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdZ,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MAEpC,IAAIc,OAAO,GAAG,cAAc;MAE5B,IAAId,KAAK,CAACe,IAAI,KAAK,cAAc,IAAIf,KAAK,CAACe,IAAI,KAAK,aAAa,EAAE;QACjED,OAAO,GAAG,gFAAgF;MAC5F,CAAC,MAAM,IAAI,EAAAH,eAAA,GAAAX,KAAK,CAACH,QAAQ,cAAAc,eAAA,uBAAdA,eAAA,CAAgBK,MAAM,MAAK,GAAG,EAAE;QACzCF,OAAO,GAAG,kEAAkE;MAC9E,CAAC,MAAM,KAAAF,gBAAA,GAAIZ,KAAK,CAACH,QAAQ,cAAAe,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,eAApBA,qBAAA,CAAsBC,OAAO,EAAE;QACxCA,OAAO,GAAGd,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACe,OAAO;MACvC;MAEAxC,KAAK,CAAC0B,KAAK,CAACc,OAAO,CAAC;MACpB,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAET,KAAK,EAAEc;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMxB,KAAK,CAACkC,IAAI,CAAC,oBAAoB,EAAEW,QAAQ,CAAC;MAEjE,MAAM;QAAEjC,IAAI;QAAEI;MAAM,CAAC,GAAGQ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE1Cb,OAAO,CAACD,IAAI,CAAC;MACbK,QAAQ,CAACD,KAAK,CAAC;MACfE,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEnB,KAAK,CAAC;MAEpCf,KAAK,CAACmC,OAAO,CAAC,2DAA2D,CAAC;MAC1E,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAExB;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOe,KAAK,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACd,MAAMN,OAAO,GAAG,EAAAK,gBAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,qBAAqB;MACtExC,KAAK,CAAC0B,KAAK,CAACc,OAAO,CAAC;MACpB,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAET,KAAK,EAAEc;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMO,WAAW,GAAG,MAAAA,CAAOjB,KAAK,EAAEW,IAAI,KAAK;IACzC,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMxB,KAAK,CAACkC,IAAI,CAAC,wBAAwB,EAAE;QAC1DH,KAAK;QACLW;MACF,CAAC,CAAC;;MAEF;MACA7B,OAAO,CAACoC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEC,WAAW,EAAE;MAAK,CAAC,CAAC,CAAC;MAEjDjD,KAAK,CAACmC,OAAO,CAAC,qCAAqC,CAAC;MACpD,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACd,MAAMX,OAAO,GAAG,EAAAU,gBAAA,GAAAxB,KAAK,CAACH,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBX,OAAO,KAAI,qBAAqB;MACtExC,KAAK,CAAC0B,KAAK,CAACc,OAAO,CAAC;MACpB,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAET,KAAK,EAAEc;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMY,kBAAkB,GAAG,MAAOtB,KAAK,IAAK;IAC1C,IAAI;MACF,MAAM/B,KAAK,CAACkC,IAAI,CAAC,+BAA+B,EAAE;QAAEH;MAAM,CAAC,CAAC;MAC5D9B,KAAK,CAACmC,OAAO,CAAC,yBAAyB,CAAC;MACxC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA;MACd,MAAMd,OAAO,GAAG,EAAAa,gBAAA,GAAA3B,KAAK,CAACH,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,kCAAkC;MACnFxC,KAAK,CAAC0B,KAAK,CAACc,OAAO,CAAC;MACpB,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAET,KAAK,EAAEc;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMZ,MAAM,GAAGA,CAAA,KAAM;IACnBhB,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAACsC,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOxD,KAAK,CAACoB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDrB,KAAK,CAACmC,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;EAED,MAAMqB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAMxB,KAAK,CAAC2D,GAAG,CAAC,oBAAoB,EAAED,WAAW,CAAC;MACnE7C,OAAO,CAACW,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACd,IAAI,CAAC;MAChCX,KAAK,CAACmC,OAAO,CAAC,+BAA+B,CAAC;MAC9C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACd,MAAMpB,OAAO,GAAG,EAAAmB,gBAAA,GAAAjC,KAAK,CAACH,QAAQ,cAAAoC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,IAAI,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsBpB,OAAO,KAAI,uBAAuB;MACxExC,KAAK,CAAC0B,KAAK,CAACc,OAAO,CAAC;MACpB,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAET,KAAK,EAAEc;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMqB,cAAc,GAAG,MAAAA,CAAOC,eAAe,EAAEC,WAAW,KAAK;IAC7D,IAAI;MACF,MAAMhE,KAAK,CAAC2D,GAAG,CAAC,4BAA4B,EAAE;QAC5CM,gBAAgB,EAAEF,eAAe;QACjCG,YAAY,EAAEF;MAChB,CAAC,CAAC;MACF/D,KAAK,CAACmC,OAAO,CAAC,gCAAgC,CAAC;MAC/C,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACd,MAAM3B,OAAO,GAAG,EAAA0B,gBAAA,GAAAxC,KAAK,CAACH,QAAQ,cAAA2C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzC,IAAI,cAAA0C,qBAAA,uBAApBA,qBAAA,CAAsB3B,OAAO,KAAI,wBAAwB;MACzExC,KAAK,CAAC0B,KAAK,CAACc,OAAO,CAAC;MACpB,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAET,KAAK,EAAEc;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAM4B,KAAK,GAAG;IACZzD,IAAI;IACJE,OAAO;IACPE,KAAK;IACLc,KAAK;IACLc,QAAQ;IACRI,WAAW;IACXK,kBAAkB;IAClBxB,MAAM;IACN4B,aAAa;IACbK,cAAc;IACdQ,eAAe,EAAE,CAAC,CAAC1D,IAAI;IACvB2D,UAAU,EAAE,CAAA3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,WAAW,KAAI,KAAK;IACtCsB,OAAO,EAAE,CAAA5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,MAAK,cAAc,IAAI,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,MAAK;EAC3D,CAAC;EAED,oBACEtE,OAAA,CAACC,WAAW,CAACsE,QAAQ;IAACL,KAAK,EAAEA,KAAM;IAAA3D,QAAA,EAChCA;EAAQ;IAAAiE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnE,GAAA,CAhLWF,YAAY;AAAAsE,EAAA,GAAZtE,YAAY;AAAA,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}