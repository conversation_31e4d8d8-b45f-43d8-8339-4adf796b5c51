import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useApp } from '../contexts/AppContext';
import {
  CalendarDaysIcon,
  ClockIcon,
  MapPinIcon,
  UserGroupIcon,
  ChartBarIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';

const DashboardPage = () => {
  const { user } = useAuth();
  const { 
    appointments, 
    queuePosition, 
    fetchAppointments, 
    getQueuePosition,
    getDashboardStats 
  } = useApp();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      await Promise.all([
        fetchAppointments(),
        getQueuePosition(),
        getDashboardStats().then(setStats)
      ]);
      setLoading(false);
    };

    loadDashboardData();
  }, []);

  const upcomingAppointments = appointments.filter(apt => 
    apt.status === 'confirmed' && new Date(apt.appointment_date) > new Date()
  ).slice(0, 3);

  const quickStats = [
    {
      name: 'Total Appointments',
      value: appointments.length,
      icon: CalendarDaysIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'Upcoming',
      value: upcomingAppointments.length,
      icon: ClockIcon,
      color: 'bg-green-500'
    },
    {
      name: 'Queue Position',
      value: queuePosition ? `#${queuePosition.position}` : 'Not in queue',
      icon: UserGroupIcon,
      color: 'bg-yellow-500'
    },
    {
      name: 'Locations Visited',
      value: stats?.locations_visited || 0,
      icon: MapPinIcon,
      color: 'bg-purple-500'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white"
      >
        <h1 className="text-3xl font-bold mb-2">
          Welcome back, {user?.name}! 👋
        </h1>
        <p className="text-primary-100 text-lg">
          Here's what's happening with your appointments and queue status.
        </p>
      </motion.div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-soft p-6"
          >
            <div className="flex items-center">
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Queue Status Alert */}
      {queuePosition && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="bg-yellow-50 border border-yellow-200 rounded-xl p-6"
        >
          <div className="flex items-center">
            <div className="bg-yellow-100 p-2 rounded-lg">
              <ClockIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4 flex-1">
              <h3 className="text-lg font-semibold text-yellow-800">
                You're in the queue!
              </h3>
              <p className="text-yellow-700">
                Position #{queuePosition.position} for {queuePosition.service_name} at {queuePosition.location_name}
              </p>
              <p className="text-sm text-yellow-600 mt-1">
                Estimated wait time: {queuePosition.estimated_wait_time} minutes
              </p>
            </div>
            <Link
              to="/queue"
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              View Queue
            </Link>
          </div>
        </motion.div>
      )}

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Upcoming Appointments */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-xl shadow-soft p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Upcoming Appointments</h2>
            <Link
              to="/appointments"
              className="text-primary-600 hover:text-primary-700 font-medium text-sm"
            >
              View all
            </Link>
          </div>

          {upcomingAppointments.length > 0 ? (
            <div className="space-y-4">
              {upcomingAppointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {appointment.service_name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {appointment.location_name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(appointment.appointment_date).toLocaleDateString()} at{' '}
                        {appointment.appointment_time}
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      appointment.status === 'confirmed' 
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {appointment.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CalendarDaysIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No upcoming appointments</p>
              <Link
                to="/services"
                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Book Appointment
              </Link>
            </div>
          )}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="bg-white rounded-xl shadow-soft p-6"
        >
          <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          
          <div className="space-y-4">
            <Link
              to="/services"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors group"
            >
              <div className="bg-primary-100 p-3 rounded-lg group-hover:bg-primary-200">
                <CalendarDaysIcon className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Book Appointment</h3>
                <p className="text-sm text-gray-600">Schedule a new appointment</p>
              </div>
            </Link>

            <Link
              to="/locations"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors group"
            >
              <div className="bg-primary-100 p-3 rounded-lg group-hover:bg-primary-200">
                <MapPinIcon className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Find Locations</h3>
                <p className="text-sm text-gray-600">Discover nearby services</p>
              </div>
            </Link>

            <Link
              to="/queue"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors group"
            >
              <div className="bg-primary-100 p-3 rounded-lg group-hover:bg-primary-200">
                <UserGroupIcon className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <h3 className="font-semibold text-gray-900">Join Queue</h3>
                <p className="text-sm text-gray-600">Get in line for walk-in services</p>
              </div>
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardPage;
