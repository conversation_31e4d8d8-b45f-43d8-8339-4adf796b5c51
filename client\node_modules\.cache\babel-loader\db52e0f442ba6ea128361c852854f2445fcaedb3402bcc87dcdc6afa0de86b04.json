{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInYear } from \"../constants/index.js\";\n/**\n * @name yearsToMonths\n * @category Conversion Helpers\n * @summary Convert years to months.\n *\n * @description\n * Convert a number of years to a full number of months.\n *\n * @param {number} years - number of years to be converted\n *\n * @returns {number} the number of years converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 years into months\n * const result = yearsToMonths(2)\n * //=> 24\n */\nexport default function yearsToMonths(years) {\n  requiredArgs(1, arguments);\n  return Math.floor(years * monthsInYear);\n}", "map": {"version": 3, "names": ["requiredArgs", "monthsInYear", "yearsToMonths", "years", "arguments", "Math", "floor"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/yearsToMonths/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInYear } from \"../constants/index.js\";\n/**\n * @name yearsToMonths\n * @category Conversion Helpers\n * @summary Convert years to months.\n *\n * @description\n * Convert a number of years to a full number of months.\n *\n * @param {number} years - number of years to be converted\n *\n * @returns {number} the number of years converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 years into months\n * const result = yearsToMonths(2)\n * //=> 24\n */\nexport default function yearsToMonths(years) {\n  requiredArgs(1, arguments);\n  return Math.floor(years * monthsInYear);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGF,YAAY,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}