import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useApp } from '../contexts/AppContext';
import {
  ClockIcon,
  UserGroupIcon,
  PlayIcon,
  XMarkIcon,
  CheckIcon,
  MapPinIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const QueuePage = () => {
  const {
    services,
    fetchServices,
    joinQueue,
    leaveQueue,
    getQueuePosition,
    loading
  } = useApp();

  const [currentQueue, setCurrentQueue] = useState(null);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [selectedService, setSelectedService] = useState(null);

  useEffect(() => {
    fetchServices();
    checkCurrentQueue();
  }, []);

  const checkCurrentQueue = async () => {
    const result = await getQueuePosition();
    if (result.success && result.data) {
      setCurrentQueue(result.data);
    }
  };

  const handleJoinQueue = async (service) => {
    setSelectedService(service);
    setShowJoinModal(true);
  };

  const confirmJoinQueue = async () => {
    if (!selectedService) return;

    const result = await joinQueue(selectedService.id);
    if (result.success) {
      setCurrentQueue(result.data);
      setShowJoinModal(false);
      setSelectedService(null);
    }
  };

  const handleLeaveQueue = async () => {
    if (!window.confirm('Are you sure you want to leave the queue?')) return;

    const result = await leaveQueue();
    if (result.success) {
      setCurrentQueue(null);
    }
  };

  const getWaitTimeDisplay = (minutes) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-3xl font-bold text-gray-900">Queue Management</h1>
        <p className="text-gray-600 mt-2">
          Join queues for walk-in services and track your position in real-time.
        </p>
      </motion.div>

      {/* Current Queue Status */}
      {currentQueue && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl shadow-soft p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-xl font-semibold mb-2">You're in Queue!</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <WrenchScrewdriverIcon className="w-5 h-5 mr-2" />
                  <div>
                    <p className="text-sm opacity-90">Service</p>
                    <p className="font-medium">{currentQueue.service_name}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <UserGroupIcon className="w-5 h-5 mr-2" />
                  <div>
                    <p className="text-sm opacity-90">Position</p>
                    <p className="font-medium">#{currentQueue.position}</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <ClockIcon className="w-5 h-5 mr-2" />
                  <div>
                    <p className="text-sm opacity-90">Estimated Wait</p>
                    <p className="font-medium">
                      {getWaitTimeDisplay(currentQueue.estimated_wait_time)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-4 flex items-center">
                <MapPinIcon className="w-4 h-4 mr-2" />
                <span className="text-sm opacity-90">{currentQueue.location_name}</span>
              </div>
            </div>

            <button
              onClick={handleLeaveQueue}
              className="ml-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
            >
              <XMarkIcon className="w-4 h-4 mr-2" />
              Leave Queue
            </button>
          </div>
        </motion.div>
      )}

      {/* Available Services */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="bg-white rounded-xl shadow-soft p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Available Walk-in Services
        </h3>

        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
          </div>
        ) : services.length === 0 ? (
          <div className="text-center py-8">
            <UserGroupIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Services Available</h3>
            <p className="text-gray-600">
              There are no walk-in services available at the moment.
            </p>
          </div>
        ) : (
          <div className="grid gap-4">
            {services.map((service) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{service.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{service.description}</p>

                    <div className="flex items-center gap-4 mt-3 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="w-4 h-4 mr-1" />
                        <span>{service.duration} min</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        <span>{service.category}</span>
                      </div>
                      <div>
                        <span className="font-medium">{service.price} RWF</span>
                      </div>
                    </div>
                  </div>

                  <div className="ml-4">
                    {currentQueue ? (
                      <button
                        disabled
                        className="px-4 py-2 bg-gray-100 text-gray-400 rounded-lg font-medium cursor-not-allowed"
                      >
                        Already in Queue
                      </button>
                    ) : (
                      <button
                        onClick={() => handleJoinQueue(service)}
                        className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center"
                      >
                        <PlayIcon className="w-4 h-4 mr-2" />
                        Join Queue
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Queue Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <div className="bg-white rounded-xl shadow-soft p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <UserGroupIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Services</p>
              <p className="text-2xl font-bold text-gray-900">{services.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-soft p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <ClockIcon className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Wait Time</p>
              <p className="text-2xl font-bold text-gray-900">
                {services.length > 0
                  ? Math.round(services.reduce((acc, s) => acc + s.duration, 0) / services.length)
                  : 0
                } min
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-soft p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <CheckIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Queue Status</p>
              <p className="text-2xl font-bold text-gray-900">
                {currentQueue ? 'Active' : 'None'}
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Join Queue Confirmation Modal */}
      {showJoinModal && selectedService && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-xl p-6 w-full max-w-md mx-4"
          >
            <div className="flex items-center mb-4">
              <div className="bg-primary-100 p-2 rounded-lg mr-3">
                <UserGroupIcon className="w-6 h-6 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Join Queue
              </h3>
            </div>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-2">{selectedService.name}</h4>
              <p className="text-sm text-gray-600 mb-4">{selectedService.description}</p>

              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Duration:</span>
                  <span className="font-medium">{selectedService.duration} minutes</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Category:</span>
                  <span className="font-medium">{selectedService.category}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Price:</span>
                  <span className="font-medium">{selectedService.price} RWF</span>
                </div>
              </div>
            </div>

            <p className="text-sm text-gray-500 mb-6">
              You'll be added to the queue and can track your position in real-time.
              You'll receive notifications when it's almost your turn.
            </p>

            <div className="flex gap-3">
              <button
                onClick={() => setShowJoinModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmJoinQueue}
                disabled={loading}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
              >
                {loading ? 'Joining...' : 'Join Queue'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default QueuePage;
