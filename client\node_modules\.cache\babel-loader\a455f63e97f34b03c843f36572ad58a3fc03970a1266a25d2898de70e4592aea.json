{"ast": null, "code": "import { statsBuffer } from '../stats/buffer.mjs';\nfunction createRenderStep(runNextFrame, stepName) {\n  /**\n   * We create and reuse two queues, one to queue jobs for the current frame\n   * and one for the next. We reuse to avoid triggering GC after x frames.\n   */\n  let thisFrame = new Set();\n  let nextFrame = new Set();\n  /**\n   * Track whether we're currently processing jobs in this step. This way\n   * we can decide whether to schedule new jobs for this frame or next.\n   */\n  let isProcessing = false;\n  let flushNextFrame = false;\n  /**\n   * A set of processes which were marked keepAlive when scheduled.\n   */\n  const toKeepAlive = new WeakSet();\n  let latestFrameData = {\n    delta: 0.0,\n    timestamp: 0.0,\n    isProcessing: false\n  };\n  let numCalls = 0;\n  function triggerCallback(callback) {\n    if (toKeepAlive.has(callback)) {\n      step.schedule(callback);\n      runNextFrame();\n    }\n    numCalls++;\n    callback(latestFrameData);\n  }\n  const step = {\n    /**\n     * Schedule a process to run on the next frame.\n     */\n    schedule: (callback, keepAlive = false, immediate = false) => {\n      const addToCurrentFrame = immediate && isProcessing;\n      const queue = addToCurrentFrame ? thisFrame : nextFrame;\n      if (keepAlive) toKeepAlive.add(callback);\n      if (!queue.has(callback)) queue.add(callback);\n      return callback;\n    },\n    /**\n     * Cancel the provided callback from running on the next frame.\n     */\n    cancel: callback => {\n      nextFrame.delete(callback);\n      toKeepAlive.delete(callback);\n    },\n    /**\n     * Execute all schedule callbacks.\n     */\n    process: frameData => {\n      latestFrameData = frameData;\n      /**\n       * If we're already processing we've probably been triggered by a flushSync\n       * inside an existing process. Instead of executing, mark flushNextFrame\n       * as true and ensure we flush the following frame at the end of this one.\n       */\n      if (isProcessing) {\n        flushNextFrame = true;\n        return;\n      }\n      isProcessing = true;\n      [thisFrame, nextFrame] = [nextFrame, thisFrame];\n      // Execute this frame\n      thisFrame.forEach(triggerCallback);\n      /**\n       * If we're recording stats then\n       */\n      if (stepName && statsBuffer.value) {\n        statsBuffer.value.frameloop[stepName].push(numCalls);\n      }\n      numCalls = 0;\n      // Clear the frame so no callbacks remain. This is to avoid\n      // memory leaks should this render step not run for a while.\n      thisFrame.clear();\n      isProcessing = false;\n      if (flushNextFrame) {\n        flushNextFrame = false;\n        step.process(frameData);\n      }\n    }\n  };\n  return step;\n}\nexport { createRenderStep };", "map": {"version": 3, "names": ["statsBuffer", "createRenderStep", "runNextFrame", "<PERSON><PERSON><PERSON>", "thisFrame", "Set", "next<PERSON><PERSON><PERSON>", "isProcessing", "flushNextFrame", "toKeepAlive", "WeakSet", "latestFrameData", "delta", "timestamp", "numCalls", "triggerCallback", "callback", "has", "step", "schedule", "keepAlive", "immediate", "addToCurrentFrame", "queue", "add", "cancel", "delete", "process", "frameData", "for<PERSON>ach", "value", "frameloop", "push", "clear"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/motion-dom/dist/es/frameloop/render-step.mjs"], "sourcesContent": ["import { statsBuffer } from '../stats/buffer.mjs';\n\nfunction createRenderStep(runNextFrame, stepName) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    let numCalls = 0;\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        numCalls++;\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            /**\n             * If we're recording stats then\n             */\n            if (stepName && statsBuffer.value) {\n                statsBuffer.value.frameloop[stepName].push(numCalls);\n            }\n            numCalls = 0;\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,qBAAqB;AAEjD,SAASC,gBAAgBA,CAACC,YAAY,EAAEC,QAAQ,EAAE;EAC9C;AACJ;AACA;AACA;EACI,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB,IAAIC,SAAS,GAAG,IAAID,GAAG,CAAC,CAAC;EACzB;AACJ;AACA;AACA;EACI,IAAIE,YAAY,GAAG,KAAK;EACxB,IAAIC,cAAc,GAAG,KAAK;EAC1B;AACJ;AACA;EACI,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAG;IAClBC,KAAK,EAAE,GAAG;IACVC,SAAS,EAAE,GAAG;IACdN,YAAY,EAAE;EAClB,CAAC;EACD,IAAIO,QAAQ,GAAG,CAAC;EAChB,SAASC,eAAeA,CAACC,QAAQ,EAAE;IAC/B,IAAIP,WAAW,CAACQ,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC3BE,IAAI,CAACC,QAAQ,CAACH,QAAQ,CAAC;MACvBd,YAAY,CAAC,CAAC;IAClB;IACAY,QAAQ,EAAE;IACVE,QAAQ,CAACL,eAAe,CAAC;EAC7B;EACA,MAAMO,IAAI,GAAG;IACT;AACR;AACA;IACQC,QAAQ,EAAEA,CAACH,QAAQ,EAAEI,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;MAC1D,MAAMC,iBAAiB,GAAGD,SAAS,IAAId,YAAY;MACnD,MAAMgB,KAAK,GAAGD,iBAAiB,GAAGlB,SAAS,GAAGE,SAAS;MACvD,IAAIc,SAAS,EACTX,WAAW,CAACe,GAAG,CAACR,QAAQ,CAAC;MAC7B,IAAI,CAACO,KAAK,CAACN,GAAG,CAACD,QAAQ,CAAC,EACpBO,KAAK,CAACC,GAAG,CAACR,QAAQ,CAAC;MACvB,OAAOA,QAAQ;IACnB,CAAC;IACD;AACR;AACA;IACQS,MAAM,EAAGT,QAAQ,IAAK;MAClBV,SAAS,CAACoB,MAAM,CAACV,QAAQ,CAAC;MAC1BP,WAAW,CAACiB,MAAM,CAACV,QAAQ,CAAC;IAChC,CAAC;IACD;AACR;AACA;IACQW,OAAO,EAAGC,SAAS,IAAK;MACpBjB,eAAe,GAAGiB,SAAS;MAC3B;AACZ;AACA;AACA;AACA;MACY,IAAIrB,YAAY,EAAE;QACdC,cAAc,GAAG,IAAI;QACrB;MACJ;MACAD,YAAY,GAAG,IAAI;MACnB,CAACH,SAAS,EAAEE,SAAS,CAAC,GAAG,CAACA,SAAS,EAAEF,SAAS,CAAC;MAC/C;MACAA,SAAS,CAACyB,OAAO,CAACd,eAAe,CAAC;MAClC;AACZ;AACA;MACY,IAAIZ,QAAQ,IAAIH,WAAW,CAAC8B,KAAK,EAAE;QAC/B9B,WAAW,CAAC8B,KAAK,CAACC,SAAS,CAAC5B,QAAQ,CAAC,CAAC6B,IAAI,CAAClB,QAAQ,CAAC;MACxD;MACAA,QAAQ,GAAG,CAAC;MACZ;MACA;MACAV,SAAS,CAAC6B,KAAK,CAAC,CAAC;MACjB1B,YAAY,GAAG,KAAK;MACpB,IAAIC,cAAc,EAAE;QAChBA,cAAc,GAAG,KAAK;QACtBU,IAAI,CAACS,OAAO,CAACC,SAAS,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD,OAAOV,IAAI;AACf;AAEA,SAASjB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}