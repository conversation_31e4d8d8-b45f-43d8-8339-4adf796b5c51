{"ast": null, "code": "import * as React from \"react\";\nfunction H2Icon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.246 3.743a.75.75 0 0 1 .75.75v6.75h9v-6.75a.75.75 0 0 1 1.5 0v15.002a.75.75 0 1 1-1.5 0v-6.751h-9v6.75a.75.75 0 1 1-1.5 0v-15a.75.75 0 0 1 .75-.75ZM18.75 10.5c-.727 0-1.441.054-2.138.16a.75.75 0 1 1-.223-1.484 15.867 15.867 0 0 1 3.635-.125c1.149.092 2.153.923 2.348 2.115.084.516.128 1.045.128 1.584 0 1.065-.676 1.927-1.531 2.354l-2.89 1.445a1.5 1.5 0 0 0-.829 1.342v.86h4.5a.75.75 0 1 1 0 1.5H16.5a.75.75 0 0 1-.75-.75v-1.61a3 3 0 0 1 1.659-2.684l2.89-1.445c.447-.223.701-.62.701-1.012a8.32 8.32 0 0 0-.108-1.342c-.075-.457-.47-.82-.987-.862a14.45 14.45 0 0 0-1.155-.046Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(H2Icon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "H2Icon", "title", "titleId", "props", "svgRef", "createElement", "Object", "assign", "xmlns", "viewBox", "fill", "ref", "id", "fillRule", "d", "clipRule", "ForwardRef", "forwardRef"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/@heroicons/react/24/solid/esm/H2Icon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction H2Icon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.246 3.743a.75.75 0 0 1 .75.75v6.75h9v-6.75a.75.75 0 0 1 1.5 0v15.002a.75.75 0 1 1-1.5 0v-6.751h-9v6.75a.75.75 0 1 1-1.5 0v-15a.75.75 0 0 1 .75-.75ZM18.75 10.5c-.727 0-1.441.054-2.138.16a.75.75 0 1 1-.223-1.484 15.867 15.867 0 0 1 3.635-.125c1.149.092 2.153.923 2.348 2.115.084.516.128 1.045.128 1.584 0 1.065-.676 1.927-1.531 2.354l-2.89 1.445a1.5 1.5 0 0 0-.829 1.342v.86h4.5a.75.75 0 1 1 0 1.5H16.5a.75.75 0 0 1-.75-.75v-1.61a3 3 0 0 1 1.659-2.684l2.89-1.445c.447-.223.701-.62.701-1.012a8.32 8.32 0 0 0-.108-1.342c-.075-.457-.47-.82-.987-.862a14.45 14.45 0 0 0-1.155-.046Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(H2Icon);\nexport default ForwardRef;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAMA,CAAC;EACdC,KAAK;EACLC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,MAAM,EAAE;EACT,OAAO,aAAaL,KAAK,CAACM,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,cAAc;IACpB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEP,MAAM;IACX,iBAAiB,EAAEF;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaF,KAAK,CAACM,aAAa,CAAC,OAAO,EAAE;IAC3DO,EAAE,EAAEV;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaF,KAAK,CAACM,aAAa,CAAC,MAAM,EAAE;IACzDQ,QAAQ,EAAE,SAAS;IACnBC,CAAC,EAAE,mkBAAmkB;IACtkBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAAcjB,KAAK,CAACkB,UAAU,CAACjB,MAAM,CAAC;AACzD,eAAegB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}