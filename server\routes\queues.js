const express = require('express');
const { Op, sequelize } = require('sequelize');
const { Queue, Appointment, Service, Location, User } = require('../models');
const { authenticateToken, requireLocationAdmin } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');
const smsService = require('../services/smsService');

const router = express.Router();

// Get queue for a location
router.get('/location/:locationId', authenticateToken, async (req, res) => {
  try {
    const { locationId } = req.params;
    const { date, service_id, status } = req.query;

    const queueDate = date || new Date().toISOString().split('T')[0];

    const whereClause = {
      location_id: locationId,
      queue_date: queueDate
    };

    if (service_id) {
      whereClause.service_id = service_id;
    }

    if (status) {
      whereClause.status = status;
    } else {
      // Default to active queue items
      whereClause.status = ['waiting', 'called', 'in_service'];
    }

    const queueItems = await Queue.findAll({
      where: whereClause,
      include: [
        {
          model: Appointment,
          as: 'appointment',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'phone']
            }
          ]
        },
        {
          model: Service,
          as: 'service',
          attributes: ['id', 'name', 'category', 'duration']
        },
        {
          model: User,
          as: 'server',
          attributes: ['id', 'name'],
          required: false
        }
      ],
      order: [['position', 'ASC']]
    });

    // Calculate current statistics
    const stats = {
      total_in_queue: queueItems.filter(q => ['waiting', 'called', 'in_service'].includes(q.status)).length,
      waiting: queueItems.filter(q => q.status === 'waiting').length,
      in_service: queueItems.filter(q => q.status === 'in_service').length,
      completed_today: await Queue.count({
        where: {
          location_id: locationId,
          queue_date: queueDate,
          status: 'completed'
        }
      })
    };

    res.json({
      success: true,
      data: {
        queue_items: queueItems,
        statistics: stats,
        queue_date: queueDate
      }
    });
  } catch (error) {
    console.error('Get location queue error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch queue'
    });
  }
});

// Get user's current queue position
router.get('/my-position', authenticateToken, async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];

    const userQueues = await Queue.findAll({
      where: {
        user_id: req.user.id,
        queue_date: today,
        status: ['waiting', 'called', 'in_service']
      },
      include: [
        {
          model: Appointment,
          as: 'appointment'
        },
        {
          model: Service,
          as: 'service',
          attributes: ['id', 'name', 'duration']
        },
        {
          model: Location,
          as: 'location',
          attributes: ['id', 'name', 'address']
        }
      ],
      order: [['created_at', 'ASC']]
    });

    // Calculate current position for each queue
    for (let queue of userQueues) {
      const currentPosition = await Queue.count({
        where: {
          location_id: queue.location_id,
          queue_date: today,
          status: ['waiting', 'called', 'in_service'],
          position: { [Op.lt]: queue.position }
        }
      });
      queue.dataValues.current_position = currentPosition + 1;
      queue.dataValues.estimated_wait_time = queue.calculateWaitTime();
    }

    res.json({
      success: true,
      data: {
        queues: userQueues
      }
    });
  } catch (error) {
    console.error('Get user queue position error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch queue position'
    });
  }
});

// Call next person in queue (admin only)
router.post('/location/:locationId/call-next',
  authenticateToken,
  requireLocationAdmin,
  async (req, res) => {
    try {
      const { locationId } = req.params;
      const { service_id, room_number } = req.body;

      const today = new Date().toISOString().split('T')[0];

      // Get next person in queue
      const nextInQueue = await Queue.getNextInLine(locationId, service_id);

      if (!nextInQueue) {
        return res.status(404).json({
          success: false,
          message: 'No one waiting in queue'
        });
      }

      // Update queue status
      await nextInQueue.update({
        status: 'called',
        called_at: new Date(),
        room_number,
        served_by: req.user.id
      });

      // Get complete queue data
      const calledQueue = await Queue.findByPk(nextInQueue.id, {
        include: [
          {
            model: Appointment,
            as: 'appointment',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'phone']
              }
            ]
          },
          {
            model: Service,
            as: 'service'
          },
          {
            model: Location,
            as: 'location'
          }
        ]
      });

      // Send SMS notification
      try {
        await smsService.sendServiceReady(calledQueue.appointment.user.phone, {
          queueNumber: calledQueue.queue_number,
          roomNumber: room_number,
          locationName: calledQueue.location.name
        });
      } catch (smsError) {
        console.error('SMS sending failed:', smsError);
      }

      res.json({
        success: true,
        message: 'Next person called successfully',
        data: { queue: calledQueue }
      });
    } catch (error) {
      console.error('Call next in queue error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to call next person'
      });
    }
  }
);

// Update queue status (admin only)
router.put('/:id/status',
  authenticateToken,
  validate(schemas.updateQueueStatus),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { status, room_number, notes } = req.body;

      const queue = await Queue.findByPk(id, {
        include: [
          {
            model: Location,
            as: 'location'
          },
          {
            model: Appointment,
            as: 'appointment',
            include: [
              {
                model: User,
                as: 'user'
              }
            ]
          }
        ]
      });

      if (!queue) {
        return res.status(404).json({
          success: false,
          message: 'Queue item not found'
        });
      }

      // Check authorization
      if (req.user.role === 'business_admin' &&
          queue.location.admin_user_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'You are not authorized to manage this queue'
        });
      }

      // Update queue
      const updateData = {
        status,
        served_by: req.user.id
      };

      if (room_number) updateData.room_number = room_number;
      if (notes) updateData.notes = notes;

      if (status === 'called') {
        updateData.called_at = new Date();
      } else if (status === 'in_service') {
        updateData.service_started_at = new Date();
      } else if (status === 'completed') {
        updateData.service_completed_at = new Date();
      } else if (status === 'skipped') {
        updateData.skipped_at = new Date();
        updateData.skip_reason = notes;
      }

      await queue.update(updateData);

      // Update corresponding appointment status
      let appointmentStatus = queue.appointment.status;
      if (status === 'in_service') appointmentStatus = 'in_progress';
      else if (status === 'completed') appointmentStatus = 'completed';
      else if (status === 'no_show') appointmentStatus = 'no_show';

      if (appointmentStatus !== queue.appointment.status) {
        await queue.appointment.update({ status: appointmentStatus });
      }

      res.json({
        success: true,
        message: 'Queue status updated successfully',
        data: { queue }
      });
    } catch (error) {
      console.error('Update queue status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update queue status'
      });
    }
  }
);

// Skip queue item (admin only)
router.put('/:id/skip', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const queue = await Queue.findByPk(id, {
      include: [
        {
          model: Location,
          as: 'location'
        }
      ]
    });

    if (!queue) {
      return res.status(404).json({
        success: false,
        message: 'Queue item not found'
      });
    }

    // Check authorization
    if (req.user.role === 'business_admin' &&
        queue.location.admin_user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to manage this queue'
      });
    }

    if (!queue.canBeSkipped()) {
      return res.status(400).json({
        success: false,
        message: 'Queue item cannot be skipped'
      });
    }

    await queue.update({
      status: 'skipped',
      skipped_at: new Date(),
      skip_reason: reason,
      served_by: req.user.id
    });

    res.json({
      success: true,
      message: 'Queue item skipped successfully'
    });
  } catch (error) {
    console.error('Skip queue error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to skip queue item'
    });
  }
});

// Send queue update notifications (admin only)
router.post('/location/:locationId/notify',
  authenticateToken,
  requireLocationAdmin,
  async (req, res) => {
    try {
      const { locationId } = req.params;
      const { positions_to_notify = 3 } = req.body;

      const today = new Date().toISOString().split('T')[0];

      // Get people in queue who should be notified
      const queueItems = await Queue.findAll({
        where: {
          location_id: locationId,
          queue_date: today,
          status: 'waiting',
          position: { [Op.lte]: positions_to_notify }
        },
        include: [
          {
            model: Appointment,
            as: 'appointment',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['phone']
              }
            ]
          },
          {
            model: Location,
            as: 'location',
            attributes: ['name']
          }
        ],
        order: [['position', 'ASC']]
      });

      const notifications = [];

      for (let queue of queueItems) {
        try {
          await smsService.sendQueueUpdate(queue.appointment.user.phone, {
            queueNumber: queue.queue_number,
            position: queue.position,
            estimatedWaitTime: queue.calculateWaitTime(),
            locationName: queue.location.name
          });

          await queue.update({
            notification_sent: true,
            last_notification_at: new Date(),
            notification_count: queue.notification_count + 1
          });

          notifications.push({
            queue_id: queue.id,
            phone: queue.appointment.user.phone,
            success: true
          });
        } catch (error) {
          notifications.push({
            queue_id: queue.id,
            phone: queue.appointment.user.phone,
            success: false,
            error: error.message
          });
        }
      }

      res.json({
        success: true,
        message: 'Queue notifications sent',
        data: { notifications }
      });
    } catch (error) {
      console.error('Send queue notifications error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send notifications'
      });
    }
  }
);

// Get queue statistics for location (admin only)
router.get('/location/:locationId/stats',
  authenticateToken,
  requireLocationAdmin,
  async (req, res) => {
    try {
      const { locationId } = req.params;
      const { date } = req.query;

      const queueDate = date || new Date().toISOString().split('T')[0];

      const stats = await Queue.findAll({
        where: {
          location_id: locationId,
          queue_date: queueDate
        },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('AVG', sequelize.col('estimated_wait_time')), 'avg_wait_time']
        ],
        group: ['status'],
        raw: true
      });

      const totalProcessed = await Queue.count({
        where: {
          location_id: locationId,
          queue_date: queueDate,
          status: ['completed', 'no_show', 'skipped']
        }
      });

      const averageServiceTime = await Queue.findOne({
        where: {
          location_id: locationId,
          queue_date: queueDate,
          status: 'completed',
          service_started_at: { [Op.not]: null },
          service_completed_at: { [Op.not]: null }
        },
        attributes: [
          [sequelize.fn('AVG',
            sequelize.literal('TIMESTAMPDIFF(MINUTE, service_started_at, service_completed_at)')
          ), 'avg_service_time']
        ],
        raw: true
      });

      res.json({
        success: true,
        data: {
          queue_date: queueDate,
          statistics: stats,
          total_processed: totalProcessed,
          average_service_time: averageServiceTime?.avg_service_time || 0
        }
      });
    } catch (error) {
      console.error('Get queue stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch queue statistics'
      });
    }
  }
);

module.exports = router;
