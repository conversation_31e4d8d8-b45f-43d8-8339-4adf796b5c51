const { DataTypes, Op } = require('sequelize');
const sequelize = require('../config/database');

const Queue = sequelize.define('Queue', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  queue_number: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  appointment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'appointments',
      key: 'id'
    }
  },
  location_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'locations',
      key: 'id'
    }
  },
  service_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  queue_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM(
      'waiting',
      'called',
      'in_service',
      'completed',
      'skipped',
      'no_show',
      'cancelled'
    ),
    defaultValue: 'waiting'
  },
  position: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  estimated_wait_time: {
    type: DataTypes.INTEGER, // in minutes
    allowNull: true
  },
  called_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  service_started_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  service_completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  skipped_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  skip_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  notification_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  last_notification_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notification_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  room_number: {
    type: DataTypes.STRING,
    allowNull: true
  },
  served_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'queues',
  indexes: [
    {
      fields: ['appointment_id']
    },
    {
      fields: ['location_id']
    },
    {
      fields: ['service_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['queue_date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['position']
    },
    {
      unique: true,
      fields: ['location_id', 'queue_date', 'queue_number']
    }
  ]
});

// Instance methods
Queue.prototype.calculateWaitTime = function() {
  // Calculate estimated wait time based on position and average service time
  const averageServiceTime = 30; // minutes - this could be dynamic
  return this.position * averageServiceTime;
};

Queue.prototype.canBeSkipped = function() {
  return ['waiting', 'called'].includes(this.status);
};

Queue.prototype.getWaitingTime = function() {
  if (this.service_started_at) {
    return Math.round((this.service_started_at - this.created_at) / (1000 * 60));
  }
  return Math.round((new Date() - this.created_at) / (1000 * 60));
};

Queue.prototype.getServiceDuration = function() {
  if (this.service_started_at && this.service_completed_at) {
    return Math.round((this.service_completed_at - this.service_started_at) / (1000 * 60));
  }
  return null;
};

// Class methods
Queue.getNextInLine = async function(locationId, serviceId = null) {
  const whereClause = {
    location_id: locationId,
    status: 'waiting',
    queue_date: new Date().toISOString().split('T')[0]
  };

  if (serviceId) {
    whereClause.service_id = serviceId;
  }

  return await this.findOne({
    where: whereClause,
    order: [['position', 'ASC']]
  });
};

Queue.getCurrentQueueLength = async function(locationId, serviceId = null) {
  const whereClause = {
    location_id: locationId,
    status: ['waiting', 'called', 'in_service'],
    queue_date: new Date().toISOString().split('T')[0]
  };

  if (serviceId) {
    whereClause.service_id = serviceId;
  }

  return await this.count({ where: whereClause });
};

Queue.getQueuePosition = async function(appointmentId) {
  const queueItem = await this.findOne({
    where: { appointment_id: appointmentId }
  });

  if (!queueItem) return null;

  const position = await this.count({
    where: {
      location_id: queueItem.location_id,
      queue_date: queueItem.queue_date,
      status: ['waiting', 'called', 'in_service'],
      position: { [sequelize.Op.lt]: queueItem.position }
    }
  });

  return position + 1;
};

module.exports = Queue;
