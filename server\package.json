{"name": "umurongo-server", "version": "1.0.0", "description": "Umurongo Queue & Appointment Management System Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test-setup": "node test-setup.js", "seed": "node seeders/initial-data.js", "migrate": "npx sequelize-cli db:migrate", "seed-cli": "npx sequelize-cli db:seed:all"}, "keywords": ["queue-management", "appointment-booking", "sms-notifications", "rwanda", "healthcare"], "author": "Umurongo Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "sequelize": "^6.35.0", "sqlite3": "^5.1.7", "twilio": "^4.19.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}