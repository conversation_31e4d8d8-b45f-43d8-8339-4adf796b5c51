{"name": "umurongo-server", "version": "1.0.0", "description": "Umurongo Queue & Appointment Management System Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test-setup": "node test-setup.js", "seed": "node seeders/initial-data.js", "migrate": "npx sequelize-cli db:migrate", "seed-cli": "npx sequelize-cli db:seed:all"}, "keywords": ["queue-management", "appointment-booking", "sms-notifications", "rwanda", "healthcare"], "author": "Umurongo Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.0", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "dotenv": "^16.3.1", "twilio": "^4.19.0", "moment": "^2.29.4", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=16.0.0"}}