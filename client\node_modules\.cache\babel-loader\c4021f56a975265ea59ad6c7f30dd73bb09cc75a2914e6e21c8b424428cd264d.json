{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInHour } from \"../constants/index.js\";\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param {number} milliseconds - number of milliseconds to be converted\n *\n * @returns {number} the number of milliseconds converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\nexport default function millisecondsToHours(milliseconds) {\n  requiredArgs(1, arguments);\n  var hours = milliseconds / millisecondsInHour;\n  return Math.floor(hours);\n}", "map": {"version": 3, "names": ["requiredArgs", "millisecondsInHour", "millisecondsToHours", "milliseconds", "arguments", "hours", "Math", "floor"], "sources": ["E:/CBS Pro/umurongo/client/node_modules/date-fns/esm/millisecondsToHours/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInHour } from \"../constants/index.js\";\n/**\n * @name millisecondsToHours\n * @category Conversion Helpers\n * @summary Convert milliseconds to hours.\n *\n * @description\n * Convert a number of milliseconds to a full number of hours.\n *\n * @param {number} milliseconds - number of milliseconds to be converted\n *\n * @returns {number} the number of milliseconds converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 7200000 milliseconds to hours:\n * const result = millisecondsToHours(7200000)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = millisecondsToHours(7199999)\n * //=> 1\n */\nexport default function millisecondsToHours(milliseconds) {\n  requiredArgs(1, arguments);\n  var hours = milliseconds / millisecondsInHour;\n  return Math.floor(hours);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAACC,YAAY,EAAE;EACxDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAGF,YAAY,GAAGF,kBAAkB;EAC7C,OAAOK,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}