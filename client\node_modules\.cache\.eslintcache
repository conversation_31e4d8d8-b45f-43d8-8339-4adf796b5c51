[{"E:\\CBS Pro\\app1\\client\\src\\index.js": "1", "E:\\CBS Pro\\app1\\client\\src\\App.js": "2", "E:\\CBS Pro\\app1\\client\\src\\reportWebVitals.js": "3", "E:\\CBS Pro\\umurongo\\client\\src\\index.js": "4", "E:\\CBS Pro\\umurongo\\client\\src\\App.js": "5", "E:\\CBS Pro\\umurongo\\client\\src\\reportWebVitals.js": "6", "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AppContext.js": "7", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LoginPage.js": "8", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\RegisterPage.js": "9", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AdminDashboard.js": "10", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LandingPage.js": "11", "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AuthContext.js": "12", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ServicesPage.js": "13", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LocationsPage.js": "14", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\DashboardPage.js": "15", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\QueuePage.js": "16", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AppointmentsPage.js": "17", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ProfilePage.js": "18", "E:\\CBS Pro\\umurongo\\client\\src\\components\\ProtectedRoute.js": "19", "E:\\CBS Pro\\umurongo\\client\\src\\components\\Layout.js": "20", "E:\\CBS Pro\\umurongo\\client\\src\\components\\LoadingSpinner.js": "21", "E:\\CBS Pro\\umurongo\\client\\src\\components\\DebugPanel.js": "22"}, {"size": 535, "mtime": 1743067831280, "results": "23", "hashOfConfig": "24"}, {"size": 3447, "mtime": 1746626607619, "results": "25", "hashOfConfig": "24"}, {"size": 362, "mtime": 1743067831532, "results": "26", "hashOfConfig": "24"}, {"size": 535, "mtime": 1743067831280, "results": "27", "hashOfConfig": "28"}, {"size": 3763, "mtime": 1748259236294, "results": "29", "hashOfConfig": "28"}, {"size": 362, "mtime": 1743067831532, "results": "30", "hashOfConfig": "28"}, {"size": 9104, "mtime": 1748261998419, "results": "31", "hashOfConfig": "28"}, {"size": 8924, "mtime": 1748259412624, "results": "32", "hashOfConfig": "28"}, {"size": 13240, "mtime": 1748259447805, "results": "33", "hashOfConfig": "28"}, {"size": 492, "mtime": 1748259529022, "results": "34", "hashOfConfig": "28"}, {"size": 10088, "mtime": 1748259380129, "results": "35", "hashOfConfig": "28"}, {"size": 4965, "mtime": 1748259263725, "results": "36", "hashOfConfig": "28"}, {"size": 17107, "mtime": 1748261064092, "results": "37", "hashOfConfig": "28"}, {"size": 12691, "mtime": 1748262069120, "results": "38", "hashOfConfig": "28"}, {"size": 9793, "mtime": 1748259480615, "results": "39", "hashOfConfig": "28"}, {"size": 468, "mtime": 1748259513172, "results": "40", "hashOfConfig": "28"}, {"size": 14829, "mtime": 1748261186000, "results": "41", "hashOfConfig": "28"}, {"size": 461, "mtime": 1748259521104, "results": "42", "hashOfConfig": "28"}, {"size": 951, "mtime": 1748259297516, "results": "43", "hashOfConfig": "28"}, {"size": 9149, "mtime": 1748260814447, "results": "44", "hashOfConfig": "28"}, {"size": 914, "mtime": 1748259306854, "results": "45", "hashOfConfig": "28"}, {"size": 5330, "mtime": 1748260743838, "results": "46", "hashOfConfig": "28"}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "138aj6v", {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "e6uvky", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\CBS Pro\\app1\\client\\src\\index.js", [], [], "E:\\CBS Pro\\app1\\client\\src\\App.js", [], [], "E:\\CBS Pro\\app1\\client\\src\\reportWebVitals.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\index.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\App.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\reportWebVitals.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AppContext.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LoginPage.js", ["113"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\RegisterPage.js", ["114", "115"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AdminDashboard.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LandingPage.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AuthContext.js", ["116"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ServicesPage.js", ["117", "118"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LocationsPage.js", ["119"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\DashboardPage.js", ["120", "121"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\QueuePage.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AppointmentsPage.js", ["122"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ProfilePage.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\ProtectedRoute.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\Layout.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\LoadingSpinner.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\DebugPanel.js", [], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 149, "column": 19, "nodeType": "125", "endLine": 149, "endColumn": 95}, {"ruleId": "123", "severity": 1, "message": "124", "line": 253, "column": 19, "nodeType": "125", "endLine": 253, "endColumn": 83}, {"ruleId": "123", "severity": 1, "message": "124", "line": 257, "column": 19, "nodeType": "125", "endLine": 257, "endColumn": 83}, {"ruleId": "126", "severity": 1, "message": "127", "line": 90, "column": 13, "nodeType": "128", "messageId": "129", "endLine": 90, "endColumn": 21}, {"ruleId": "126", "severity": 1, "message": "130", "line": 12, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 12, "endColumn": 11}, {"ruleId": "131", "severity": 1, "message": "132", "line": 43, "column": 6, "nodeType": "133", "endLine": 43, "endColumn": 8, "suggestions": "134"}, {"ruleId": "131", "severity": 1, "message": "135", "line": 22, "column": 6, "nodeType": "133", "endLine": 22, "endColumn": 8, "suggestions": "136"}, {"ruleId": "126", "severity": 1, "message": "137", "line": 10, "column": 3, "nodeType": "128", "messageId": "129", "endLine": 10, "endColumn": 15}, {"ruleId": "131", "severity": 1, "message": "138", "line": 39, "column": 6, "nodeType": "133", "endLine": 39, "endColumn": 8, "suggestions": "139"}, {"ruleId": "131", "severity": 1, "message": "140", "line": 30, "column": 6, "nodeType": "133", "endLine": 30, "endColumn": 8, "suggestions": "141"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'response' is assigned a value but never used.", "Identifier", "unusedVar", "'PlusIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchLocations' and 'fetchServices'. Either include them or remove the dependency array.", "ArrayExpression", ["142"], "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["143"], "'ChartBarIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAppointments', 'getDashboardStats', and 'getQueuePosition'. Either include them or remove the dependency array.", ["144"], "React Hook useEffect has a missing dependency: 'fetchAppointments'. Either include it or remove the dependency array.", ["145"], {"desc": "146", "fix": "147"}, {"desc": "148", "fix": "149"}, {"desc": "150", "fix": "151"}, {"desc": "152", "fix": "153"}, "Update the dependencies array to be: [fetchLocations, fetchServices]", {"range": "154", "text": "155"}, "Update the dependencies array to be: [fetchLocations]", {"range": "156", "text": "157"}, "Update the dependencies array to be: [fetchAppointments, getDashboardStats, getQueuePosition]", {"range": "158", "text": "159"}, "Update the dependencies array to be: [fetchAppointments]", {"range": "160", "text": "161"}, [1156, 1158], "[fetchLocations, fetchServices]", [690, 692], "[fetchLocations]", [947, 949], "[fetchAppointments, getDashboardStats, getQueuePosition]", [800, 802], "[fetchAppointments]"]