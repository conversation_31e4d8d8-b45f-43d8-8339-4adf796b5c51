[{"E:\\CBS Pro\\app1\\client\\src\\index.js": "1", "E:\\CBS Pro\\app1\\client\\src\\App.js": "2", "E:\\CBS Pro\\app1\\client\\src\\reportWebVitals.js": "3", "E:\\CBS Pro\\umurongo\\client\\src\\index.js": "4", "E:\\CBS Pro\\umurongo\\client\\src\\App.js": "5", "E:\\CBS Pro\\umurongo\\client\\src\\reportWebVitals.js": "6", "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AppContext.js": "7", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LoginPage.js": "8", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\RegisterPage.js": "9", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AdminDashboard.js": "10", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LandingPage.js": "11", "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AuthContext.js": "12", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ServicesPage.js": "13", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LocationsPage.js": "14", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\DashboardPage.js": "15", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\QueuePage.js": "16", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AppointmentsPage.js": "17", "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ProfilePage.js": "18", "E:\\CBS Pro\\umurongo\\client\\src\\components\\ProtectedRoute.js": "19", "E:\\CBS Pro\\umurongo\\client\\src\\components\\Layout.js": "20", "E:\\CBS Pro\\umurongo\\client\\src\\components\\LoadingSpinner.js": "21", "E:\\CBS Pro\\umurongo\\client\\src\\components\\DebugPanel.js": "22"}, {"size": 535, "mtime": 1743067831280, "results": "23", "hashOfConfig": "24"}, {"size": 3447, "mtime": 1746626607619, "results": "25", "hashOfConfig": "24"}, {"size": 362, "mtime": 1743067831532, "results": "26", "hashOfConfig": "24"}, {"size": 535, "mtime": 1743067831280, "results": "27", "hashOfConfig": "28"}, {"size": 3763, "mtime": 1748259236294, "results": "29", "hashOfConfig": "28"}, {"size": 362, "mtime": 1743067831532, "results": "30", "hashOfConfig": "28"}, {"size": 14115, "mtime": 1748264021315, "results": "31", "hashOfConfig": "28"}, {"size": 9450, "mtime": 1748266355143, "results": "32", "hashOfConfig": "28"}, {"size": 13240, "mtime": 1748259447805, "results": "33", "hashOfConfig": "28"}, {"size": 12449, "mtime": 1748263948833, "results": "34", "hashOfConfig": "28"}, {"size": 10088, "mtime": 1748259380129, "results": "35", "hashOfConfig": "28"}, {"size": 7274, "mtime": 1748266327699, "results": "36", "hashOfConfig": "28"}, {"size": 17107, "mtime": 1748261064092, "results": "37", "hashOfConfig": "28"}, {"size": 12691, "mtime": 1748262069120, "results": "38", "hashOfConfig": "28"}, {"size": 9793, "mtime": 1748259480615, "results": "39", "hashOfConfig": "28"}, {"size": 12649, "mtime": 1748264126621, "results": "40", "hashOfConfig": "28"}, {"size": 14829, "mtime": 1748261186000, "results": "41", "hashOfConfig": "28"}, {"size": 461, "mtime": 1748259521104, "results": "42", "hashOfConfig": "28"}, {"size": 951, "mtime": 1748259297516, "results": "43", "hashOfConfig": "28"}, {"size": 9149, "mtime": 1748260814447, "results": "44", "hashOfConfig": "28"}, {"size": 914, "mtime": 1748259306854, "results": "45", "hashOfConfig": "28"}, {"size": 5317, "mtime": 1748265433310, "results": "46", "hashOfConfig": "28"}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "138aj6v", {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "e6uvky", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\CBS Pro\\app1\\client\\src\\index.js", [], [], "E:\\CBS Pro\\app1\\client\\src\\App.js", [], [], "E:\\CBS Pro\\app1\\client\\src\\reportWebVitals.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\index.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\App.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\reportWebVitals.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AppContext.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LoginPage.js", ["113"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\RegisterPage.js", ["114", "115"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AdminDashboard.js", ["116"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LandingPage.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\contexts\\AuthContext.js", ["117"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ServicesPage.js", ["118", "119"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\LocationsPage.js", ["120"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\DashboardPage.js", ["121", "122"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\QueuePage.js", ["123", "124"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\AppointmentsPage.js", ["125"], [], "E:\\CBS Pro\\umurongo\\client\\src\\pages\\ProfilePage.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\ProtectedRoute.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\Layout.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\LoadingSpinner.js", [], [], "E:\\CBS Pro\\umurongo\\client\\src\\components\\DebugPanel.js", [], [], {"ruleId": "126", "severity": 1, "message": "127", "line": 162, "column": 19, "nodeType": "128", "endLine": 162, "endColumn": 95}, {"ruleId": "126", "severity": 1, "message": "127", "line": 253, "column": 19, "nodeType": "128", "endLine": 253, "endColumn": 83}, {"ruleId": "126", "severity": 1, "message": "127", "line": 257, "column": 19, "nodeType": "128", "endLine": 257, "endColumn": 83}, {"ruleId": "129", "severity": 1, "message": "130", "line": 91, "column": 9, "nodeType": "131", "messageId": "132", "endLine": 91, "endColumn": 19}, {"ruleId": "129", "severity": 1, "message": "133", "line": 154, "column": 13, "nodeType": "131", "messageId": "132", "endLine": 154, "endColumn": 21}, {"ruleId": "129", "severity": 1, "message": "134", "line": 12, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 12, "endColumn": 11}, {"ruleId": "135", "severity": 1, "message": "136", "line": 43, "column": 6, "nodeType": "137", "endLine": 43, "endColumn": 8, "suggestions": "138"}, {"ruleId": "135", "severity": 1, "message": "139", "line": 22, "column": 6, "nodeType": "137", "endLine": 22, "endColumn": 8, "suggestions": "140"}, {"ruleId": "129", "severity": 1, "message": "141", "line": 10, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 10, "endColumn": 15}, {"ruleId": "135", "severity": 1, "message": "142", "line": 39, "column": 6, "nodeType": "137", "endLine": 39, "endColumn": 8, "suggestions": "143"}, {"ruleId": "129", "severity": 1, "message": "144", "line": 13, "column": 8, "nodeType": "131", "messageId": "132", "endLine": 13, "endColumn": 13}, {"ruleId": "135", "severity": 1, "message": "145", "line": 32, "column": 6, "nodeType": "137", "endLine": 32, "endColumn": 8, "suggestions": "146"}, {"ruleId": "135", "severity": 1, "message": "147", "line": 30, "column": 6, "nodeType": "137", "endLine": 30, "endColumn": 8, "suggestions": "148"}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'handleSave' is assigned a value but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "'PlusIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchLocations' and 'fetchServices'. Either include them or remove the dependency array.", "ArrayExpression", ["149"], "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["150"], "'ChartBarIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAppointments', 'getDashboardStats', and 'getQueuePosition'. Either include them or remove the dependency array.", ["151"], "'toast' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkCurrentQueue' and 'fetchServices'. Either include them or remove the dependency array.", ["152"], "React Hook useEffect has a missing dependency: 'fetchAppointments'. Either include it or remove the dependency array.", ["153"], {"desc": "154", "fix": "155"}, {"desc": "156", "fix": "157"}, {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, "Update the dependencies array to be: [fetchLocations, fetchServices]", {"range": "164", "text": "165"}, "Update the dependencies array to be: [fetchLocations]", {"range": "166", "text": "167"}, "Update the dependencies array to be: [fetchAppointments, getDashboardStats, getQueuePosition]", {"range": "168", "text": "169"}, "Update the dependencies array to be: [checkCurrentQueue, fetchServices]", {"range": "170", "text": "171"}, "Update the dependencies array to be: [fetchAppointments]", {"range": "172", "text": "173"}, [1156, 1158], "[fetchLocations, fetchServices]", [690, 692], "[fetchLocations]", [947, 949], "[fetchAppointments, getDashboardStats, getQueuePosition]", [731, 733], "[checkCurrent<PERSON><PERSON>ue, fetchServices]", [800, 802], "[fetchAppointments]"]